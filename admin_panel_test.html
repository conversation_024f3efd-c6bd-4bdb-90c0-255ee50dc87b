<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Champions Sports Bar - Admin Panel Testing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #dc2626;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .section-header {
            background: #374151;
            color: white;
            padding: 15px;
            font-weight: bold;
            font-size: 18px;
        }
        .section-content {
            padding: 20px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .test-item {
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 5px;
            background: #f9fafb;
        }
        .test-item h3 {
            margin-top: 0;
            color: #374151;
            font-size: 16px;
        }
        .test-button {
            background: #dc2626;
            color: white;
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px 5px 5px 0;
            text-decoration: none;
            display: inline-block;
        }
        .test-button:hover {
            background: #b91c1c;
        }
        .test-button.secondary {
            background: #3b82f6;
        }
        .test-button.secondary:hover {
            background: #2563eb;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status.pass { background: #d1fae5; color: #065f46; }
        .status.fail { background: #fee2e2; color: #991b1b; }
        .status.pending { background: #fef3c7; color: #92400e; }
        .credentials-box {
            background: #eff6ff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #3b82f6;
        }
        .credentials-box h3 {
            margin-top: 0;
            color: #1f2937;
        }
        .test-checklist {
            background: #f0fdf4;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            border-left: 4px solid #10b981;
        }
        .test-checklist h4 {
            margin-top: 0;
            color: #065f46;
        }
        .test-checklist ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .test-checklist li {
            margin: 5px 0;
        }
        .iframe-container {
            border: 2px solid #d1d5db;
            border-radius: 8px;
            overflow: hidden;
            margin-top: 15px;
        }
        .iframe-container iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
        .test-results {
            background: #fefce8;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            border-left: 4px solid #eab308;
        }
        .test-results h3 {
            margin-top: 0;
            color: #92400e;
        }
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            .test-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏆 Champions Sports Bar - Admin Panel Testing</h1>
        
        <div class="credentials-box">
            <h3>🔐 Admin Login Credentials</h3>
            <p><strong>URL:</strong> <a href="/admin/login.php" target="_blank">http://localhost:8000/admin/login.php</a></p>
            <p><strong>Username:</strong> admin</p>
            <p><strong>Password:</strong> admin123</p>
            <p><em>Use these credentials to log into the admin panel for testing.</em></p>
        </div>

        <!-- Authentication Testing -->
        <div class="test-section">
            <div class="section-header">🔐 Authentication & Security Testing</div>
            <div class="section-content">
                <div class="test-grid">
                    <div class="test-item">
                        <h3>Login System</h3>
                        <p>Test admin login functionality and session management.</p>
                        <a href="/admin/login.php" target="_blank" class="test-button">Test Login Page</a>
                        <a href="/admin/dashboard.php" target="_blank" class="test-button secondary">Test Dashboard Access</a>
                        <div class="test-checklist">
                            <h4>Test Checklist:</h4>
                            <ul>
                                <li>Login form displays correctly</li>
                                <li>Valid credentials allow access</li>
                                <li>Invalid credentials are rejected</li>
                                <li>Session persists across pages</li>
                                <li>Logout functionality works</li>
                            </ul>
                        </div>
                    </div>

                    <div class="test-item">
                        <h3>Access Control</h3>
                        <p>Verify protected pages require authentication.</p>
                        <a href="/admin/dashboard.php" target="_blank" class="test-button">Test Dashboard</a>
                        <a href="/admin/content.php" target="_blank" class="test-button">Test Content Page</a>
                        <div class="test-checklist">
                            <h4>Test Checklist:</h4>
                            <ul>
                                <li>Unauthenticated users redirected to login</li>
                                <li>Authenticated users can access all pages</li>
                                <li>Session timeout works properly</li>
                                <li>Direct URL access is protected</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Testing -->
        <div class="test-section">
            <div class="section-header">📊 Dashboard Testing</div>
            <div class="section-content">
                <div class="test-grid">
                    <div class="test-item">
                        <h3>Dashboard Overview</h3>
                        <p>Test dashboard statistics and quick actions.</p>
                        <a href="/admin/dashboard.php" target="_blank" class="test-button">Open Dashboard</a>
                        <div class="test-checklist">
                            <h4>Test Checklist:</h4>
                            <ul>
                                <li>Statistics display correctly</li>
                                <li>Quick action buttons work</li>
                                <li>Recent activity shows</li>
                                <li>Navigation menu functions</li>
                                <li>User info displays correctly</li>
                            </ul>
                        </div>
                    </div>

                    <div class="test-item">
                        <h3>Navigation</h3>
                        <p>Test admin panel navigation and menu system.</p>
                        <a href="/admin/dashboard.php" target="_blank" class="test-button">Test Navigation</a>
                        <div class="test-checklist">
                            <h4>Test Checklist:</h4>
                            <ul>
                                <li>All menu items are clickable</li>
                                <li>Active page is highlighted</li>
                                <li>Dropdown menus work</li>
                                <li>Mobile menu functions</li>
                                <li>Breadcrumbs display correctly</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Management Testing -->
        <div class="test-section">
            <div class="section-header">📝 Content Management Testing</div>
            <div class="section-content">
                <div class="test-grid">
                    <div class="test-item">
                        <h3>Page Content</h3>
                        <p>Test content editing and management features.</p>
                        <a href="/admin/content.php" target="_blank" class="test-button">Test Content Management</a>
                        <div class="test-checklist">
                            <h4>Test Checklist:</h4>
                            <ul>
                                <li>Content editor loads properly</li>
                                <li>Text editing works</li>
                                <li>Changes save correctly</li>
                                <li>Preview functionality works</li>
                                <li>All pages are editable</li>
                            </ul>
                        </div>
                    </div>

                    <div class="test-item">
                        <h3>Hero Banners</h3>
                        <p>Test hero banner management system.</p>
                        <a href="/admin/hero-banners.php" target="_blank" class="test-button">Test Hero Banners</a>
                        <div class="test-checklist">
                            <h4>Test Checklist:</h4>
                            <ul>
                                <li>Banner list displays</li>
                                <li>Add new banner works</li>
                                <li>Edit banner functionality</li>
                                <li>Delete banner works</li>
                                <li>Image upload functions</li>
                            </ul>
                        </div>
                    </div>

                    <div class="test-item">
                        <h3>Site Settings</h3>
                        <p>Test site configuration and settings.</p>
                        <a href="/admin/settings.php" target="_blank" class="test-button">Test Settings</a>
                        <div class="test-checklist">
                            <h4>Test Checklist:</h4>
                            <ul>
                                <li>Settings form loads</li>
                                <li>All fields are editable</li>
                                <li>Settings save properly</li>
                                <li>Validation works</li>
                                <li>Changes reflect on site</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Events Management Testing -->
        <div class="test-section">
            <div class="section-header">🎉 Events Management Testing</div>
            <div class="section-content">
                <div class="test-grid">
                    <div class="test-item">
                        <h3>Events CRUD</h3>
                        <p>Test event creation, editing, and deletion.</p>
                        <a href="/admin/events.php" target="_blank" class="test-button">Test Events</a>
                        <div class="test-checklist">
                            <h4>Test Checklist:</h4>
                            <ul>
                                <li>Events list displays</li>
                                <li>Add new event works</li>
                                <li>Edit event functionality</li>
                                <li>Delete event works</li>
                                <li>Event images upload</li>
                                <li>Date/time picker works</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Jobs Management Testing -->
        <div class="test-section">
            <div class="section-header">💼 Jobs Management Testing</div>
            <div class="section-content">
                <div class="test-grid">
                    <div class="test-item">
                        <h3>Job Postings</h3>
                        <p>Test job posting management system.</p>
                        <a href="/admin/jobs.php" target="_blank" class="test-button">Test Jobs</a>
                        <div class="test-checklist">
                            <h4>Test Checklist:</h4>
                            <ul>
                                <li>Job listings display</li>
                                <li>Add new job works</li>
                                <li>Edit job functionality</li>
                                <li>Delete job works</li>
                                <li>Job status management</li>
                                <li>Application viewing</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gallery Management Testing -->
        <div class="test-section">
            <div class="section-header">🖼️ Gallery Management Testing</div>
            <div class="section-content">
                <div class="test-grid">
                    <div class="test-item">
                        <h3>Image Gallery</h3>
                        <p>Test gallery image management system.</p>
                        <a href="/admin/gallery.php" target="_blank" class="test-button">Test Gallery</a>
                        <div class="test-checklist">
                            <h4>Test Checklist:</h4>
                            <ul>
                                <li>Gallery images display</li>
                                <li>Image upload works</li>
                                <li>Image categorization</li>
                                <li>Image editing/deletion</li>
                                <li>Bulk operations work</li>
                                <li>Image optimization</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Menu Management Testing -->
        <div class="test-section">
            <div class="section-header">🍽️ Menu Management Testing</div>
            <div class="section-content">
                <div class="test-grid">
                    <div class="test-item">
                        <h3>Menu Categories</h3>
                        <p>Test menu category management.</p>
                        <a href="/admin/menu-categories.php" target="_blank" class="test-button">Test Categories</a>
                        <div class="test-checklist">
                            <h4>Test Checklist:</h4>
                            <ul>
                                <li>Categories list displays</li>
                                <li>Add category works</li>
                                <li>Edit category functionality</li>
                                <li>Delete category works</li>
                                <li>Category ordering</li>
                            </ul>
                        </div>
                    </div>

                    <div class="test-item">
                        <h3>Menu Items</h3>
                        <p>Test menu item management system.</p>
                        <a href="/admin/menu-items.php" target="_blank" class="test-button">Test Menu Items</a>
                        <div class="test-checklist">
                            <h4>Test Checklist:</h4>
                            <ul>
                                <li>Menu items display</li>
                                <li>Add item works</li>
                                <li>Edit item functionality</li>
                                <li>Delete item works</li>
                                <li>Price formatting</li>
                                <li>Item images upload</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results Summary -->
        <div class="test-results">
            <h3>📋 Testing Summary</h3>
            <p>Use the links above to systematically test each admin panel feature. Check off items in the checklists as you verify they work correctly.</p>
            <p><strong>Key Areas to Focus On:</strong></p>
            <ul>
                <li>All CRUD operations (Create, Read, Update, Delete)</li>
                <li>Form validation and error handling</li>
                <li>File upload functionality</li>
                <li>Session management and security</li>
                <li>User interface responsiveness</li>
                <li>Data persistence and integrity</li>
            </ul>
        </div>

        <!-- Quick Admin Panel Access -->
        <div class="iframe-container">
            <iframe src="/admin/login.php" title="Admin Panel"></iframe>
        </div>
    </div>

    <script>
        // Add click tracking for test buttons
        document.addEventListener('DOMContentLoaded', function() {
            const testButtons = document.querySelectorAll('.test-button');
            testButtons.forEach(button => {
                button.addEventListener('click', function() {
                    console.log('Testing:', this.textContent, 'URL:', this.href);
                });
            });
        });

        // Auto-refresh iframe every 30 seconds to catch updates
        setInterval(function() {
            const iframe = document.querySelector('iframe');
            if (iframe && iframe.src.includes('login.php')) {
                // Only refresh if still on login page
                iframe.src = iframe.src;
            }
        }, 30000);
    </script>
</body>
</html>
