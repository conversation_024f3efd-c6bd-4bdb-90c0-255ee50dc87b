<?php
// Champions Sports Bar - Careers Page
$page_title = "Careers - Join Our Team at Champions Sports Bar & Grill";
$page_description = "Join the Champions Sports Bar & Grill team in Brownstown, MI. We're hiring servers, bartenders, cooks, and more. Apply today!";
$current_page = "careers";
include '../includes/header.php';
?>

<!-- <PERSON> Header -->
<section class="page-header bg-dark text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="display-4 fw-bold mb-3">Join Our Team</h1>
                <p class="lead">Build your career with Champions Sports Bar & Grill</p>
            </div>
        </div>
    </div>
</section>

<!-- Why Work With Us -->
<section class="why-work-with-us py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="display-5 fw-bold">Why Work at Champions?</h2>
                <p class="lead">We're more than just a workplace - we're a team, a family</p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="benefit-card text-center p-4">
                    <div class="benefit-icon mb-3">
                        <i class="fas fa-users fa-3x text-primary"></i>
                    </div>
                    <h4>Great Team Environment</h4>
                    <p>Work with a supportive team that values collaboration and mutual respect.</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="benefit-card text-center p-4">
                    <div class="benefit-icon mb-3">
                        <i class="fas fa-chart-line fa-3x text-primary"></i>
                    </div>
                    <h4>Growth Opportunities</h4>
                    <p>Advance your career with training programs and promotion opportunities.</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="benefit-card text-center p-4">
                    <div class="benefit-icon mb-3">
                        <i class="fas fa-dollar-sign fa-3x text-primary"></i>
                    </div>
                    <h4>Competitive Pay</h4>
                    <p>Earn competitive wages plus tips in a high-volume, popular establishment.</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="benefit-card text-center p-4">
                    <div class="benefit-icon mb-3">
                        <i class="fas fa-clock fa-3x text-primary"></i>
                    </div>
                    <h4>Flexible Scheduling</h4>
                    <p>We work with your schedule and offer both full-time and part-time positions.</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="benefit-card text-center p-4">
                    <div class="benefit-icon mb-3">
                        <i class="fas fa-graduation-cap fa-3x text-primary"></i>
                    </div>
                    <h4>Training & Development</h4>
                    <p>Comprehensive training programs to help you succeed in your role.</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="benefit-card text-center p-4">
                    <div class="benefit-icon mb-3">
                        <i class="fas fa-heart fa-3x text-primary"></i>
                    </div>
                    <h4>Employee Benefits</h4>
                    <p>Health benefits, employee discounts, and paid time off for eligible employees.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Current Openings -->
<section class="current-openings py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="display-5 fw-bold">Current Job Openings</h2>
                <p class="lead">Find the perfect position for you</p>
            </div>
        </div>
        
        <div class="row g-4">
            <!-- Server Position -->
            <div class="col-lg-6">
                <div class="job-card p-4">
                    <div class="job-header d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h4 class="job-title">Server</h4>
                            <span class="badge bg-success">Full-Time / Part-Time</span>
                        </div>
                        <div class="job-salary text-end">
                            <strong>$15-25/hr</strong>
                            <small class="d-block text-muted">+ Tips</small>
                        </div>
                    </div>
                    <p class="job-description">
                        We're looking for friendly, energetic servers to join our team. 
                        Experience preferred but will train the right candidate.
                    </p>
                    <ul class="job-requirements mb-3">
                        <li>Excellent customer service skills</li>
                        <li>Ability to work in fast-paced environment</li>
                        <li>Must be 18+ years old</li>
                        <li>Weekend availability required</li>
                    </ul>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#applicationModal" 
                            data-position="Server">Apply Now</button>
                </div>
            </div>
            
            <!-- Bartender Position -->
            <div class="col-lg-6">
                <div class="job-card p-4">
                    <div class="job-header d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h4 class="job-title">Bartender</h4>
                            <span class="badge bg-success">Full-Time / Part-Time</span>
                        </div>
                        <div class="job-salary text-end">
                            <strong>$18-30/hr</strong>
                            <small class="d-block text-muted">+ Tips</small>
                        </div>
                    </div>
                    <p class="job-description">
                        Experienced bartender needed for busy sports bar. Must be knowledgeable 
                        about cocktails, beer, and wine service.
                    </p>
                    <ul class="job-requirements mb-3">
                        <li>2+ years bartending experience</li>
                        <li>Knowledge of cocktails and beer</li>
                        <li>Must be 21+ years old</li>
                        <li>TIPS certification preferred</li>
                    </ul>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#applicationModal" 
                            data-position="Bartender">Apply Now</button>
                </div>
            </div>
            
            <!-- Cook Position -->
            <div class="col-lg-6">
                <div class="job-card p-4">
                    <div class="job-header d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h4 class="job-title">Line Cook</h4>
                            <span class="badge bg-warning text-dark">Full-Time</span>
                        </div>
                        <div class="job-salary text-end">
                            <strong>$16-20/hr</strong>
                            <small class="d-block text-muted">Based on experience</small>
                        </div>
                    </div>
                    <p class="job-description">
                        Join our kitchen team! We need experienced line cooks who can handle 
                        high-volume service and maintain quality standards.
                    </p>
                    <ul class="job-requirements mb-3">
                        <li>1+ years kitchen experience</li>
                        <li>Ability to work under pressure</li>
                        <li>Food safety knowledge</li>
                        <li>Reliable and punctual</li>
                    </ul>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#applicationModal" 
                            data-position="Line Cook">Apply Now</button>
                </div>
            </div>
            
            <!-- Host Position -->
            <div class="col-lg-6">
                <div class="job-card p-4">
                    <div class="job-header d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h4 class="job-title">Host/Hostess</h4>
                            <span class="badge bg-info">Part-Time</span>
                        </div>
                        <div class="job-salary text-end">
                            <strong>$14-16/hr</strong>
                            <small class="d-block text-muted">Plus tip share</small>
                        </div>
                    </div>
                    <p class="job-description">
                        First impression matters! We need a friendly host to greet guests 
                        and manage seating in our busy restaurant.
                    </p>
                    <ul class="job-requirements mb-3">
                        <li>Excellent communication skills</li>
                        <li>Professional appearance</li>
                        <li>Must be 16+ years old</li>
                        <li>Evening and weekend availability</li>
                    </ul>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#applicationModal" 
                            data-position="Host/Hostess">Apply Now</button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Application Process -->
<section class="application-process py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="display-5 fw-bold">Application Process</h2>
                <p class="lead">Simple steps to join our team</p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-lg-3 col-md-6 text-center">
                <div class="process-step">
                    <div class="step-number">1</div>
                    <h5>Submit Application</h5>
                    <p>Fill out our online application form with your information and experience.</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 text-center">
                <div class="process-step">
                    <div class="step-number">2</div>
                    <h5>Phone Screening</h5>
                    <p>We'll call qualified candidates for a brief phone interview.</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 text-center">
                <div class="process-step">
                    <div class="step-number">3</div>
                    <h5>In-Person Interview</h5>
                    <p>Meet with our management team at the restaurant.</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 text-center">
                <div class="process-step">
                    <div class="step-number">4</div>
                    <h5>Start Working</h5>
                    <p>Complete orientation and training, then start your new career!</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Application Modal -->
<div class="modal fade" id="applicationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Job Application</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="applicationForm">
                    <input type="hidden" id="position" name="position">
                    
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="firstName" class="form-label">First Name *</label>
                            <input type="text" class="form-control" id="firstName" name="firstName" required>
                        </div>
                        <div class="col-md-6">
                            <label for="lastName" class="form-label">Last Name *</label>
                            <input type="text" class="form-control" id="lastName" name="lastName" required>
                        </div>
                        <div class="col-md-6">
                            <label for="email" class="form-label">Email *</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="col-md-6">
                            <label for="phone" class="form-label">Phone *</label>
                            <input type="tel" class="form-control" id="phone" name="phone" required>
                        </div>
                        <div class="col-12">
                            <label for="address" class="form-label">Address</label>
                            <input type="text" class="form-control" id="address" name="address">
                        </div>
                        <div class="col-md-4">
                            <label for="city" class="form-label">City</label>
                            <input type="text" class="form-control" id="city" name="city">
                        </div>
                        <div class="col-md-4">
                            <label for="state" class="form-label">State</label>
                            <input type="text" class="form-control" id="state" name="state">
                        </div>
                        <div class="col-md-4">
                            <label for="zipCode" class="form-label">Zip Code</label>
                            <input type="text" class="form-control" id="zipCode" name="zipCode">
                        </div>
                        <div class="col-md-6">
                            <label for="availability" class="form-label">Availability</label>
                            <select class="form-control" id="availability" name="availability">
                                <option value="">Select availability</option>
                                <option value="full-time">Full-time</option>
                                <option value="part-time">Part-time</option>
                                <option value="weekends">Weekends only</option>
                                <option value="evenings">Evenings only</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="experience" class="form-label">Years of Experience</label>
                            <select class="form-control" id="experience" name="experience">
                                <option value="">Select experience</option>
                                <option value="0">No experience</option>
                                <option value="1">Less than 1 year</option>
                                <option value="2">1-2 years</option>
                                <option value="3">3-5 years</option>
                                <option value="5+">5+ years</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label for="previousWork" class="form-label">Previous Work Experience</label>
                            <textarea class="form-control" id="previousWork" name="previousWork" rows="3" 
                                      placeholder="Tell us about your previous work experience..."></textarea>
                        </div>
                        <div class="col-12">
                            <label for="whyWork" class="form-label">Why do you want to work at Champions?</label>
                            <textarea class="form-control" id="whyWork" name="whyWork" rows="3" 
                                      placeholder="Tell us why you'd like to join our team..."></textarea>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="authorize" name="authorize" required>
                                <label class="form-check-label" for="authorize">
                                    I authorize Champions Sports Bar & Grill to contact my previous employers and references.
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="applicationForm" class="btn btn-primary">Submit Application</button>
            </div>
        </div>
    </div>
</div>

<style>
.benefit-card {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
    height: 100%;
}

.benefit-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.job-card {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
    height: 100%;
}

.job-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.job-requirements {
    list-style: none;
    padding-left: 0;
}

.job-requirements li {
    padding: 0.25rem 0;
    position: relative;
    padding-left: 1.5rem;
}

.job-requirements li:before {
    content: "✓";
    color: var(--success);
    font-weight: bold;
    position: absolute;
    left: 0;
}

.process-step {
    padding: 2rem 1rem;
}

.step-number {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0 auto 1rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle application modal
    const applicationModal = document.getElementById('applicationModal');
    const positionInput = document.getElementById('position');
    
    applicationModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const position = button.getAttribute('data-position');
        positionInput.value = position;
        
        const modalTitle = applicationModal.querySelector('.modal-title');
        modalTitle.textContent = `Apply for ${position} Position`;
    });
    
    // Handle form submission
    const applicationForm = document.getElementById('applicationForm');
    applicationForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<span class="loading"></span> Submitting...';
        submitBtn.disabled = true;
        
        // Simulate form submission
        setTimeout(() => {
            // Reset form and close modal
            this.reset();
            const modal = bootstrap.Modal.getInstance(applicationModal);
            modal.hide();
            
            // Show success message
            alert('Thank you for your application! We will review it and contact you within 2-3 business days.');
            
            // Reset button
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 2000);
    });
});
</script>

<?php include '../includes/footer.php'; ?>
