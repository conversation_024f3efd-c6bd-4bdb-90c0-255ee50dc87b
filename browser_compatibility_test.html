<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Champions Sports Bar - Browser Compatibility Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #dc2626;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h2 {
            color: #1f2937;
            margin-top: 0;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .test-item {
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 5px;
            background: #f9fafb;
        }
        .test-item h3 {
            margin-top: 0;
            color: #374151;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.pass { background: #d1fae5; color: #065f46; }
        .status.fail { background: #fee2e2; color: #991b1b; }
        .status.pending { background: #fef3c7; color: #92400e; }
        .browser-info {
            background: #eff6ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .feature-test {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #3b82f6;
            background: #f8fafc;
        }
        .responsive-test {
            border: 2px solid #10b981;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        @media (max-width: 768px) {
            .test-grid {
                grid-template-columns: 1fr;
            }
            .container {
                margin: 10px;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏆 Champions Sports Bar - Browser Compatibility Test</h1>
        
        <div class="browser-info">
            <h3>Current Browser Information</h3>
            <p><strong>User Agent:</strong> <span id="userAgent"></span></p>
            <p><strong>Screen Resolution:</strong> <span id="screenRes"></span></p>
            <p><strong>Viewport Size:</strong> <span id="viewportSize"></span></p>
            <p><strong>Color Depth:</strong> <span id="colorDepth"></span></p>
        </div>

        <div class="test-section">
            <h2>🌐 Browser Feature Support Tests</h2>
            <div class="test-grid">
                <div class="test-item">
                    <h3>CSS Features</h3>
                    <div class="feature-test">
                        <strong>CSS Grid:</strong> <span id="cssGrid" class="status pending">Testing...</span>
                    </div>
                    <div class="feature-test">
                        <strong>CSS Flexbox:</strong> <span id="cssFlexbox" class="status pending">Testing...</span>
                    </div>
                    <div class="feature-test">
                        <strong>CSS Transforms:</strong> <span id="cssTransforms" class="status pending">Testing...</span>
                    </div>
                    <div class="feature-test">
                        <strong>CSS Transitions:</strong> <span id="cssTransitions" class="status pending">Testing...</span>
                    </div>
                </div>

                <div class="test-item">
                    <h3>JavaScript Features</h3>
                    <div class="feature-test">
                        <strong>ES6 Support:</strong> <span id="es6Support" class="status pending">Testing...</span>
                    </div>
                    <div class="feature-test">
                        <strong>Local Storage:</strong> <span id="localStorage" class="status pending">Testing...</span>
                    </div>
                    <div class="feature-test">
                        <strong>Fetch API:</strong> <span id="fetchAPI" class="status pending">Testing...</span>
                    </div>
                    <div class="feature-test">
                        <strong>Promise Support:</strong> <span id="promiseSupport" class="status pending">Testing...</span>
                    </div>
                </div>

                <div class="test-item">
                    <h3>HTML5 Features</h3>
                    <div class="feature-test">
                        <strong>Canvas:</strong> <span id="canvas" class="status pending">Testing...</span>
                    </div>
                    <div class="feature-test">
                        <strong>Video:</strong> <span id="video" class="status pending">Testing...</span>
                    </div>
                    <div class="feature-test">
                        <strong>Geolocation:</strong> <span id="geolocation" class="status pending">Testing...</span>
                    </div>
                    <div class="feature-test">
                        <strong>Web Workers:</strong> <span id="webWorkers" class="status pending">Testing...</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>📱 Responsive Design Tests</h2>
            <div class="responsive-test">
                <h3>Current Viewport Test</h3>
                <p>This section tests how the website responds to different screen sizes.</p>
                <div id="responsiveResults"></div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔗 Website Page Tests</h2>
            <div class="test-grid">
                <div class="test-item">
                    <h3>Public Pages</h3>
                    <div class="feature-test">
                        <strong>Homepage:</strong> <span id="homePage" class="status pending">Testing...</span>
                        <button onclick="testPage('/')">Test</button>
                    </div>
                    <div class="feature-test">
                        <strong>Menu Page:</strong> <span id="menuPage" class="status pending">Testing...</span>
                        <button onclick="testPage('/menu/')">Test</button>
                    </div>
                    <div class="feature-test">
                        <strong>Gallery Page:</strong> <span id="galleryPage" class="status pending">Testing...</span>
                        <button onclick="testPage('/gallery/')">Test</button>
                    </div>
                    <div class="feature-test">
                        <strong>Events Page:</strong> <span id="eventsPage" class="status pending">Testing...</span>
                        <button onclick="testPage('/events/')">Test</button>
                    </div>
                    <div class="feature-test">
                        <strong>Careers Page:</strong> <span id="careersPage" class="status pending">Testing...</span>
                        <button onclick="testPage('/careers/')">Test</button>
                    </div>
                    <div class="feature-test">
                        <strong>Contact Page:</strong> <span id="contactPage" class="status pending">Testing...</span>
                        <button onclick="testPage('/contact/')">Test</button>
                    </div>
                </div>

                <div class="test-item">
                    <h3>Admin Pages</h3>
                    <div class="feature-test">
                        <strong>Admin Login:</strong> <span id="adminLogin" class="status pending">Testing...</span>
                        <button onclick="testPage('/admin/login.php')">Test</button>
                    </div>
                    <div class="feature-test">
                        <strong>Admin Dashboard:</strong> <span id="adminDashboard" class="status pending">Testing...</span>
                        <button onclick="testPage('/admin/dashboard.php')">Test</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 Test Summary</h2>
            <div id="testSummary">
                <p>Run the tests above to see the summary.</p>
            </div>
        </div>
    </div>

    <script>
        // Display browser information
        document.getElementById('userAgent').textContent = navigator.userAgent;
        document.getElementById('screenRes').textContent = screen.width + 'x' + screen.height;
        document.getElementById('viewportSize').textContent = window.innerWidth + 'x' + window.innerHeight;
        document.getElementById('colorDepth').textContent = screen.colorDepth + ' bits';

        // Test results storage
        const testResults = {};

        // Helper function to update test status
        function updateTestStatus(testId, passed, message = '') {
            const element = document.getElementById(testId);
            if (element) {
                element.className = 'status ' + (passed ? 'pass' : 'fail');
                element.textContent = passed ? 'PASS' : 'FAIL';
                if (message) {
                    element.title = message;
                }
                testResults[testId] = { passed, message };
            }
        }

        // CSS Feature Tests
        function testCSSFeatures() {
            // Test CSS Grid
            const gridTest = document.createElement('div');
            gridTest.style.display = 'grid';
            updateTestStatus('cssGrid', gridTest.style.display === 'grid');

            // Test CSS Flexbox
            const flexTest = document.createElement('div');
            flexTest.style.display = 'flex';
            updateTestStatus('cssFlexbox', flexTest.style.display === 'flex');

            // Test CSS Transforms
            const transformTest = document.createElement('div');
            transformTest.style.transform = 'translateX(10px)';
            updateTestStatus('cssTransforms', transformTest.style.transform !== '');

            // Test CSS Transitions
            const transitionTest = document.createElement('div');
            transitionTest.style.transition = 'all 0.3s ease';
            updateTestStatus('cssTransitions', transitionTest.style.transition !== '');
        }

        // JavaScript Feature Tests
        function testJSFeatures() {
            // Test ES6 Support (arrow functions, const/let)
            try {
                eval('const test = () => true;');
                updateTestStatus('es6Support', true);
            } catch (e) {
                updateTestStatus('es6Support', false, e.message);
            }

            // Test Local Storage
            try {
                localStorage.setItem('test', 'value');
                const value = localStorage.getItem('test');
                localStorage.removeItem('test');
                updateTestStatus('localStorage', value === 'value');
            } catch (e) {
                updateTestStatus('localStorage', false, e.message);
            }

            // Test Fetch API
            updateTestStatus('fetchAPI', typeof fetch === 'function');

            // Test Promise Support
            updateTestStatus('promiseSupport', typeof Promise === 'function');
        }

        // HTML5 Feature Tests
        function testHTML5Features() {
            // Test Canvas
            const canvas = document.createElement('canvas');
            updateTestStatus('canvas', !!(canvas.getContext && canvas.getContext('2d')));

            // Test Video
            const video = document.createElement('video');
            updateTestStatus('video', !!video.canPlayType);

            // Test Geolocation
            updateTestStatus('geolocation', !!navigator.geolocation);

            // Test Web Workers
            updateTestStatus('webWorkers', typeof Worker !== 'undefined');
        }

        // Responsive Design Tests
        function testResponsiveDesign() {
            const width = window.innerWidth;
            let deviceType = '';
            
            if (width < 576) {
                deviceType = 'Mobile (< 576px)';
            } else if (width < 768) {
                deviceType = 'Mobile Large (576px - 767px)';
            } else if (width < 992) {
                deviceType = 'Tablet (768px - 991px)';
            } else if (width < 1200) {
                deviceType = 'Desktop (992px - 1199px)';
            } else {
                deviceType = 'Large Desktop (≥ 1200px)';
            }

            document.getElementById('responsiveResults').innerHTML = `
                <p><strong>Current Device Type:</strong> ${deviceType}</p>
                <p><strong>Viewport Width:</strong> ${width}px</p>
                <p><strong>Viewport Height:</strong> ${window.innerHeight}px</p>
                <p><strong>Device Pixel Ratio:</strong> ${window.devicePixelRatio || 1}</p>
            `;
        }

        // Test individual pages
        async function testPage(url) {
            const pageId = url.replace(/[^a-zA-Z]/g, '') + 'Page';
            const element = document.getElementById(pageId);
            
            if (!element) return;
            
            element.textContent = 'Testing...';
            element.className = 'status pending';
            
            try {
                const response = await fetch(url, { method: 'HEAD' });
                const passed = response.ok;
                updateTestStatus(pageId, passed, `Status: ${response.status}`);
            } catch (error) {
                updateTestStatus(pageId, false, error.message);
            }
        }

        // Update test summary
        function updateTestSummary() {
            const total = Object.keys(testResults).length;
            const passed = Object.values(testResults).filter(r => r.passed).length;
            const failed = total - passed;
            
            document.getElementById('testSummary').innerHTML = `
                <h3>Test Results Summary</h3>
                <p><strong>Total Tests:</strong> ${total}</p>
                <p><strong>Passed:</strong> <span style="color: #065f46;">${passed}</span></p>
                <p><strong>Failed:</strong> <span style="color: #991b1b;">${failed}</span></p>
                <p><strong>Success Rate:</strong> ${total > 0 ? Math.round((passed / total) * 100) : 0}%</p>
            `;
        }

        // Run all tests on page load
        window.addEventListener('load', function() {
            testCSSFeatures();
            testJSFeatures();
            testHTML5Features();
            testResponsiveDesign();
            
            // Update summary after a short delay
            setTimeout(updateTestSummary, 1000);
        });

        // Update responsive design info on window resize
        window.addEventListener('resize', testResponsiveDesign);
    </script>
</body>
</html>
