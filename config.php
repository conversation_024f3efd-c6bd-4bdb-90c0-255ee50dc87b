<?php
/**
 * Champions Sports Bar & Grill - Main Website Configuration
 * 
 * This file contains all configuration settings for the main website.
 * It integrates with the admin panel database for dynamic settings.
 */

// Prevent direct access
if (!defined('CHAMPIONS_CONFIG_LOADED')) {
    define('CHAMPIONS_CONFIG_LOADED', true);
}

// Error reporting and logging
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors to users in production
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/logs/error.log');

// Create logs directory if it doesn't exist
if (!file_exists(__DIR__ . '/logs')) {
    mkdir(__DIR__ . '/logs', 0755, true);
}

// Timezone
date_default_timezone_set('America/Detroit');

// Site configuration
define('SITE_URL', 'http://localhost:8000');
define('SITE_ROOT', __DIR__);
define('ADMIN_URL', SITE_URL . '/admin');

// Database configuration (same as admin panel)
define('DB_HOST', 'localhost');
define('DB_NAME', 'champions_admin');
define('DB_USER', 'champions_user');
define('DB_PASS', 'champions_password_123');
define('DB_CHARSET', 'utf8mb4');

// File upload settings
define('UPLOAD_PATH', 'assets/images/');
define('UPLOAD_URL', SITE_URL . '/' . UPLOAD_PATH);
define('MAX_UPLOAD_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['image/jpeg', 'image/png', 'image/gif', 'image/webp']);

// Email settings
define('MAIL_FROM', '<EMAIL>');
define('MAIL_FROM_NAME', 'Champions Sports Bar & Grill');
define('CONTACT_EMAIL', '<EMAIL>');

// SMTP Configuration (for production use)
define('SMTP_HOST', 'localhost'); // Change to your SMTP server
define('SMTP_PORT', 587);
define('SMTP_USERNAME', ''); // Your SMTP username
define('SMTP_PASSWORD', ''); // Your SMTP password
define('SMTP_ENCRYPTION', 'tls'); // tls or ssl

// Security settings
define('CSRF_TOKEN_NAME', 'csrf_token');
define('SESSION_LIFETIME', 3600); // 1 hour
define('RATE_LIMIT_CONTACT', 60); // 1 minute between contact form submissions

// Google Services
define('GOOGLE_MAPS_API_KEY', ''); // Will be loaded from database
define('GOOGLE_ANALYTICS_ID', ''); // Will be loaded from database

/**
 * Database Connection Class for Main Site
 */
class SiteDatabase {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
            ];
            
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            // Fallback to default values if database is not available
            error_log("Database connection failed: " . $e->getMessage());
            $this->connection = null;
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    public function query($sql, $params = []) {
        if (!$this->connection) {
            return false;
        }
        
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Database query failed: " . $e->getMessage());
            return false;
        }
    }
    
    public function fetch($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt ? $stmt->fetch() : false;
    }
    
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt ? $stmt->fetchAll() : [];
    }
}

/**
 * Site Configuration Manager
 */
class SiteConfig {
    private static $settings = null;
    private static $db = null;
    
    /**
     * Initialize configuration
     */
    public static function init() {
        self::$db = SiteDatabase::getInstance();
        self::loadSettings();
    }
    
    /**
     * Load settings from database
     */
    private static function loadSettings() {
        if (self::$db->getConnection()) {
            $settings = self::$db->fetchAll("SELECT setting_key, setting_value FROM site_settings");
            
            self::$settings = [];
            foreach ($settings as $setting) {
                self::$settings[$setting['setting_key']] = $setting['setting_value'];
            }
        } else {
            // Fallback default settings
            self::$settings = self::getDefaultSettings();
        }
    }
    
    /**
     * Get setting value
     */
    public static function get($key, $default = null) {
        if (self::$settings === null) {
            self::init();
        }
        
        return self::$settings[$key] ?? $default;
    }
    
    /**
     * Get all settings
     */
    public static function getAll() {
        if (self::$settings === null) {
            self::init();
        }
        
        return self::$settings;
    }
    
    /**
     * Get business hours
     */
    public static function getBusinessHours() {
        $hours = self::get('business_hours');
        if ($hours) {
            return json_decode($hours, true);
        }
        
        // Default hours
        return [
            'monday' => '11:00-00:00',
            'tuesday' => '11:00-00:00',
            'wednesday' => '11:00-00:00',
            'thursday' => '11:00-00:00',
            'friday' => '11:00-02:00',
            'saturday' => '11:00-02:00',
            'sunday' => '11:00-00:00'
        ];
    }
    
    /**
     * Get contact information
     */
    public static function getContactInfo() {
        return [
            'phone' => self::get('contact_phone', '(*************'),
            'email' => self::get('contact_email', '<EMAIL>'),
            'address' => [
                'street' => self::get('address_street', '22112 Sibley Road'),
                'city' => self::get('address_city', 'Brownstown Charter Township'),
                'state' => self::get('address_state', 'MI'),
                'zip' => self::get('address_zip', '48183')
            ]
        ];
    }
    
    /**
     * Get social media links
     */
    public static function getSocialMedia() {
        return [
            'facebook' => self::get('social_facebook', 'https://facebook.com/championssportsgrill'),
            'instagram' => self::get('social_instagram', 'https://instagram.com/champions_sg'),
            'twitter' => self::get('social_twitter', 'https://twitter.com/championssportsgrill')
        ];
    }
    
    /**
     * Default settings fallback
     */
    private static function getDefaultSettings() {
        return [
            'site_name' => 'Champions Sports Bar & Grill',
            'site_tagline' => 'Your Ultimate Sports Bar Experience',
            'contact_phone' => '(*************',
            'contact_email' => '<EMAIL>',
            'address_street' => '22112 Sibley Road',
            'address_city' => 'Brownstown Charter Township',
            'address_state' => 'MI',
            'address_zip' => '48183',
            'social_facebook' => 'https://facebook.com/championssportsgrill',
            'social_instagram' => 'https://instagram.com/champions_sg',
            'social_twitter' => 'https://twitter.com/championssportsgrill',
            'google_maps_api_key' => '',
            'google_analytics_id' => ''
        ];
    }
}

/**
 * Content Manager
 */
class ContentManager {
    private static $db = null;
    
    public static function init() {
        self::$db = SiteDatabase::getInstance();
    }
    
    /**
     * Get content section
     */
    public static function getSection($sectionKey, $default = null) {
        if (!self::$db) {
            self::init();
        }
        
        if (self::$db->getConnection()) {
            $section = self::$db->fetch(
                "SELECT * FROM content_sections WHERE section_key = ? AND is_active = 1",
                [$sectionKey]
            );
            
            if ($section) {
                return $section;
            }
        }
        
        return $default;
    }
    
    /**
     * Get hero banners
     */
    public static function getHeroBanners() {
        if (!self::$db) {
            self::init();
        }
        
        if (self::$db->getConnection()) {
            return self::$db->fetchAll(
                "SELECT * FROM hero_banners WHERE is_active = 1 ORDER BY sort_order ASC, created_at DESC"
            );
        }
        
        return [];
    }
    
    /**
     * Get menu categories with items
     */
    public static function getMenuCategories() {
        if (!self::$db) {
            self::init();
        }
        
        if (self::$db->getConnection()) {
            $categories = self::$db->fetchAll(
                "SELECT * FROM menu_categories WHERE is_active = 1 ORDER BY sort_order ASC"
            );
            
            foreach ($categories as &$category) {
                $category['items'] = self::$db->fetchAll(
                    "SELECT * FROM menu_items WHERE category_id = ? AND is_available = 1 ORDER BY sort_order ASC",
                    [$category['id']]
                );
            }
            
            return $categories;
        }
        
        return [];
    }
    
    /**
     * Get events
     */
    public static function getEvents($limit = null) {
        if (!self::$db) {
            self::init();
        }
        
        if (self::$db->getConnection()) {
            $sql = "SELECT * FROM events WHERE is_active = 1 AND (event_date >= CURDATE() OR is_recurring = 1) ORDER BY event_date ASC";
            if ($limit) {
                $sql .= " LIMIT " . (int)$limit;
            }
            
            return self::$db->fetchAll($sql);
        }
        
        return [];
    }
    
    /**
     * Get gallery images
     */
    public static function getGalleryImages($category = null, $limit = null) {
        if (!self::$db) {
            self::init();
        }
        
        if (self::$db->getConnection()) {
            $sql = "SELECT * FROM gallery_images WHERE 1=1";
            $params = [];
            
            if ($category) {
                $sql .= " AND category = ?";
                $params[] = $category;
            }
            
            $sql .= " ORDER BY sort_order ASC, uploaded_at DESC";
            
            if ($limit) {
                $sql .= " LIMIT " . (int)$limit;
            }
            
            return self::$db->fetchAll($sql, $params);
        }
        
        return [];
    }
}

/**
 * Utility Functions
 */

/**
 * Sanitize input data
 */
function sanitize($data) {
    if (is_array($data)) {
        return array_map('sanitize', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

/**
 * Format phone number for display
 */
function formatPhone($phone) {
    $phone = preg_replace('/[^0-9]/', '', $phone);
    if (strlen($phone) === 10) {
        return '(' . substr($phone, 0, 3) . ') ' . substr($phone, 3, 3) . '-' . substr($phone, 6);
    }
    return $phone;
}

/**
 * Format business hours
 */
function formatBusinessHours($hours) {
    $formatted = [];
    $days = [
        'monday' => 'Monday',
        'tuesday' => 'Tuesday', 
        'wednesday' => 'Wednesday',
        'thursday' => 'Thursday',
        'friday' => 'Friday',
        'saturday' => 'Saturday',
        'sunday' => 'Sunday'
    ];
    
    foreach ($days as $key => $day) {
        if (isset($hours[$key])) {
            $time = $hours[$key];
            if (strpos($time, '-') !== false) {
                list($open, $close) = explode('-', $time);
                $formatted[$day] = date('g:i A', strtotime($open)) . ' - ' . date('g:i A', strtotime($close));
            } else {
                $formatted[$day] = $time;
            }
        }
    }
    
    return $formatted;
}

/**
 * Get current page name
 */
function getCurrentPage() {
    return basename($_SERVER['PHP_SELF'], '.php');
}

/**
 * Check if current page is active
 */
function isActivePage($page) {
    return getCurrentPage() === $page;
}

/**
 * Log error messages to file
 */
function logError($message, $context = []) {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] ERROR: {$message}";

    if (!empty($context)) {
        $logMessage .= " | Context: " . json_encode($context);
    }

    $logMessage .= " | IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown');
    $logMessage .= " | User Agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'unknown');
    $logMessage .= PHP_EOL;

    error_log($logMessage, 3, __DIR__ . '/logs/error.log');
}

/**
 * Log activity messages
 */
function logActivity($message, $context = []) {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] ACTIVITY: {$message}";

    if (!empty($context)) {
        $logMessage .= " | Context: " . json_encode($context);
    }

    $logMessage .= " | IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown');
    $logMessage .= PHP_EOL;

    error_log($logMessage, 3, __DIR__ . '/logs/activity.log');
}

/**
 * Custom exception handler
 */
function customExceptionHandler($exception) {
    $message = "Uncaught exception: " . $exception->getMessage();
    $context = [
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString()
    ];

    logError($message, $context);

    // Show user-friendly error page
    if (!headers_sent()) {
        http_response_code(500);
        include __DIR__ . '/includes/error-page.php';
    }
}

/**
 * Custom error handler
 */
function customErrorHandler($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        return false;
    }

    $context = [
        'severity' => $severity,
        'file' => $file,
        'line' => $line
    ];

    logError($message, $context);

    return true;
}

/**
 * Send email with fallback options
 */
function sendEmail($to, $subject, $message, $headers = []) {
    try {
        // Default headers
        $defaultHeaders = [
            'From' => MAIL_FROM_NAME . ' <' . MAIL_FROM . '>',
            'Reply-To' => CONTACT_EMAIL,
            'X-Mailer' => 'Champions Sports Bar Website',
            'MIME-Version' => '1.0',
            'Content-Type' => 'text/html; charset=UTF-8'
        ];

        // Merge with custom headers
        $allHeaders = array_merge($defaultHeaders, $headers);

        // Convert headers array to string
        $headerString = '';
        foreach ($allHeaders as $key => $value) {
            $headerString .= $key . ': ' . $value . "\r\n";
        }

        // Try to send email
        $success = mail($to, $subject, $message, $headerString);

        if ($success) {
            logActivity("Email sent successfully", [
                'to' => $to,
                'subject' => $subject
            ]);
            return true;
        } else {
            logError("Failed to send email", [
                'to' => $to,
                'subject' => $subject,
                'error' => 'mail() function returned false'
            ]);
            return false;
        }

    } catch (Exception $e) {
        logError("Email sending exception", [
            'to' => $to,
            'subject' => $subject,
            'error' => $e->getMessage()
        ]);
        return false;
    }
}

/**
 * Send contact form notification email
 */
function sendContactNotification($formData) {
    $subject = 'New Contact Form Submission - Champions Sports Bar';

    $message = '
    <html>
    <head>
        <title>New Contact Form Submission</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .header { background: #1a1a1a; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; }
            .field { margin-bottom: 15px; }
            .label { font-weight: bold; color: #555; }
            .value { margin-top: 5px; padding: 10px; background: #f9f9f9; border-left: 4px solid #ff6b35; }
        </style>
    </head>
    <body>
        <div class="header">
            <h2>Champions Sports Bar & Grill</h2>
            <p>New Contact Form Submission</p>
        </div>

        <div class="content">
            <div class="field">
                <div class="label">Name:</div>
                <div class="value">' . htmlspecialchars($formData['name']) . '</div>
            </div>

            <div class="field">
                <div class="label">Email:</div>
                <div class="value">' . htmlspecialchars($formData['email']) . '</div>
            </div>';

    if (!empty($formData['phone'])) {
        $message .= '
            <div class="field">
                <div class="label">Phone:</div>
                <div class="value">' . htmlspecialchars($formData['phone']) . '</div>
            </div>';
    }

    if (!empty($formData['subject'])) {
        $message .= '
            <div class="field">
                <div class="label">Subject:</div>
                <div class="value">' . htmlspecialchars($formData['subject']) . '</div>
            </div>';
    }

    $message .= '
            <div class="field">
                <div class="label">Message:</div>
                <div class="value">' . nl2br(htmlspecialchars($formData['message'])) . '</div>
            </div>

            <div class="field">
                <div class="label">Submitted:</div>
                <div class="value">' . date('F j, Y \a\t g:i A') . '</div>
            </div>

            <div class="field">
                <div class="label">IP Address:</div>
                <div class="value">' . ($_SERVER['REMOTE_ADDR'] ?? 'Unknown') . '</div>
            </div>
        </div>
    </body>
    </html>';

    return sendEmail(CONTACT_EMAIL, $subject, $message);
}

// Set custom error and exception handlers
set_exception_handler('customExceptionHandler');
set_error_handler('customErrorHandler');

// Initialize configuration
SiteConfig::init();
ContentManager::init();

// Make settings available globally
$GLOBALS['site_config'] = SiteConfig::getAll();
$GLOBALS['contact_info'] = SiteConfig::getContactInfo();
$GLOBALS['social_media'] = SiteConfig::getSocialMedia();
$GLOBALS['business_hours'] = SiteConfig::getBusinessHours();

?>
