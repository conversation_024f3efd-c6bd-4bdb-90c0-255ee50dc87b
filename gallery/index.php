<?php
// Champions Sports Bar - Gallery Page
$page_title = "Photo Gallery - Champions Sports Bar & Grill";
$page_description = "Browse our photo gallery showcasing the atmosphere, food, drinks, and outdoor patio at Champions Sports Bar & Grill in Brownstown, MI.";
$current_page = "gallery";
include '../includes/header.php';
?>

<!-- Page Header -->
<section class="page-header bg-dark text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="display-4 fw-bold mb-3">Photo Gallery</h1>
                <p class="lead">Take a visual tour of Champions Sports Bar & Grill</p>
            </div>
        </div>
    </div>
</section>

<!-- Gallery Filters -->
<section class="gallery-filters py-4 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="d-flex flex-wrap justify-content-center gap-2">
                    <button class="btn btn-outline-primary gallery-filter-btn active" data-filter="all">All Photos</button>
                    <button class="btn btn-outline-primary gallery-filter-btn" data-filter="food">Food & Drinks</button>
                    <button class="btn btn-outline-primary gallery-filter-btn" data-filter="interior">Interior</button>
                    <button class="btn btn-outline-primary gallery-filter-btn" data-filter="patio">Outdoor Patio</button>
                    <button class="btn btn-outline-primary gallery-filter-btn" data-filter="events">Events</button>
                    <button class="btn btn-outline-primary gallery-filter-btn" data-filter="sports">Sports Viewing</button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Gallery Grid -->
<section class="gallery-section py-5">
    <div class="container">
        <div class="gallery-grid" id="gallery-grid">
            <!-- Food & Drinks -->
            <div class="gallery-item" data-category="food" data-fancybox="gallery" data-src="assets/images/gallery/food-1.jpg">
                <img src="assets/images/gallery/food-1.jpg" alt="Delicious Burger" class="img-fluid">
                <div class="gallery-overlay">
                    <i class="fas fa-search-plus"></i>
                </div>
            </div>
            
            <div class="gallery-item" data-category="food" data-fancybox="gallery" data-src="assets/images/gallery/food-2.jpg">
                <img src="assets/images/gallery/food-2.jpg" alt="Buffalo Wings" class="img-fluid">
                <div class="gallery-overlay">
                    <i class="fas fa-search-plus"></i>
                </div>
            </div>
            
            <div class="gallery-item" data-category="food" data-fancybox="gallery" data-src="assets/images/gallery/drinks-1.jpg">
                <img src="assets/images/gallery/drinks-1.jpg" alt="Craft Beer Selection" class="img-fluid">
                <div class="gallery-overlay">
                    <i class="fas fa-search-plus"></i>
                </div>
            </div>
            
            <div class="gallery-item" data-category="food" data-fancybox="gallery" data-src="assets/images/gallery/drinks-2.jpg">
                <img src="assets/images/gallery/drinks-2.jpg" alt="Cocktails" class="img-fluid">
                <div class="gallery-overlay">
                    <i class="fas fa-search-plus"></i>
                </div>
            </div>
            
            <!-- Interior -->
            <div class="gallery-item" data-category="interior" data-fancybox="gallery" data-src="assets/images/gallery/interior-1.jpg">
                <img src="assets/images/gallery/interior-1.jpg" alt="Main Dining Area" class="img-fluid">
                <div class="gallery-overlay">
                    <i class="fas fa-search-plus"></i>
                </div>
            </div>
            
            <div class="gallery-item" data-category="interior" data-fancybox="gallery" data-src="assets/images/gallery/interior-2.jpg">
                <img src="assets/images/gallery/interior-2.jpg" alt="Bar Area" class="img-fluid">
                <div class="gallery-overlay">
                    <i class="fas fa-search-plus"></i>
                </div>
            </div>
            
            <div class="gallery-item" data-category="interior" data-fancybox="gallery" data-src="assets/images/gallery/interior-3.jpg">
                <img src="assets/images/gallery/interior-3.jpg" alt="Booth Seating" class="img-fluid">
                <div class="gallery-overlay">
                    <i class="fas fa-search-plus"></i>
                </div>
            </div>
            
            <!-- Outdoor Patio -->
            <div class="gallery-item" data-category="patio" data-fancybox="gallery" data-src="assets/images/gallery/patio-1.jpg">
                <img src="assets/images/gallery/patio-1.jpg" alt="Outdoor Patio Overview" class="img-fluid">
                <div class="gallery-overlay">
                    <i class="fas fa-search-plus"></i>
                </div>
            </div>
            
            <div class="gallery-item" data-category="patio" data-fancybox="gallery" data-src="assets/images/gallery/patio-2.jpg">
                <img src="assets/images/gallery/patio-2.jpg" alt="Outdoor Bar" class="img-fluid">
                <div class="gallery-overlay">
                    <i class="fas fa-search-plus"></i>
                </div>
            </div>
            
            <div class="gallery-item" data-category="patio" data-fancybox="gallery" data-src="assets/images/gallery/patio-3.jpg">
                <img src="assets/images/gallery/patio-3.jpg" alt="Patio Seating" class="img-fluid">
                <div class="gallery-overlay">
                    <i class="fas fa-search-plus"></i>
                </div>
            </div>
            
            <div class="gallery-item" data-category="patio" data-fancybox="gallery" data-src="assets/images/gallery/patio-4.jpg">
                <img src="assets/images/gallery/patio-4.jpg" alt="Patio Evening Atmosphere" class="img-fluid">
                <div class="gallery-overlay">
                    <i class="fas fa-search-plus"></i>
                </div>
            </div>
            
            <!-- Sports Viewing -->
            <div class="gallery-item" data-category="sports" data-fancybox="gallery" data-src="assets/images/gallery/sports-1.jpg">
                <img src="assets/images/gallery/sports-1.jpg" alt="Multiple TV Screens" class="img-fluid">
                <div class="gallery-overlay">
                    <i class="fas fa-search-plus"></i>
                </div>
            </div>
            
            <div class="gallery-item" data-category="sports" data-fancybox="gallery" data-src="assets/images/gallery/sports-2.jpg">
                <img src="assets/images/gallery/sports-2.jpg" alt="Game Day Atmosphere" class="img-fluid">
                <div class="gallery-overlay">
                    <i class="fas fa-search-plus"></i>
                </div>
            </div>
            
            <!-- Events -->
            <div class="gallery-item" data-category="events" data-fancybox="gallery" data-src="assets/images/gallery/events-1.jpg">
                <img src="assets/images/gallery/events-1.jpg" alt="Private Event Setup" class="img-fluid">
                <div class="gallery-overlay">
                    <i class="fas fa-search-plus"></i>
                </div>
            </div>
            
            <div class="gallery-item" data-category="events" data-fancybox="gallery" data-src="assets/images/gallery/events-2.jpg">
                <img src="assets/images/gallery/events-2.jpg" alt="Birthday Celebration" class="img-fluid">
                <div class="gallery-overlay">
                    <i class="fas fa-search-plus"></i>
                </div>
            </div>
            
            <div class="gallery-item" data-category="events" data-fancybox="gallery" data-src="assets/images/gallery/events-3.jpg">
                <img src="assets/images/gallery/events-3.jpg" alt="Live Music Night" class="img-fluid">
                <div class="gallery-overlay">
                    <i class="fas fa-search-plus"></i>
                </div>
            </div>
        </div>
        
        <!-- Load More Button -->
        <div class="row mt-5">
            <div class="col-12 text-center">
                <button id="loadMoreBtn" class="btn btn-primary btn-lg">Load More Photos</button>
            </div>
        </div>
    </div>
</section>

<!-- Gallery CTA -->
<section class="gallery-cta py-5 bg-light">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h3 class="h2 mb-3">Experience It Yourself!</h3>
                <p class="lead mb-0">Come visit Champions Sports Bar & Grill and create your own memorable moments.</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="contact.php" class="btn btn-primary btn-lg me-3">Visit Us</a>
                <a href="events.php" class="btn btn-outline-primary btn-lg">View Events</a>
            </div>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gallery filtering
    const filterButtons = document.querySelectorAll('.gallery-filter-btn');
    const galleryItems = document.querySelectorAll('.gallery-item');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.dataset.filter;
            
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter gallery items
            galleryItems.forEach(item => {
                if (filter === 'all' || item.dataset.category === filter) {
                    item.style.display = 'block';
                    item.classList.add('animate__animated', 'animate__fadeIn');
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });
    
    // Load more functionality (placeholder)
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            // Simulate loading more images
            this.innerHTML = '<span class="loading"></span> Loading...';
            this.disabled = true;
            
            setTimeout(() => {
                this.innerHTML = 'Load More Photos';
                this.disabled = false;
                // In a real implementation, you would load more images here
            }, 2000);
        });
    }
});
</script>

<?php include '../includes/footer.php'; ?>
