# Champions Sports Bar & Grill - Apache Configuration

# Enable URL Rewriting
RewriteEngine On

# Security Headers
<IfModule mod_headers.c>
    # Prevent clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options nosniff
    
    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Referrer Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy (adjust as needed)
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://maps.googleapis.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: https:; connect-src 'self' https:; frame-src https://maps.google.com;"
</IfModule>

# Hide sensitive files
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

<Files ~ "(^#.*#|\.(bak|config|dist|fla|inc|ini|log|psd|sh|sql|sw[op])|~)$">
    Order allow,deny
    Deny from all
</Files>

# Prevent access to PHP files in uploads directory
<Files "*.php">
    Order allow,deny
    Deny from all
</Files>

# SEO-Friendly URLs
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Remove .php extension from URLs
RewriteCond %{REQUEST_FILENAME}.php -f
RewriteRule ^([^.]+)$ $1.php [L]

# Redirect .php URLs to clean URLs
RewriteCond %{THE_REQUEST} /([^.]+)\.php [NC]
RewriteRule ^ /%1? [NC,L,R=301]

# Force HTTPS (uncomment when SSL is configured)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Force www (or remove www - choose one)
# Force www:
# RewriteCond %{HTTP_HOST} !^www\. [NC]
# RewriteRule ^(.*)$ https://www.%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Remove www:
# RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
# RewriteRule ^(.*)$ https://%1%{REQUEST_URI} [L,R=301]

# Compression
<IfModule mod_deflate.c>
    # Compress HTML, CSS, JavaScript, Text, XML and fonts
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
    AddOutputFilterByType DEFLATE application/x-font
    AddOutputFilterByType DEFLATE application/x-font-opentype
    AddOutputFilterByType DEFLATE application/x-font-otf
    AddOutputFilterByType DEFLATE application/x-font-truetype
    AddOutputFilterByType DEFLATE application/x-font-ttf
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE font/otf
    AddOutputFilterByType DEFLATE font/ttf
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE image/x-icon
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/xml
</IfModule>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive on
    
    # Images
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    
    # Fonts
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/otf "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    
    # HTML
    ExpiresByType text/html "access plus 1 hour"
    
    # Default
    ExpiresDefault "access plus 1 week"
</IfModule>

# Custom Error Pages
ErrorDocument 404 /404.php
ErrorDocument 500 /500.php

# Prevent hotlinking
RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?champions-sportsgrill\.com [NC]
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?google\. [NC]
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?bing\. [NC]
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?facebook\. [NC]
RewriteRule \.(jpg|jpeg|png|gif|webp)$ - [F,L]

# Disable server signature
ServerSignature Off

# Limit file upload size (adjust as needed)
php_value upload_max_filesize 10M
php_value post_max_size 10M

# Set timezone (adjust as needed)
php_value date.timezone "America/Detroit"

# Disable PHP errors in production (enable for development)
# php_flag display_errors Off
# php_flag log_errors On

# Performance optimizations
<IfModule mod_headers.c>
    # Remove ETags
    Header unset ETag
    FileETag None
    
    # Cache static resources
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|webp|svg|ico|woff|woff2|ttf|otf)$">
        Header set Cache-Control "public, max-age=2592000"
    </FilesMatch>
    
    # Cache HTML for a short time
    <FilesMatch "\.(html|htm|php)$">
        Header set Cache-Control "public, max-age=3600"
    </FilesMatch>
</IfModule>

# Redirect old .php URLs to new folder structure (SEO-friendly)
RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+menu\.php[\s?] [NC]
RewriteRule ^ /menu/? [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+gallery\.php[\s?] [NC]
RewriteRule ^ /gallery/? [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+events\.php[\s?] [NC]
RewriteRule ^ /events/? [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+careers\.php[\s?] [NC]
RewriteRule ^ /careers/? [R=301,L]

RewriteCond %{THE_REQUEST} ^[A-Z]{3,}\s/+contact\.php[\s?] [NC]
RewriteRule ^ /contact/? [R=301,L]

# Redirect old site URLs to new structure (customize based on old site)
# RewriteRule ^Home\.html$ / [R=301,L]
# RewriteRule ^Menu\.html$ /menu [R=301,L]
# RewriteRule ^Contact\ Us\.html$ /contact [R=301,L]
# RewriteRule ^Join\ Our\ Team\.html$ /careers [R=301,L]
# RewriteRule ^Happenings\.html$ /events [R=301,L]
# RewriteRule ^Photo\ Gallery\.html$ /gallery [R=301,L]
