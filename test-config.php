<?php
/**
 * Champions Sports Bar & Grill - Configuration Test Page
 * 
 * This page tests the config system and displays current settings.
 * Remove this file in production.
 */

// Include configuration
require_once 'config.php';

$page_title = "Configuration Test - " . SiteConfig::get('site_name');
$page_description = "Testing configuration system";
$current_page = "test";

include 'includes/header.php';
?>

<!-- Page Header -->
<section class="page-header bg-dark text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="display-4 fw-bold">Configuration Test</h1>
                <p class="lead">Testing the config system and database integration</p>
            </div>
        </div>
    </div>
</section>

<!-- Configuration Display -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Site Settings -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-cog me-2"></i>
                            Site Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Site Name:</strong></td>
                                <td><?php echo htmlspecialchars(SiteConfig::get('site_name', 'Not set')); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Site Tagline:</strong></td>
                                <td><?php echo htmlspecialchars(SiteConfig::get('site_tagline', 'Not set')); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Contact Phone:</strong></td>
                                <td><?php echo htmlspecialchars(SiteConfig::get('contact_phone', 'Not set')); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Contact Email:</strong></td>
                                <td><?php echo htmlspecialchars(SiteConfig::get('contact_email', 'Not set')); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-address-book me-2"></i>
                            Contact Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php $contact_info = SiteConfig::getContactInfo(); ?>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Phone:</strong></td>
                                <td><?php echo htmlspecialchars($contact_info['phone']); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td><?php echo htmlspecialchars($contact_info['email']); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Street:</strong></td>
                                <td><?php echo htmlspecialchars($contact_info['address']['street']); ?></td>
                            </tr>
                            <tr>
                                <td><strong>City:</strong></td>
                                <td><?php echo htmlspecialchars($contact_info['address']['city']); ?></td>
                            </tr>
                            <tr>
                                <td><strong>State:</strong></td>
                                <td><?php echo htmlspecialchars($contact_info['address']['state']); ?></td>
                            </tr>
                            <tr>
                                <td><strong>ZIP:</strong></td>
                                <td><?php echo htmlspecialchars($contact_info['address']['zip']); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Social Media -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-share-alt me-2"></i>
                            Social Media
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php $social_media = SiteConfig::getSocialMedia(); ?>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Facebook:</strong></td>
                                <td>
                                    <?php if ($social_media['facebook']): ?>
                                        <a href="<?php echo htmlspecialchars($social_media['facebook']); ?>" target="_blank">
                                            <?php echo htmlspecialchars($social_media['facebook']); ?>
                                        </a>
                                    <?php else: ?>
                                        Not set
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Instagram:</strong></td>
                                <td>
                                    <?php if ($social_media['instagram']): ?>
                                        <a href="<?php echo htmlspecialchars($social_media['instagram']); ?>" target="_blank">
                                            <?php echo htmlspecialchars($social_media['instagram']); ?>
                                        </a>
                                    <?php else: ?>
                                        Not set
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Twitter:</strong></td>
                                <td>
                                    <?php if ($social_media['twitter']): ?>
                                        <a href="<?php echo htmlspecialchars($social_media['twitter']); ?>" target="_blank">
                                            <?php echo htmlspecialchars($social_media['twitter']); ?>
                                        </a>
                                    <?php else: ?>
                                        Not set
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Business Hours -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-clock me-2"></i>
                            Business Hours
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php 
                        $business_hours = SiteConfig::getBusinessHours();
                        $formatted_hours = formatBusinessHours($business_hours);
                        ?>
                        <table class="table table-sm">
                            <?php foreach ($formatted_hours as $day => $hours): ?>
                            <tr>
                                <td><strong><?php echo $day; ?>:</strong></td>
                                <td><?php echo htmlspecialchars($hours); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Database Status -->
            <div class="col-12 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-database me-2"></i>
                            Database Status
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php 
                        $db = SiteDatabase::getInstance();
                        $connection = $db->getConnection();
                        ?>
                        
                        <?php if ($connection): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>Database Connected Successfully!</strong>
                                <br>
                                <small>Configuration is loading from the database.</small>
                            </div>
                            
                            <?php
                            // Test database queries
                            $tableCount = $db->fetch("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ?", [DB_NAME]);
                            $settingsCount = $db->fetch("SELECT COUNT(*) as count FROM site_settings");
                            ?>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Database:</strong> <?php echo DB_NAME; ?></p>
                                    <p><strong>Tables:</strong> <?php echo $tableCount['count'] ?? 'Unknown'; ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Settings:</strong> <?php echo $settingsCount['count'] ?? 'Unknown'; ?></p>
                                    <p><strong>Host:</strong> <?php echo DB_HOST; ?></p>
                                </div>
                            </div>
                            
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Database Not Connected</strong>
                                <br>
                                <small>Using fallback default settings. Please set up the database using the admin panel setup.</small>
                            </div>
                            
                            <p><strong>Database Configuration:</strong></p>
                            <ul>
                                <li><strong>Host:</strong> <?php echo DB_HOST; ?></li>
                                <li><strong>Database:</strong> <?php echo DB_NAME; ?></li>
                                <li><strong>User:</strong> <?php echo DB_USER; ?></li>
                            </ul>
                            
                            <a href="admin/setup-database.php" class="btn btn-primary">
                                <i class="fas fa-database me-1"></i>
                                Set Up Database
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-tools me-2"></i>
                            Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <a href="index.php" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-home me-1"></i> Homepage
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="admin/login.php" class="btn btn-outline-secondary w-100">
                                    <i class="fas fa-user-shield me-1"></i> Admin Login
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="admin/setup-database.php" class="btn btn-outline-info w-100">
                                    <i class="fas fa-database me-1"></i> Setup Database
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="contact.php" class="btn btn-outline-success w-100">
                                    <i class="fas fa-envelope me-1"></i> Contact Page
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.page-header {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.table td {
    border-top: 1px solid #dee2e6;
    padding: 0.5rem;
}

.table td:first-child {
    width: 30%;
}
</style>

<?php include 'includes/footer.php'; ?>
