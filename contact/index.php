<?php
// Champions Sports Bar - Contact Page

// Include config first
require_once '../config.php';

$page_title = "Contact Us - " . SiteConfig::get('site_name', 'Champions Sports Bar & Grill');
$page_description = "Visit " . SiteConfig::get('site_name', 'Champions Sports Bar & Grill') . " in Brownstown, MI. Get directions, hours, contact information, and send us a message.";
$current_page = "contact";
include '../includes/header.php';

// Get contact info from config
$contact_info = SiteConfig::getContactInfo();
$business_hours = SiteConfig::getBusinessHours();
$formatted_hours = formatBusinessHours($business_hours);
?>

<!-- Page Header -->
<section class="page-header bg-dark text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1 class="display-4 fw-bold mb-3">Contact Us</h1>
                <p class="lead">Visit us, call us, or send us a message</p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Information -->
<section class="contact-info py-5">
    <div class="container">
        <div class="row g-4">
            <!-- Location Info -->
            <div class="col-lg-4">
                <div class="contact-card h-100 p-4 text-center">
                    <div class="contact-icon mb-3">
                        <i class="fas fa-map-marker-alt fa-3x text-primary"></i>
                    </div>
                    <h4>Location</h4>
                    <p class="mb-3">
                        <?php echo htmlspecialchars($contact_info['address']['street']); ?><br>
                        <?php echo htmlspecialchars($contact_info['address']['city']); ?><br>
                        <?php echo htmlspecialchars($contact_info['address']['state'] . ' ' . $contact_info['address']['zip']); ?>
                    </p>
                    <?php
                    $address_query = urlencode($contact_info['address']['street'] . ', ' . $contact_info['address']['city'] . ', ' . $contact_info['address']['state'] . ' ' . $contact_info['address']['zip']);
                    ?>
                    <a href="https://maps.google.com/?q=<?php echo $address_query; ?>"
                       target="_blank" class="btn btn-outline-primary">Get Directions</a>
                </div>
            </div>

            <!-- Phone Info -->
            <div class="col-lg-4">
                <div class="contact-card h-100 p-4 text-center">
                    <div class="contact-icon mb-3">
                        <i class="fas fa-phone fa-3x text-primary"></i>
                    </div>
                    <h4>Phone</h4>
                    <p class="mb-3">
                        <a href="tel:<?php echo preg_replace('/[^0-9]/', '', $contact_info['phone']); ?>" class="text-decoration-none">
                            <?php echo htmlspecialchars($contact_info['phone']); ?>
                        </a>
                    </p>
                    <p class="text-muted mb-3">Call us for reservations, takeout orders, or any questions</p>
                    <a href="tel:<?php echo preg_replace('/[^0-9]/', '', $contact_info['phone']); ?>" class="btn btn-outline-primary">Call Now</a>
                </div>
            </div>

            <!-- Email Info -->
            <div class="col-lg-4">
                <div class="contact-card h-100 p-4 text-center">
                    <div class="contact-icon mb-3">
                        <i class="fas fa-envelope fa-3x text-primary"></i>
                    </div>
                    <h4>Email</h4>
                    <p class="mb-3">
                        <a href="mailto:<?php echo htmlspecialchars($contact_info['email']); ?>" class="text-decoration-none">
                            <?php echo htmlspecialchars($contact_info['email']); ?>
                        </a>
                    </p>
                    <p class="text-muted mb-3">Send us an email and we'll get back to you within 24 hours</p>
                    <a href="mailto:<?php echo htmlspecialchars($contact_info['email']); ?>" class="btn btn-outline-primary">Send Email</a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Hours & Map Section -->
<section class="hours-map py-5 bg-light">
    <div class="container">
        <div class="row g-4">
            <!-- Hours -->
            <div class="col-lg-6">
                <div class="hours-card p-4">
                    <h3 class="h2 fw-bold mb-4 text-center">Hours of Operation</h3>
                    <div class="hours-list">
                        <?php
                        $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                        foreach ($days as $index => $day):
                            $isLast = ($index === count($days) - 1);
                        ?>
                        <div class="hour-item d-flex justify-content-between py-2 <?php echo !$isLast ? 'border-bottom' : ''; ?>">
                            <span class="fw-semibold"><?php echo $day; ?></span>
                            <span><?php echo htmlspecialchars($formatted_hours[$day] ?? '11:00 AM - 12:00 AM'); ?></span>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="mt-3 p-3 bg-warning bg-opacity-10 rounded">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Kitchen closes 1 hour before bar closing time. Hours may vary on holidays.
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- Map -->
            <div class="col-lg-6">
                <div class="map-container">
                    <div id="map" style="height: 400px; border-radius: 0.5rem;"></div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Form -->
<section class="contact-form-section py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="contact-form">
                    <h3 class="h2 fw-bold mb-4 text-center">Send Us a Message</h3>
                    <form id="contactForm" method="POST" action="../process-contact.php">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">Name *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="col-md-6">
                                <label for="phone" class="form-label">Phone</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                            <div class="col-md-6">
                                <label for="subject" class="form-label">Subject</label>
                                <select class="form-control" id="subject" name="subject">
                                    <option value="">Select a subject</option>
                                    <option value="general">General Inquiry</option>
                                    <option value="reservation">Reservation</option>
                                    <option value="private-event">Private Event</option>
                                    <option value="feedback">Feedback</option>
                                    <option value="employment">Employment</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label for="message" class="form-label">Message *</label>
                                <textarea class="form-control" id="message" name="message" rows="5" required 
                                          placeholder="Tell us how we can help you..."></textarea>
                            </div>
                            <div class="col-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="newsletter" name="newsletter">
                                    <label class="form-check-label" for="newsletter">
                                        I would like to receive email updates about events and specials
                                    </label>
                                </div>
                            </div>
                            <div class="col-12 text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i>Send Message
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Social Media & Additional Info -->
<section class="social-section py-5 bg-dark text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h3 class="h2 mb-3">Follow Us on Social Media</h3>
                <p class="lead mb-4">Stay updated with our latest events, specials, and news!</p>
                <div class="social-links">
                    <a href="https://instagram.com/champions_sg" target="_blank" class="btn btn-outline-light me-3">
                        <i class="fab fa-instagram me-2"></i>Instagram
                    </a>
                    <a href="https://facebook.com/championssportsgrill" target="_blank" class="btn btn-outline-light me-3">
                        <i class="fab fa-facebook me-2"></i>Facebook
                    </a>
                    <a href="https://twitter.com/championssportsgrill" target="_blank" class="btn btn-outline-light">
                        <i class="fab fa-twitter me-2"></i>Twitter
                    </a>
                </div>
            </div>
            <div class="col-lg-6 text-lg-end">
                <h4 class="mb-3">Quick Actions</h4>
                <div class="d-flex flex-column flex-lg-row gap-2 justify-content-lg-end">
                    <a href="../menu/" class="btn btn-primary">View Menu</a>
                    <a href="../events/" class="btn btn-outline-light">Current Events</a>
                    <a href="../careers/" class="btn btn-outline-light">Join Our Team</a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Google Maps Script -->
<script async defer 
        src="https://maps.googleapis.com/maps/api/js?key=YOUR_GOOGLE_MAPS_API_KEY&callback=initMap">
</script>

<script>
// Initialize Google Map
function initMap() {
    const championsLocation = { lat: 42.1687, lng: -83.1756 };
    
    const map = new google.maps.Map(document.getElementById('map'), {
        zoom: 15,
        center: championsLocation,
        styles: [
            {
                featureType: 'all',
                elementType: 'geometry.fill',
                stylers: [{ color: '#f5f5f5' }]
            },
            {
                featureType: 'water',
                elementType: 'geometry',
                stylers: [{ color: '#c9c9c9' }]
            }
        ]
    });
    
    const marker = new google.maps.Marker({
        position: championsLocation,
        map: map,
        title: 'Champions Sports Bar & Grill'
    });
    
    const infoWindow = new google.maps.InfoWindow({
        content: `
            <div style="padding: 10px;">
                <h6 style="margin-bottom: 5px;">Champions Sports Bar & Grill</h6>
                <p style="margin-bottom: 5px;">22112 Sibley Road<br>Brownstown Charter Township, MI 48183</p>
                <p style="margin-bottom: 0;"><strong>Phone:</strong> (*************</p>
            </div>
        `
    });
    
    marker.addListener('click', () => {
        infoWindow.open(map, marker);
    });
}

// Fallback if Google Maps fails to load
window.addEventListener('load', function() {
    setTimeout(function() {
        if (!window.google) {
            document.getElementById('map').innerHTML = `
                <div class="d-flex align-items-center justify-content-center h-100 bg-light">
                    <div class="text-center">
                        <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Map temporarily unavailable</p>
                        <a href="https://maps.google.com/?q=22112+Sibley+Road,+Brownstown+Charter+Township,+MI+48183" 
                           target="_blank" class="btn btn-primary">View on Google Maps</a>
                    </div>
                </div>
            `;
        }
    }, 3000);
});
</script>

<?php include '../includes/footer.php'; ?>
