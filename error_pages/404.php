<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Not Found - Champions Sports Bar</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="/assets/css/style.css" rel="stylesheet">
    <style>
        .error-page {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: white;
        }
        .error-content {
            text-align: center;
            max-width: 600px;
            padding: 2rem;
        }
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            color: #ff6b35;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            margin-bottom: 1rem;
        }
        .error-title {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #fff;
        }
        .error-description {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: #ccc;
        }
        .btn-home {
            background: linear-gradient(45deg, #ff6b35, #f7931e);
            border: none;
            padding: 12px 30px;
            font-size: 1.1rem;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        .btn-home:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 53, 0.4);
        }
        .sports-icon {
            font-size: 3rem;
            color: #ff6b35;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="error-page">
        <div class="error-content">
            <div class="sports-icon">🏈</div>
            <div class="error-code">404</div>
            <h1 class="error-title">Oops! Page Not Found</h1>
            <p class="error-description">
                Looks like this page fumbled the ball! The page you're looking for doesn't exist or has been moved.
            </p>
            <div class="mt-4">
                <a href="/" class="btn btn-primary btn-home">
                    🏠 Back to Home Field
                </a>
            </div>
            <div class="mt-4">
                <p class="text-muted">
                    <small>
                        Try checking the URL for typos, or use our navigation menu to find what you're looking for.
                    </small>
                </p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
