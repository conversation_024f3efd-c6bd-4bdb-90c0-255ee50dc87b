2025-07-12 16:26:24 [INFO] Error handling test started | Context: {"test_id":1} | IP: ::1 | URI: /test_error_handling.php
2025-07-12 16:26:24 [WARNING] This is a test warning | Context: {"test_id":2} | IP: ::1 | URI: /test_error_handling.php
2025-07-12 16:26:24 [ERROR] This is a test error | Context: {"test_id":3} | IP: ::1 | URI: /test_error_handling.php
2025-07-12 16:26:24 [DEBUG] This is a test debug message | Context: {"test_id":4} | IP: ::1 | URI: /test_error_handling.php
2025-07-12 16:26:24 [WARNING] Validation Error: email - Invalid email format | Context: {"type":"Validation Error","field":"email","message":"Invalid email format","value":"invalid-email"} | IP: ::1 | URI: /test_error_handling.php
2025-07-12 16:26:24 [ERROR] File Upload Error: File too large (exceeds upload_max_filesize) | Context: {"type":"File Upload Error","error_code":1,"filename":"test_file.jpg","message":"File too large (exceeds upload_max_filesize)"} | IP: ::1 | URI: /test_error_handling.php
2025-07-12 16:26:24 [ERROR] Security Violation: SQL Injection Attempt | Context: {"type":"Security Violation","violation_type":"SQL Injection Attempt","query":"SELECT * FROM users WHERE id = '1 OR 1=1'","ip":"::1"} | IP: ::1 | URI: /test_error_handling.php
2025-07-12 16:26:24 [ERROR] Database Error: Table does not exist | Query: SELECT * FROM non_existent_table | Context: {"type":"Database Error","error":"Table does not exist","query":"SELECT * FROM non_existent_table"} | IP: ::1 | URI: /test_error_handling.php
