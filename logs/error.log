[2025-07-11 16:25:20] ERROR: Test error message | Context: {"test":"data"} | IP: unknown | User Agent: unknown
[2025-07-11 16:27:06] ERROR: Failed to send email | Context: {"to":"<EMAIL>","subject":"New Contact Form Submission - Champions Sports Bar","error":"mail() function returned false"} | IP: ::1 | User Agent: curl/8.11.0
[11-Jul-2025 16:27:06 America/Detroit] Contact form submitted successfully by: <EMAIL> (Message ID: 2)
[11-Jul-2025 16:30:30 America/Detroit] PHP Fatal error:  Uncaught Error: Call to undefined function getDB() in Command line code:3
Stack trace:
#0 {main}
  thrown in Command line code on line 3
[2025-07-11 16:46:29] ERROR: Uncaught exception: Call to undefined function getDB() | Context: {"file":"\/home\/<USER>\/Documents\/augment-projects\/Champions-Sports-Bar-2025\/menu\/index.php","line":10,"trace":"#0 {main}"} | IP: ::1 | User Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-11 16:56:36] ERROR: Uncaught exception: Call to undefined function getDB() | Context: {"file":"\/home\/<USER>\/Documents\/augment-projects\/Champions-Sports-Bar-2025\/menu\/index.php","line":10,"trace":"#0 {main}"} | IP: ::1 | User Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-11 16:56:45] ERROR: Uncaught exception: Call to undefined function getDB() | Context: {"file":"\/home\/<USER>\/Documents\/augment-projects\/Champions-Sports-Bar-2025\/menu\/index.php","line":10,"trace":"#0 {main}"} | IP: ::1 | User Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[11-Jul-2025 16:57:02 America/Detroit] PHP Fatal error:  Uncaught Error: Call to undefined function getDB() in Command line code:4
Stack trace:
#0 {main}
  thrown in Command line code on line 4
[2025-07-11 16:59:36] ERROR: Uncaught exception: Call to undefined function getDB() | Context: {"file":"\/home\/<USER>\/Documents\/augment-projects\/Champions-Sports-Bar-2025\/menu\/index.php","line":10,"trace":"#0 {main}"} | IP: ::1 | User Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-11 17:00:11] ERROR: Uncaught exception: Call to undefined function getDB() | Context: {"file":"\/home\/<USER>\/Documents\/augment-projects\/Champions-Sports-Bar-2025\/menu\/index.php","line":10,"trace":"#0 {main}"} | IP: ::1 | User Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
