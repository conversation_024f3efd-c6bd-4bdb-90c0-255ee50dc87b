<?php
/**
 * Champions Sports Bar Website - Database Operations Test
 * 
 * This script comprehensively tests all CRUD operations across all database tables
 * to ensure data integrity and proper functionality.
 */

// Include configuration
require_once 'config.php';

// Test results storage
$testResults = [];
$totalTests = 0;
$passedTests = 0;

/**
 * Add test result
 */
function addTestResult($testName, $passed, $message = '') {
    global $testResults, $totalTests, $passedTests;
    
    $totalTests++;
    if ($passed) {
        $passedTests++;
    }
    
    $testResults[] = [
        'name' => $testName,
        'passed' => $passed,
        'message' => $message,
        'status' => $passed ? 'PASS' : 'FAIL'
    ];
}

/**
 * Test database connection and basic operations
 */
function testDatabaseConnection() {
    try {
        $db = SiteDatabase::getInstance();
        $connection = $db->getConnection();
        
        if ($connection) {
            // Test transaction support
            $connection->beginTransaction();
            $connection->rollback();
            
            addTestResult('Database Connection & Transactions', true, 'Connection established, transactions supported');
        } else {
            addTestResult('Database Connection & Transactions', false, 'Failed to establish connection');
        }
    } catch (Exception $e) {
        addTestResult('Database Connection & Transactions', false, 'Exception: ' . $e->getMessage());
    }
}

/**
 * Test site settings CRUD operations
 */
function testSiteSettingsCRUD() {
    try {
        $db = SiteDatabase::getInstance();
        $connection = $db->getConnection();
        
        // CREATE - Insert test setting
        $stmt = $connection->prepare("INSERT INTO site_settings (setting_key, setting_value) VALUES (?, ?)");
        $testKey = 'test_setting_' . time();
        $testValue = 'test_value_' . time();
        $created = $stmt->execute([$testKey, $testValue]);
        
        if (!$created) {
            addTestResult('Site Settings CRUD', false, 'Failed to create test setting');
            return;
        }
        
        // READ - Retrieve the setting
        $stmt = $connection->prepare("SELECT setting_value FROM site_settings WHERE setting_key = ?");
        $stmt->execute([$testKey]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$result || $result['setting_value'] !== $testValue) {
            addTestResult('Site Settings CRUD', false, 'Failed to read test setting');
            return;
        }
        
        // UPDATE - Modify the setting
        $newValue = 'updated_value_' . time();
        $stmt = $connection->prepare("UPDATE site_settings SET setting_value = ? WHERE setting_key = ?");
        $updated = $stmt->execute([$newValue, $testKey]);
        
        // Verify update
        $stmt = $connection->prepare("SELECT setting_value FROM site_settings WHERE setting_key = ?");
        $stmt->execute([$testKey]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$updated || !$result || $result['setting_value'] !== $newValue) {
            addTestResult('Site Settings CRUD', false, 'Failed to update test setting');
            return;
        }
        
        // DELETE - Remove the test setting
        $stmt = $connection->prepare("DELETE FROM site_settings WHERE setting_key = ?");
        $deleted = $stmt->execute([$testKey]);
        
        // Verify deletion
        $stmt = $connection->prepare("SELECT COUNT(*) as count FROM site_settings WHERE setting_key = ?");
        $stmt->execute([$testKey]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$deleted || $result['count'] > 0) {
            addTestResult('Site Settings CRUD', false, 'Failed to delete test setting');
            return;
        }
        
        addTestResult('Site Settings CRUD', true, 'All CRUD operations successful');
        
    } catch (Exception $e) {
        addTestResult('Site Settings CRUD', false, 'Exception: ' . $e->getMessage());
    }
}

/**
 * Test content pages CRUD operations
 */
function testContentPagesCRUD() {
    try {
        $db = SiteDatabase::getInstance();
        $connection = $db->getConnection();
        
        // CREATE - Insert test content page
        $stmt = $connection->prepare("
            INSERT INTO content_pages (page_name, page_title, page_content, meta_description) 
            VALUES (?, ?, ?, ?)
        ");
        $testPage = 'test_page_' . time();
        $testTitle = 'Test Page Title';
        $testContent = 'Test page content for CRUD testing.';
        $testMeta = 'Test meta description';
        
        $created = $stmt->execute([$testPage, $testTitle, $testContent, $testMeta]);
        
        if (!$created) {
            addTestResult('Content Pages CRUD', false, 'Failed to create test page');
            return;
        }
        
        $pageId = $connection->lastInsertId();
        
        // READ - Retrieve the page
        $stmt = $connection->prepare("SELECT * FROM content_pages WHERE id = ?");
        $stmt->execute([$pageId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$result || $result['page_name'] !== $testPage) {
            addTestResult('Content Pages CRUD', false, 'Failed to read test page');
            return;
        }
        
        // UPDATE - Modify the page
        $newTitle = 'Updated Test Page Title';
        $stmt = $connection->prepare("UPDATE content_pages SET page_title = ? WHERE id = ?");
        $updated = $stmt->execute([$newTitle, $pageId]);
        
        // Verify update
        $stmt = $connection->prepare("SELECT page_title FROM content_pages WHERE id = ?");
        $stmt->execute([$pageId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$updated || !$result || $result['page_title'] !== $newTitle) {
            addTestResult('Content Pages CRUD', false, 'Failed to update test page');
            return;
        }
        
        // DELETE - Remove the test page
        $stmt = $connection->prepare("DELETE FROM content_pages WHERE id = ?");
        $deleted = $stmt->execute([$pageId]);
        
        // Verify deletion
        $stmt = $connection->prepare("SELECT COUNT(*) as count FROM content_pages WHERE id = ?");
        $stmt->execute([$pageId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$deleted || $result['count'] > 0) {
            addTestResult('Content Pages CRUD', false, 'Failed to delete test page');
            return;
        }
        
        addTestResult('Content Pages CRUD', true, 'All CRUD operations successful');
        
    } catch (Exception $e) {
        addTestResult('Content Pages CRUD', false, 'Exception: ' . $e->getMessage());
    }
}

/**
 * Test events CRUD operations
 */
function testEventsCRUD() {
    try {
        $db = SiteDatabase::getInstance();
        $connection = $db->getConnection();
        
        // CREATE - Insert test event
        $stmt = $connection->prepare("
            INSERT INTO events (title, description, event_date, event_time, location, status) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $testTitle = 'Test Event ' . time();
        $testDesc = 'Test event description for CRUD testing.';
        $testDate = date('Y-m-d', strtotime('+1 week'));
        $testTime = '19:00:00';
        $testLocation = 'Test Location';
        $testStatus = 'active';
        
        $created = $stmt->execute([$testTitle, $testDesc, $testDate, $testTime, $testLocation, $testStatus]);
        
        if (!$created) {
            addTestResult('Events CRUD', false, 'Failed to create test event');
            return;
        }
        
        $eventId = $connection->lastInsertId();
        
        // READ - Retrieve the event
        $stmt = $connection->prepare("SELECT * FROM events WHERE id = ?");
        $stmt->execute([$eventId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$result || $result['title'] !== $testTitle) {
            addTestResult('Events CRUD', false, 'Failed to read test event');
            return;
        }
        
        // UPDATE - Modify the event
        $newTitle = 'Updated Test Event ' . time();
        $stmt = $connection->prepare("UPDATE events SET title = ? WHERE id = ?");
        $updated = $stmt->execute([$newTitle, $eventId]);
        
        // Verify update
        $stmt = $connection->prepare("SELECT title FROM events WHERE id = ?");
        $stmt->execute([$eventId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$updated || !$result || $result['title'] !== $newTitle) {
            addTestResult('Events CRUD', false, 'Failed to update test event');
            return;
        }
        
        // DELETE - Remove the test event
        $stmt = $connection->prepare("DELETE FROM events WHERE id = ?");
        $deleted = $stmt->execute([$eventId]);
        
        // Verify deletion
        $stmt = $connection->prepare("SELECT COUNT(*) as count FROM events WHERE id = ?");
        $stmt->execute([$eventId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$deleted || $result['count'] > 0) {
            addTestResult('Events CRUD', false, 'Failed to delete test event');
            return;
        }
        
        addTestResult('Events CRUD', true, 'All CRUD operations successful');
        
    } catch (Exception $e) {
        addTestResult('Events CRUD', false, 'Exception: ' . $e->getMessage());
    }
}

/**
 * Test jobs and job applications CRUD operations
 */
function testJobsCRUD() {
    try {
        $db = SiteDatabase::getInstance();
        $connection = $db->getConnection();
        
        // CREATE - Insert test job
        $stmt = $connection->prepare("
            INSERT INTO jobs (title, department, description, requirements, salary_range, status) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $testTitle = 'Test Job ' . time();
        $testDept = 'Test Department';
        $testDesc = 'Test job description for CRUD testing.';
        $testReq = 'Test requirements';
        $testSalary = '$15-20/hour';
        $testStatus = 'active';
        
        $created = $stmt->execute([$testTitle, $testDept, $testDesc, $testReq, $testSalary, $testStatus]);
        
        if (!$created) {
            addTestResult('Jobs CRUD', false, 'Failed to create test job');
            return;
        }
        
        $jobId = $connection->lastInsertId();
        
        // Test job application creation
        $stmt = $connection->prepare("
            INSERT INTO job_applications (job_id, name, email, phone, cover_letter, status) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $appName = 'Test Applicant';
        $appEmail = '<EMAIL>';
        $appPhone = '************';
        $appCover = 'Test cover letter';
        $appStatus = 'pending';
        
        $appCreated = $stmt->execute([$jobId, $appName, $appEmail, $appPhone, $appCover, $appStatus]);
        
        if (!$appCreated) {
            addTestResult('Jobs CRUD', false, 'Failed to create test job application');
            return;
        }
        
        $appId = $connection->lastInsertId();
        
        // READ - Retrieve job and application
        $stmt = $connection->prepare("
            SELECT j.*, ja.name as applicant_name 
            FROM jobs j 
            LEFT JOIN job_applications ja ON j.id = ja.job_id 
            WHERE j.id = ? AND ja.id = ?
        ");
        $stmt->execute([$jobId, $appId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$result || $result['title'] !== $testTitle || $result['applicant_name'] !== $appName) {
            addTestResult('Jobs CRUD', false, 'Failed to read test job and application');
            return;
        }
        
        // UPDATE - Modify job and application
        $newTitle = 'Updated Test Job ' . time();
        $stmt = $connection->prepare("UPDATE jobs SET title = ? WHERE id = ?");
        $jobUpdated = $stmt->execute([$newTitle, $jobId]);
        
        $newStatus = 'reviewed';
        $stmt = $connection->prepare("UPDATE job_applications SET status = ? WHERE id = ?");
        $appUpdated = $stmt->execute([$newStatus, $appId]);
        
        if (!$jobUpdated || !$appUpdated) {
            addTestResult('Jobs CRUD', false, 'Failed to update test job or application');
            return;
        }
        
        // DELETE - Clean up test data
        $stmt = $connection->prepare("DELETE FROM job_applications WHERE id = ?");
        $appDeleted = $stmt->execute([$appId]);
        
        $stmt = $connection->prepare("DELETE FROM jobs WHERE id = ?");
        $jobDeleted = $stmt->execute([$jobId]);
        
        if (!$appDeleted || !$jobDeleted) {
            addTestResult('Jobs CRUD', false, 'Failed to delete test job or application');
            return;
        }
        
        addTestResult('Jobs CRUD', true, 'All CRUD operations successful for jobs and applications');
        
    } catch (Exception $e) {
        addTestResult('Jobs CRUD', false, 'Exception: ' . $e->getMessage());
    }
}

/**
 * Test menu system CRUD operations
 */
function testMenuCRUD() {
    try {
        $db = SiteDatabase::getInstance();
        $connection = $db->getConnection();
        
        // CREATE - Insert test menu category
        $stmt = $connection->prepare("INSERT INTO menu_categories (name, description, sort_order) VALUES (?, ?, ?)");
        $testCatName = 'Test Category ' . time();
        $testCatDesc = 'Test category description';
        $testOrder = 999;
        
        $catCreated = $stmt->execute([$testCatName, $testCatDesc, $testOrder]);
        
        if (!$catCreated) {
            addTestResult('Menu CRUD', false, 'Failed to create test menu category');
            return;
        }
        
        $catId = $connection->lastInsertId();
        
        // CREATE - Insert test menu item
        $stmt = $connection->prepare("
            INSERT INTO menu_items (category_id, name, description, price, allergens, featured) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $testItemName = 'Test Menu Item ' . time();
        $testItemDesc = 'Test menu item description';
        $testPrice = 12.99;
        $testAllergens = 'None';
        $testFeatured = 0;
        
        $itemCreated = $stmt->execute([$catId, $testItemName, $testItemDesc, $testPrice, $testAllergens, $testFeatured]);
        
        if (!$itemCreated) {
            addTestResult('Menu CRUD', false, 'Failed to create test menu item');
            return;
        }
        
        $itemId = $connection->lastInsertId();
        
        // READ - Retrieve category and item
        $stmt = $connection->prepare("
            SELECT mc.name as category_name, mi.name as item_name, mi.price 
            FROM menu_categories mc 
            JOIN menu_items mi ON mc.id = mi.category_id 
            WHERE mc.id = ? AND mi.id = ?
        ");
        $stmt->execute([$catId, $itemId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$result || $result['category_name'] !== $testCatName || $result['item_name'] !== $testItemName) {
            addTestResult('Menu CRUD', false, 'Failed to read test menu category and item');
            return;
        }
        
        // UPDATE - Modify category and item
        $newCatName = 'Updated Test Category ' . time();
        $stmt = $connection->prepare("UPDATE menu_categories SET name = ? WHERE id = ?");
        $catUpdated = $stmt->execute([$newCatName, $catId]);
        
        $newPrice = 15.99;
        $stmt = $connection->prepare("UPDATE menu_items SET price = ? WHERE id = ?");
        $itemUpdated = $stmt->execute([$newPrice, $itemId]);
        
        if (!$catUpdated || !$itemUpdated) {
            addTestResult('Menu CRUD', false, 'Failed to update test menu category or item');
            return;
        }
        
        // DELETE - Clean up test data
        $stmt = $connection->prepare("DELETE FROM menu_items WHERE id = ?");
        $itemDeleted = $stmt->execute([$itemId]);
        
        $stmt = $connection->prepare("DELETE FROM menu_categories WHERE id = ?");
        $catDeleted = $stmt->execute([$catId]);
        
        if (!$itemDeleted || !$catDeleted) {
            addTestResult('Menu CRUD', false, 'Failed to delete test menu category or item');
            return;
        }
        
        addTestResult('Menu CRUD', true, 'All CRUD operations successful for menu categories and items');
        
    } catch (Exception $e) {
        addTestResult('Menu CRUD', false, 'Exception: ' . $e->getMessage());
    }
}

/**
 * Test foreign key constraints and referential integrity
 */
function testReferentialIntegrity() {
    try {
        $db = SiteDatabase::getInstance();
        $connection = $db->getConnection();
        
        // Test that we cannot create a menu item with invalid category_id
        $stmt = $connection->prepare("INSERT INTO menu_items (category_id, name, description, price) VALUES (?, ?, ?, ?)");
        
        try {
            $stmt->execute([99999, 'Test Item', 'Test Description', 10.00]);
            addTestResult('Referential Integrity', false, 'Foreign key constraint not enforced');
        } catch (PDOException $e) {
            // This should fail due to foreign key constraint
            if (strpos($e->getMessage(), 'foreign key constraint') !== false || 
                strpos($e->getMessage(), 'Cannot add or update') !== false) {
                addTestResult('Referential Integrity', true, 'Foreign key constraints properly enforced');
            } else {
                addTestResult('Referential Integrity', false, 'Unexpected error: ' . $e->getMessage());
            }
        }
        
    } catch (Exception $e) {
        addTestResult('Referential Integrity', false, 'Exception: ' . $e->getMessage());
    }
}

// Run all database tests
echo "<h1>Champions Sports Bar - Database Operations Test</h1>\n";
echo "<p>Testing all CRUD operations and database integrity...</p>\n";

testDatabaseConnection();
testSiteSettingsCRUD();
testContentPagesCRUD();
testEventsCRUD();
testJobsCRUD();
testMenuCRUD();
testReferentialIntegrity();

// Display results
echo "<h2>Database Test Results Summary</h2>\n";
echo "<p><strong>Total Tests:</strong> {$totalTests}</p>\n";
echo "<p><strong>Passed:</strong> {$passedTests}</p>\n";
echo "<p><strong>Failed:</strong> " . ($totalTests - $passedTests) . "</p>\n";
echo "<p><strong>Success Rate:</strong> " . round(($passedTests / $totalTests) * 100, 1) . "%</p>\n";

echo "<h2>Detailed Test Results</h2>\n";
echo "<table border='1' cellpadding='5' cellspacing='0' style='width: 100%; border-collapse: collapse;'>\n";
echo "<tr style='background: #f5f5f5;'><th>Test Name</th><th>Status</th><th>Message</th></tr>\n";

foreach ($testResults as $result) {
    $statusColor = $result['passed'] ? 'green' : 'red';
    echo "<tr>";
    echo "<td style='padding: 8px;'>{$result['name']}</td>";
    echo "<td style='color: {$statusColor}; font-weight: bold; padding: 8px;'>{$result['status']}</td>";
    echo "<td style='padding: 8px;'>{$result['message']}</td>";
    echo "</tr>\n";
}

echo "</table>\n";

if ($passedTests === $totalTests) {
    echo "<h2 style='color: green;'>🎉 All Database Tests Passed!</h2>\n";
    echo "<p>All CRUD operations are working correctly and database integrity is maintained.</p>\n";
} else {
    echo "<h2 style='color: red;'>⚠️ Some Database Tests Failed</h2>\n";
    echo "<p>Please review the failed tests and address any database issues.</p>\n";
}

echo "<h3>Database Tables Tested:</h3>\n";
echo "<ul>\n";
echo "<li>✅ site_settings - Configuration management</li>\n";
echo "<li>✅ content_pages - Page content management</li>\n";
echo "<li>✅ events - Event management system</li>\n";
echo "<li>✅ jobs & job_applications - Career management</li>\n";
echo "<li>✅ menu_categories & menu_items - Menu system</li>\n";
echo "<li>✅ Foreign key constraints - Referential integrity</li>\n";
echo "</ul>\n";

echo "<p><em>Database test completed at: " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
