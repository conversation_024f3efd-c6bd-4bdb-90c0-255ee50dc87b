<?php
/**
 * Champions Sports Bar & Grill - Header Template
 */

// Include site configuration
require_once __DIR__ . '/../config.php';

// Set default page title and description if not set
if (!isset($page_title)) {
    $page_title = SiteConfig::get('site_name', 'Champions Sports Bar & Grill') . ' - Brownstown, MI';
}

if (!isset($page_description)) {
    $page_description = SiteConfig::get('site_tagline', 'Champions Sports Bar & Grill in Brownstown, Michigan. Great food, drinks, sports viewing, and outdoor patio with full bar.');
}

if (!isset($current_page)) {
    $current_page = getCurrentPage();
}

// Get dynamic content
$contact_info = SiteConfig::getContactInfo();
$social_media = SiteConfig::getSocialMedia();

// Determine base path for navigation links
$base_path = '';
$current_dir = dirname($_SERVER['PHP_SELF']);
if ($current_dir !== '/') {
    $base_path = '../';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title); ?></title>
    
    <!-- Meta Tags for SEO -->
    <meta name="description" content="<?php echo htmlspecialchars($page_description); ?>">
    <meta name="keywords" content="sports bar, restaurant, Brownstown MI, outdoor patio, full bar, sports viewing, Michigan restaurant, American food, craft beer, live sports, private events, Brownstown Charter Township">
    <meta name="author" content="Champions Sports Bar & Grill">
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <meta name="googlebot" content="index, follow">
    <meta name="bingbot" content="index, follow">
    <meta name="geo.region" content="US-MI">
    <meta name="geo.placename" content="Brownstown Charter Township">
    <meta name="geo.position" content="42.1687;-83.1756">
    <meta name="ICBM" content="42.1687, -83.1756">
    <meta name="language" content="en-US">
    <meta name="distribution" content="global">
    <meta name="rating" content="general">
    <meta name="revisit-after" content="7 days">
    <link rel="canonical" href="<?php echo 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo isset($page_title) ? $page_title : 'Champions Sports Bar & Grill'; ?>">
    <meta property="og:description" content="<?php echo isset($page_description) ? $page_description : 'Great food, drinks, and sports in Brownstown, Michigan'; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>">
    <meta property="og:image" content="<?php echo 'https://' . $_SERVER['HTTP_HOST']; ?>/assets/images/champions-logo.jpg">
    <meta property="og:site_name" content="Champions Sports Bar & Grill">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo isset($page_title) ? $page_title : 'Champions Sports Bar & Grill'; ?>">
    <meta name="twitter:description" content="<?php echo isset($page_description) ? $page_description : 'Great food, drinks, and sports in Brownstown, Michigan'; ?>">
    <meta name="twitter:image" content="<?php echo 'https://' . $_SERVER['HTTP_HOST']; ?>/assets/images/champions-logo.jpg">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo $base_path; ?>assets/images/favicon.ico">
    <link rel="apple-touch-icon" href="<?php echo $base_path; ?>assets/images/apple-touch-icon.png">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- Fancybox CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo $base_path; ?>assets/css/style.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Oswald:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Restaurant",
        "name": "Champions Sports Bar & Grill",
        "alternateName": "Champions Sports Bar",
        "image": [
            "<?php echo 'https://' . $_SERVER['HTTP_HOST']; ?>/assets/images/champions-logo.jpg",
            "<?php echo 'https://' . $_SERVER['HTTP_HOST']; ?>/assets/images/outdoor-patio.jpg",
            "<?php echo 'https://' . $_SERVER['HTTP_HOST']; ?>/assets/images/gallery/interior-1.jpg"
        ],
        "description": "Sports bar and restaurant in Brownstown, Michigan featuring great food, drinks, and outdoor patio with full bar. Watch your favorite games while enjoying delicious American cuisine.",
        "address": {
            "@type": "PostalAddress",
            "streetAddress": "22112 Sibley Road",
            "addressLocality": "Brownstown Charter Township",
            "addressRegion": "MI",
            "postalCode": "48183",
            "addressCountry": "US"
        },
        "geo": {
            "@type": "GeoCoordinates",
            "latitude": 42.1687,
            "longitude": -83.1756
        },
        "telephone": "******-284-7000",
        "email": "<EMAIL>",
        "url": "<?php echo 'https://' . $_SERVER['HTTP_HOST']; ?>",
        "servesCuisine": ["American", "Bar Food", "Sports Bar"],
        "priceRange": "$$",
        "paymentAccepted": ["Cash", "Credit Card", "Debit Card"],
        "currenciesAccepted": "USD",
        "openingHours": [
            "Mo-Th 11:00-00:00",
            "Fr-Sa 11:00-02:00",
            "Su 11:00-00:00"
        ],
        "openingHoursSpecification": [
            {
                "@type": "OpeningHoursSpecification",
                "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday"],
                "opens": "11:00",
                "closes": "00:00"
            },
            {
                "@type": "OpeningHoursSpecification",
                "dayOfWeek": ["Friday", "Saturday"],
                "opens": "11:00",
                "closes": "02:00"
            },
            {
                "@type": "OpeningHoursSpecification",
                "dayOfWeek": "Sunday",
                "opens": "11:00",
                "closes": "00:00"
            }
        ],
        "hasMenu": "<?php echo 'https://' . $_SERVER['HTTP_HOST']; ?>/menu",
        "acceptsReservations": true,
        "amenityFeature": [
            {
                "@type": "LocationFeatureSpecification",
                "name": "Outdoor Seating",
                "value": true
            },
            {
                "@type": "LocationFeatureSpecification",
                "name": "Full Bar",
                "value": true
            },
            {
                "@type": "LocationFeatureSpecification",
                "name": "Sports Viewing",
                "value": true
            },
            {
                "@type": "LocationFeatureSpecification",
                "name": "Private Events",
                "value": true
            }
        ],
        "sameAs": [
            "https://www.facebook.com/championssportsgrill",
            "https://www.instagram.com/champions_sg",
            "https://twitter.com/championssportsgrill"
        ]
    }
    </script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="<?php echo $base_path; ?>index.php">
                <img src="<?php echo $base_path; ?>assets/images/logo.png" alt="Champions Logo" height="40" class="me-2">
                Champions Sports Bar
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($current_page == 'home') ? 'active' : ''; ?>" href="<?php echo $base_path; ?>index.php">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($current_page == 'menu') ? 'active' : ''; ?>" href="<?php echo $base_path; ?>menu/">Menu</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($current_page == 'gallery') ? 'active' : ''; ?>" href="<?php echo $base_path; ?>gallery/">Gallery</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($current_page == 'events') ? 'active' : ''; ?>" href="<?php echo $base_path; ?>events/">Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($current_page == 'careers') ? 'active' : ''; ?>" href="<?php echo $base_path; ?>careers/">Careers</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($current_page == 'contact') ? 'active' : ''; ?>" href="<?php echo $base_path; ?>contact/">Contact</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="tel:+17342847000">
                            <i class="fas fa-phone me-1"></i> (*************
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="main-content">
