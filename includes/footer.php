    </main>

    <?php
    // Determine base path for footer links (same logic as header)
    if (!isset($base_path)) {
        $base_path = '';
        $current_dir = dirname($_SERVER['PHP_SELF']);
        if ($current_dir !== '/') {
            $base_path = '../';
        }
    }
    ?>

    <!-- Footer -->
    <footer class="footer bg-dark text-white py-5">
        <div class="container">
            <div class="row g-4">
                <!-- Contact Information -->
                <div class="col-lg-4">
                    <h5 class="fw-bold mb-3">Contact Information</h5>
                    <div class="contact-info">
                        <p class="mb-2">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            <?php echo htmlspecialchars($contact_info['address']['street']); ?><br>
                            <span class="ms-4"><?php echo htmlspecialchars($contact_info['address']['city'] . ', ' . $contact_info['address']['state'] . ' ' . $contact_info['address']['zip']); ?></span>
                        </p>
                        <p class="mb-2">
                            <i class="fas fa-phone me-2"></i>
                            <a href="tel:<?php echo preg_replace('/[^0-9]/', '', $contact_info['phone']); ?>" class="text-white text-decoration-none"><?php echo htmlspecialchars($contact_info['phone']); ?></a>
                        </p>
                        <p class="mb-2">
                            <i class="fas fa-envelope me-2"></i>
                            <a href="mailto:<?php echo htmlspecialchars($contact_info['email']); ?>" class="text-white text-decoration-none"><?php echo htmlspecialchars($contact_info['email']); ?></a>
                        </p>
                    </div>
                </div>
                
                <!-- Hours -->
                <div class="col-lg-4">
                    <h5 class="fw-bold mb-3">Hours of Operation</h5>
                    <div class="hours-info">
                        <?php
                        $business_hours = SiteConfig::getBusinessHours();
                        $formatted_hours = formatBusinessHours($business_hours);

                        // Group similar hours
                        $mon_thu = $formatted_hours['Monday'] ?? '11:00 AM - 12:00 AM';
                        $fri_sat = $formatted_hours['Friday'] ?? '11:00 AM - 2:00 AM';
                        $sunday = $formatted_hours['Sunday'] ?? '11:00 AM - 12:00 AM';
                        ?>
                        <div class="d-flex justify-content-between mb-1">
                            <span>Monday - Thursday:</span>
                            <span><?php echo htmlspecialchars($mon_thu); ?></span>
                        </div>
                        <div class="d-flex justify-content-between mb-1">
                            <span>Friday - Saturday:</span>
                            <span><?php echo htmlspecialchars($fri_sat); ?></span>
                        </div>
                        <div class="d-flex justify-content-between mb-1">
                            <span>Sunday:</span>
                            <span><?php echo htmlspecialchars($sunday); ?></span>
                        </div>
                        <small class="text-muted">Kitchen closes 1 hour before bar</small>
                    </div>
                </div>
                
                <!-- Quick Links & Social -->
                <div class="col-lg-4">
                    <h5 class="fw-bold mb-3">Quick Links</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="<?php echo $base_path; ?>index.php" class="text-white text-decoration-none">Home</a></li>
                        <li class="mb-2"><a href="<?php echo $base_path; ?>menu/" class="text-white text-decoration-none">Menu</a></li>
                        <li class="mb-2"><a href="<?php echo $base_path; ?>gallery/" class="text-white text-decoration-none">Photo Gallery</a></li>
                        <li class="mb-2"><a href="<?php echo $base_path; ?>events/" class="text-white text-decoration-none">Events & Happenings</a></li>
                        <li class="mb-2"><a href="<?php echo $base_path; ?>careers/" class="text-white text-decoration-none">Careers</a></li>
                        <li class="mb-2"><a href="<?php echo $base_path; ?>contact/" class="text-white text-decoration-none">Contact Us</a></li>
                    </ul>
                    
                    <!-- Social Media -->
                    <div class="social-links mt-3">
                        <h6 class="fw-bold mb-2">Follow Us</h6>
                        <?php if (!empty($social_media['instagram'])): ?>
                        <a href="<?php echo htmlspecialchars($social_media['instagram']); ?>" target="_blank" class="text-white me-3" aria-label="Instagram">
                            <i class="fab fa-instagram fa-2x"></i>
                        </a>
                        <?php endif; ?>
                        <?php if (!empty($social_media['facebook'])): ?>
                        <a href="<?php echo htmlspecialchars($social_media['facebook']); ?>" target="_blank" class="text-white me-3" aria-label="Facebook">
                            <i class="fab fa-facebook fa-2x"></i>
                        </a>
                        <?php endif; ?>
                        <?php if (!empty($social_media['twitter'])): ?>
                        <a href="<?php echo htmlspecialchars($social_media['twitter']); ?>" target="_blank" class="text-white me-3" aria-label="Twitter">
                            <i class="fab fa-twitter fa-2x"></i>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <hr class="my-4">
            
            <!-- Copyright -->
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; <?php echo date('Y'); ?> <?php echo htmlspecialchars(SiteConfig::get('site_name', 'Champions Sports Bar & Grill')); ?>. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <a href="privacy-policy.php" class="text-white text-decoration-none me-3">Privacy Policy</a>
                        <a href="terms-of-service.php" class="text-white text-decoration-none">Terms of Service</a>
                    </p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Back to Top Button -->
    <button id="backToTop" class="btn btn-primary position-fixed bottom-0 end-0 m-4 rounded-circle" style="display: none; z-index: 1000;">
        <i class="fas fa-arrow-up"></i>
    </button>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Fancybox JS -->
    <script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.umd.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="assets/js/main.js"></script>
    
    <!-- Google Analytics (replace with actual tracking ID) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=GA_TRACKING_ID"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'GA_TRACKING_ID');
    </script>
</body>
</html>
