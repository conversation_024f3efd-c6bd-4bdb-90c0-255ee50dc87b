<?php
/**
 * Champions Sports Bar Website - Error Handler
 * 
 * Comprehensive error handling and logging system
 */

class ErrorHandler {
    private static $instance = null;
    private $logFile;
    private $errorLogFile;
    private $debugMode;
    
    private function __construct() {
        $this->logFile = __DIR__ . '/../logs/application.log';
        $this->errorLogFile = __DIR__ . '/../logs/error.log';
        $this->debugMode = defined('DEBUG_MODE') ? DEBUG_MODE : false;
        
        // Create logs directory if it doesn't exist
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        // Set up error handlers
        $this->setupErrorHandlers();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Set up PHP error handlers
     */
    private function setupErrorHandlers() {
        // Set error handler
        set_error_handler([$this, 'handleError']);
        
        // Set exception handler
        set_exception_handler([$this, 'handleException']);
        
        // Set fatal error handler
        register_shutdown_function([$this, 'handleFatalError']);
        
        // Configure error reporting
        if ($this->debugMode) {
            error_reporting(E_ALL);
            ini_set('display_errors', 1);
        } else {
            error_reporting(E_ALL & ~E_NOTICE & ~E_STRICT & ~E_DEPRECATED);
            ini_set('display_errors', 0);
        }
        
        ini_set('log_errors', 1);
        ini_set('error_log', $this->errorLogFile);
    }
    
    /**
     * Handle PHP errors
     */
    public function handleError($severity, $message, $file, $line) {
        // Don't handle errors that are suppressed with @
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        $errorType = $this->getErrorType($severity);
        $errorMessage = "[$errorType] $message in $file on line $line";
        
        $this->logError($errorMessage, [
            'type' => $errorType,
            'severity' => $severity,
            'file' => $file,
            'line' => $line,
            'message' => $message
        ]);
        
        // Don't execute PHP internal error handler
        return true;
    }
    
    /**
     * Handle uncaught exceptions
     */
    public function handleException($exception) {
        $errorMessage = "Uncaught Exception: " . $exception->getMessage() . 
                       " in " . $exception->getFile() . 
                       " on line " . $exception->getLine();
        
        $this->logError($errorMessage, [
            'type' => 'Exception',
            'class' => get_class($exception),
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString()
        ]);
        
        // Display user-friendly error page
        $this->displayErrorPage('An unexpected error occurred. Please try again later.');
    }
    
    /**
     * Handle fatal errors
     */
    public function handleFatalError() {
        $error = error_get_last();
        
        if ($error !== null && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
            $errorMessage = "Fatal Error: " . $error['message'] . 
                           " in " . $error['file'] . 
                           " on line " . $error['line'];
            
            $this->logError($errorMessage, [
                'type' => 'Fatal Error',
                'message' => $error['message'],
                'file' => $error['file'],
                'line' => $error['line']
            ]);
            
            // Display user-friendly error page
            $this->displayErrorPage('A critical error occurred. Please contact support.');
        }
    }
    
    /**
     * Log application events
     */
    public function logInfo($message, $context = []) {
        $this->writeLog('INFO', $message, $context);
    }
    
    /**
     * Log warnings
     */
    public function logWarning($message, $context = []) {
        $this->writeLog('WARNING', $message, $context);
    }
    
    /**
     * Log errors
     */
    public function logError($message, $context = []) {
        $this->writeLog('ERROR', $message, $context);
    }
    
    /**
     * Log debug information
     */
    public function logDebug($message, $context = []) {
        if ($this->debugMode) {
            $this->writeLog('DEBUG', $message, $context);
        }
    }
    
    /**
     * Write log entry
     */
    private function writeLog($level, $message, $context = []) {
        $timestamp = date('Y-m-d H:i:s');
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        $requestUri = $_SERVER['REQUEST_URI'] ?? 'unknown';
        
        $logEntry = [
            'timestamp' => $timestamp,
            'level' => $level,
            'message' => $message,
            'ip' => $ip,
            'user_agent' => $userAgent,
            'request_uri' => $requestUri,
            'context' => $context
        ];
        
        $logLine = $timestamp . " [$level] " . $message;
        if (!empty($context)) {
            $logLine .= " | Context: " . json_encode($context);
        }
        $logLine .= " | IP: $ip | URI: $requestUri" . PHP_EOL;
        
        file_put_contents($this->logFile, $logLine, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Get error type string
     */
    private function getErrorType($severity) {
        switch ($severity) {
            case E_ERROR:
                return 'Fatal Error';
            case E_WARNING:
                return 'Warning';
            case E_PARSE:
                return 'Parse Error';
            case E_NOTICE:
                return 'Notice';
            case E_CORE_ERROR:
                return 'Core Error';
            case E_CORE_WARNING:
                return 'Core Warning';
            case E_COMPILE_ERROR:
                return 'Compile Error';
            case E_COMPILE_WARNING:
                return 'Compile Warning';
            case E_USER_ERROR:
                return 'User Error';
            case E_USER_WARNING:
                return 'User Warning';
            case E_USER_NOTICE:
                return 'User Notice';
            case E_STRICT:
                return 'Strict Standards';
            case E_RECOVERABLE_ERROR:
                return 'Recoverable Error';
            case E_DEPRECATED:
                return 'Deprecated';
            case E_USER_DEPRECATED:
                return 'User Deprecated';
            default:
                return 'Unknown Error';
        }
    }
    
    /**
     * Display user-friendly error page
     */
    private function displayErrorPage($message) {
        // Don't output anything if headers already sent
        if (headers_sent()) {
            return;
        }
        
        http_response_code(500);
        
        if ($this->debugMode) {
            // Show detailed error in debug mode
            echo "<h1>Error</h1><p>$message</p>";
        } else {
            // Show generic error page in production
            include __DIR__ . '/../error_pages/500.php';
        }
        
        exit;
    }
    
    /**
     * Handle database errors
     */
    public function handleDatabaseError($error, $query = '') {
        $message = "Database Error: " . $error;
        if ($query) {
            $message .= " | Query: " . $query;
        }
        
        $this->logError($message, [
            'type' => 'Database Error',
            'error' => $error,
            'query' => $query
        ]);
        
        if ($this->debugMode) {
            throw new Exception($message);
        } else {
            $this->displayErrorPage('A database error occurred. Please try again later.');
        }
    }
    
    /**
     * Handle form validation errors
     */
    public function handleValidationError($field, $message, $value = '') {
        $this->logWarning("Validation Error: $field - $message", [
            'type' => 'Validation Error',
            'field' => $field,
            'message' => $message,
            'value' => $value
        ]);
    }
    
    /**
     * Handle file upload errors
     */
    public function handleFileUploadError($error, $filename = '') {
        $errorMessages = [
            UPLOAD_ERR_INI_SIZE => 'File too large (exceeds upload_max_filesize)',
            UPLOAD_ERR_FORM_SIZE => 'File too large (exceeds MAX_FILE_SIZE)',
            UPLOAD_ERR_PARTIAL => 'File upload was interrupted',
            UPLOAD_ERR_NO_FILE => 'No file was uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'File upload stopped by extension'
        ];
        
        $message = $errorMessages[$error] ?? 'Unknown upload error';
        
        $this->logError("File Upload Error: $message", [
            'type' => 'File Upload Error',
            'error_code' => $error,
            'filename' => $filename,
            'message' => $message
        ]);
    }
    
    /**
     * Handle security violations
     */
    public function handleSecurityViolation($type, $details = []) {
        $message = "Security Violation: $type";
        
        $this->logError($message, array_merge([
            'type' => 'Security Violation',
            'violation_type' => $type
        ], $details));
        
        // In production, you might want to block the IP or take other actions
        if (!$this->debugMode) {
            http_response_code(403);
            include __DIR__ . '/../error_pages/403.php';
            exit;
        }
    }
    
    /**
     * Get recent log entries
     */
    public function getRecentLogs($lines = 100) {
        if (!file_exists($this->logFile)) {
            return [];
        }
        
        $logs = [];
        $file = new SplFileObject($this->logFile);
        $file->seek(PHP_INT_MAX);
        $totalLines = $file->key();
        
        $startLine = max(0, $totalLines - $lines);
        $file->seek($startLine);
        
        while (!$file->eof()) {
            $line = trim($file->current());
            if (!empty($line)) {
                $logs[] = $line;
            }
            $file->next();
        }
        
        return $logs;
    }
    
    /**
     * Clear old log files
     */
    public function clearOldLogs($days = 30) {
        $files = [$this->logFile, $this->errorLogFile];
        
        foreach ($files as $file) {
            if (file_exists($file) && filemtime($file) < strtotime("-$days days")) {
                unlink($file);
                $this->logInfo("Cleared old log file: $file");
            }
        }
    }
}

// Initialize error handler
ErrorHandler::getInstance();
?>
