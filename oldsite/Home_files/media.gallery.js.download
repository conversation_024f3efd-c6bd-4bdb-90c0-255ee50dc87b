define(["designer/util/util.instances","designer/util/util.model","common/util/documentHelper","jquery"],function(i,t,e,s){function a(i,e){this.$element=i,this.$slider=s("."+n+"-slider",this.$element),this.model=new t(e),this.mode=this.model.get("mode"),this.source=this.model.get("source")||"default",this.loaded=this.subscriptions=[],this.timer=this.animating=this.paginationType=this.arrowsType=!1,this.$pagination=this.$paginationArrows=this.$arrows=this.$layout=this.$overlay=!1,this.preloadAmount=3,this.isInitializing=!0,this.init()}var n="wsb-media-gallery",o={None:!1,Default:"pagination",Thumbnail:"pagination-thumbs"},r={None:!1,Default:"bordered"},h={None:!1,Default:"arrows",Hover:"arrows-hover"},l=[{pagination:o.None,border:r.Default,arrows:h.Default},{pagination:o.Default,border:r.None,arrows:h.None},{pagination:o.Default,border:r.Default,arrows:h.Default},{pagination:o.Default,border:r.None,arrows:h.Default},{pagination:o.Thumbnail,border:r.None,arrows:h.Hover}],d={pagination:o.Default,border:r.Default,arrows:h.Default},p=function(i){return i<l.length?l[i]:d};a.prototype={init:function(){var i=this;if(this.model.isKO){var t=function(){i.refresh(),i.isInitializing&&(i.isInitializing=!1)};this.subscriptions.push(this.model.get("localizedAssets",!0).subscribe(function(){i.isInitializing=!0,t()}));var e=this.model.get("mutatorViewModel",!0);if(e)for(var s in e)this.model.ko.isObservable(e[s])&&this.subscriptions.push(e[s].subscribe(t))}this.refresh()},"goto":function(i,t){if(!this.animating&&this.assets[i]){var e=this;this.animating=!0,clearTimeout(this.timer),this.preloadFrom(i);var a=0,r=this.model.get("GalleryTransition"),h=this.$cur||!1;h&&(a=s("li",this.$slider).index(h));var l=s("li:eq("+i+")",this.$slider),d=!1;this.$cur?a===i&&(r="None"):(this.$cur=h=s("li:eq("+i+")",this.$slider),this.zindex=this.model.get("Layer"),d=!0,t="next");var g=this.zindex;t||(t=i>=a?"next":"prev");var c=function(){if(e.$cur=l,e.animating=!1,e.model.get("GalleryAutoStart")||!d&&"homefinder"!==e.source){var t,s=i+1,a=e.assets.length;s>=a&&(s=0,t="next"),e.timer=setTimeout(function(){e.goto(s,t)},1e3*(parseFloat(e.model.get("GallerySpeed"))||1))}};switch(p(e.model.get("GalleryTheme")).pagination!==o.None&&e.$pagination&&e.$pagination.length&&e.$pagination.trigger("paginate."+n,i),r){case"FirstTime":l.css("left",0).fadeIn("slow",function(){c()});break;case"None":l.css({left:0,"z-index":g+1}).show(),d||a===i||h.css("z-index",g).hide(),c();break;case"Fade":l.css({left:0,"z-index":g+1}).hide().fadeIn("slow",function(){c()}),d||h.fadeOut("slow");break;case"Slide":var u=h.width();d?l.css({left:"prev"===t?-u:u,"z-index":g+1}).show().animate({left:0},500,function(){c()}):(h.css("z-index",g),l.css({left:"prev"===t?-u:u,"z-index":g+1}).show().animate({left:0},{duration:500,queue:!1}),h.animate({left:"prev"===t?u:-u},500,function(){h.hide(),c()}));break;case"Slide Vertically":var f=h.height();d?l.css({top:"prev"===t?-f:f,left:0,"z-index":g+1}).show().animate({top:0},500,function(){c()}):(h.css("z-index",g),l.css({top:"prev"===t?-f:f,left:0,"z-index":g+1}).show().animate({top:0},{duration:500,queue:!1}),h.animate({top:"prev"===t?f:-f},500,function(){h.hide(),c()}));break;case"Stack":var u=h.width();d?l.css({left:"prev"===t?-u:u,"z-index":g+1}).show().animate({left:0},500,function(){c()}):(h.css("z-index",g),l.css({left:"prev"===t?-u:u,"z-index":g+1}).show().animate({left:0},{duration:500,queue:!1}),h.fadeOut(500,function(){c()}));break;case"Stack Vertically":var f=h.height();d?l.css({top:"prev"===t?-f:f,left:0,"z-index":g+1}).show().animate({top:0},500,function(){c()}):(h.css("z-index",g),l.css({top:"prev"===t?-f:f,left:0,"z-index":g+1}).show().animate({top:0},{duration:500,queue:!1}),h.fadeOut(500,function(){c()}))}}},refresh:function(){var i=this,t=this.assets=this.model.get("localizedAssets")||this.model.get("GalleryAssets"),e=this.model.get("GalleryTheme"),a=p(e),l=this.model.get("effects"),d=l?l[parseInt(e)]:!1,g=d&&d.className?d.className:!1;this.model.isKO&&!i.isInitializing&&this.model.set("className",g),this.loaded=[],this.animating=!0,clearTimeout(this.timer);var c=this.width=parseInt(this.model.get("Width").replace("px","")),u=this.model.get("Height");if("mobile"==this.mode){var f=this.$element.width(),m=f/c;1!=m&&(c=this.width=f,u=parseInt(u.replace("px",""))*m)}this.$element.css({height:u,width:c}),this.arrowsType=a.arrows,this.arrowsType===h.Default?(this.arrowsRefresh(),c=this.width=c-44,this.$slider.css("margin-left",22)):this.arrowsType===h.Hover?(this.arrowsRefresh(),this.$slider.css("margin-left",0)):(this.arrowsRemove(),this.$slider.css("margin-left",0)),a.border===r.Default?(this.$slider.addClass(r.Default),c=this.width=c-30):this.$slider.removeClass(r.Default),this.$cur=!1,this.$slider.empty().css("width",c);for(var v=0;v<t.length;v++)this.$slider.append(s('<li class="loading"/>').css({width:this.width,left:this.width})),this.loaded[v]=!1;if(this.paginationType=a.pagination,this.paginationType!==o.None?(this.paginationRefresh(),"mobile"===this.mode?(this.$slider.css("height",u),this.$element.css(this.paginationType==o.Thumbnail?{height:parseInt(u)+this.$paginationArrows.outerHeight(!0)+5+"px"}:{height:parseInt(u)+this.$pagination.outerHeight(!0)+"px"})):this.$slider.css("height",this.$element.height()-this.$pagination.outerHeight(!0))):(this.paginationRemove(),this.$slider.css("height",this.$element.height())),a.border===r.Default&&this.$slider.css("height",this.$slider.height()-30),this.arrowsType===h.Default?this.$arrows.css("top",(this.$slider.outerHeight(!0)-44)/2):this.arrowsType===h.Hover&&this.$arrows.height(this.$slider.outerHeight(!0)).css("z-index",this.model.get("Layer")+2),"designer"==this.mode){var w=this.zindex||this.model.get("Layer");this.$overlay&&this.$overlay.length||(this.$overlay=s("<div/>").addClass(n+"-overlay").appendTo(this.$element)),this.$overlay.css({position:"absolute",top:0,width:this.$element.outerWidth(),height:this.$element.outerHeight(),"z-index":w+2}).children().remove()}this.load(0,function(){i.animating=!1,i.goto(0)})},getAutofitCSS:function(i,t){var e,s=i.width(),a=i.height(),n=a/s,o=t.width(),r=t.height(),h=r/o,l=0,d=0,p=0;return 1>n?1>h&&n>h?l=o:e=r:n>1?h>1&&h>n?e=r:l=o:1>h?l=o:e=r,e?d=-(s*(e/a)-o)/2:p=-(a*(l/s)-r)/2,{width:l||"auto",height:e||"auto",left:d,top:p}},load:function(i,t){var a=this,r=this.assets[i];if(r&&!this.loaded[i]){this.loaded[i]=!0;var h=s("li:eq("+i+")",this.$slider);switch(r.type){case"video":break;case"image":default:var l=!1,d=!1,p=s("<img/>").bind("load",function(){var e=h.removeClass("loading");if(r.link){if(e=s("<a/>").attr({title:r.caption}).appendTo(h),r.link.search("window.wsb.gotoAddress")>=0){var g=r.link.match(/preview\/(?:desktop|mobile)\/(?:(?:[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})|(?:\d+))/)[0];e.attr("href","#"),e.click(function(){window.wsb.gotoAddress(g)})}else e.attr("href",r.link);Boolean(r.link.match(/^((http|https|ftp|mailto):|\/\/)/i))&&e.attr("target","_blank")}if(p.appendTo(e),a.model.get("GalleryAutoSize"))p.css(a.getAutofitCSS(p.addClass("autosize"),h));else{var c=Math.floor((h.height()-p.height())/2);c>0&&p.css("marginTop",c)}l&&d&&(d.addClass("autosize").appendTo(s("<div/>").addClass(n+"-"+o.Thumbnail+"-thumbnail-wrapper").appendTo(l.removeClass("loading"))),d.css(a.getAutofitCSS(d,l))),a.model.get("GalleryCaption")&&r.caption&&e.append(s("<div/>").addClass(n+"-caption").css("width",a.width).text(r.caption)),s.isFunction(t)&&t(i),p.unbind("load")}),g=r.src||e.getDocumentUrl(r.id);p[0].src=g,a.paginationType&&a.paginationType==o.Thumbnail&&a.$pagination&&(l=s("li:eq("+i+")",a.$pagination),d=s("<img/>"),d[0].src=g)}}else s.isFunction(t)&&t(!1)},preloadFrom:function(i){for(var t=this.assets.length,e=i;e<i+this.preloadAmount;e++){var s=e;e>t&&(s=0+(t-e)),this.load(s)}for(var e=i;e>i-this.preloadAmount;e--){var s=e;0>e&&(s=t+e),this.load(s)}},paginationRefresh:function(){var i=this;if(this.paginationRemove(),this.$pagination=s("<ul/>").addClass(n+"-"+this.paginationType).appendTo(this.$element),this.paginationType===o.Thumbnail){var t=this.$pagination.wrap(s("<div/>").addClass(n+"-"+o.Thumbnail+"-wrapper")).parent(),e=s("<div/>").addClass(n+"-"+o.Thumbnail+"-left-arrow"),a=s("<div/>").addClass(n+"-"+o.Thumbnail+"-right-arrow");this.$paginationArrows=e.insertBefore(t).add(a.insertAfter(t));var r=t.width(),h=93,l=2,d=5,p=Math.floor(r/(h+2*l)),g=Math.min(Math.floor((r/p-h)/2),d),c=Math.ceil(p/2);this.preloadAmount=p;for(var u=this.assets.length,f=0;u>f;f++)this.paginationThumbCreate(f,g);var m=s("li:first",this.$pagination).outerWidth(!0),v=r-m*p;t.width(r-=v).css("left",(parseInt(t.css("left").replace("px",""))||0)+Math.floor(v/2)),this.$pagination.width((h+2*d)*u),u>1&&a.addClass("enabled"),this.$paginationArrows.bind("click",function(){var t=s(this);if(!i.animating&&t.hasClass("enabled")){var a,n=i.$cur?s("li",i.$slider).index(i.$cur):0;t.attr("class")==e.attr("class")?(a="prev",n--):(a="next",n++),1>n?n=0:n>=u-1&&(n=u-1),i.goto(n,a)}}),this.$pagination.bind("paginate."+n,function(t,n){s("li",i.$pagination).removeClass("active"),s("li:eq("+n+")",i.$pagination).addClass("active"),1>n?(e.removeClass("enabled"),u>1&&a.addClass("enabled")):n>=u-1?(a.removeClass("enabled"),u>1&&e.addClass("enabled")):(e.addClass("enabled"),a.addClass("enabled")),i.$pagination.animate({left:n>=c?-(m*(n+1-c)):0},500,function(){i.animating=!1})})}else{for(var f=0;f<this.assets.length;f++)this.paginationCreate(f);this.$pagination.bind("paginate."+n,function(t,e){s("li",i.$pagination).removeClass("active"),s("li:eq("+e+")",i.$pagination).addClass("active")})}},paginationCreate:function(i){var t=this;this.$pagination.append(s("<li/>").bind("click",function(){t.animating||t.goto(i)}))},paginationThumbCreate:function(i,t){var e=this;this.$pagination.append(s("<li/>").css({marginLeft:t,marginRight:t}).bind("click",function(){e.animating||e.goto(i)}))},paginationRemove:function(){this.$pagination&&this.$pagination.length&&(this.$pagination.children("li").unbind().remove(),this.$pagination.remove(),this.$paginationArrows&&this.$paginationArrows.length&&(this.$paginationArrows.unbind().remove(),this.$paginationArrows=!1),this.$pagination=!1)},arrowsRefresh:function(){this.arrowsRemove();var i=this,t=s("<div/>").addClass(n+"-"+this.arrowsType+"-left-arrow"),e=s("<div/>").addClass(n+"-"+this.arrowsType+"-right-arrow");this.$arrows=t.insertBefore(this.$slider).add(e.insertAfter(this.$slider)),this.$arrows.bind("click",function(){if(!i.animating){var e,a=s(this),n=i.$cur?s("li",i.$slider).index(i.$cur):0,o=i.assets.length;a.attr("class")==t.attr("class")?(e="prev",n--):(e="next",n++),0>n?n=o-1:n>=o&&(n=0),i.goto(n,e)}}),this.arrowsType===h.Hover&&this.$arrows.css("opacity",0).bind("mouseenter",function(){s(this).stop().fadeTo(100,1)}).bind("mouseleave",function(){s(this).stop().fadeTo(100,0)})},arrowsRemove:function(){this.$arrows&&this.$arrows.length&&(this.$arrows.unbind().remove(),this.$arrows=!1)},destroy:function(){for(var i in this.subscriptions)this.subscriptions[i].dispose();clearTimeout(this.timer),this.paginationRemove(),this.arrowsRemove(),g.destroy(this.$element),this.$element.remove()}};var g=new i(n,a);return{render:function(i,t){var e=g.get(i);return e?e.refresh():e=g.create(i,t),e},destroy:function(i){var t=g.get(i);return t?(g.destroy(i),!0):!1}}});
//# sourceMappingURL=media.gallery.js.map