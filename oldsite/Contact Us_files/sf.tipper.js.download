/*! duel v2.5.8 - Built 2023-06-07, 3:14 PM MST - Copyright (c) 2023 */
define("starfield/sf.tipper",["i18n!starfield/sf.i18n/duel","css!starfield/sf.core.css","starfield/sf.util","jq!starfield/jquery.mod"],function(a){return function(b){function c(a,c){var d=a;0==d.is(".sf_tipper,.sf-tip")&&(d=b(".sf_tipper,.sf-tip",a)),d.each(function(){var a=b(this),d=a.attr("data-title");("string"!=typeof d||0==d.length)&&(d=a.attr("data-tipper-title"));var e=a.attr("data-content");("string"!=typeof e||0==e.length)&&(e=a.attr("data-tipper-content"));var f=a.attr("data-style");("string"!=typeof f||0==f.length)&&(f=a.attr("data-tipper-style"));var g=a.attr("data-location");("string"!=typeof g||0==g.length)&&(g=a.attr("data-tipper-location"));var h=a.attr("data-width");("string"!=typeof h||0==h.length)&&(h=a.attr("data-tipper-width"));var i=a.attr("data-target");("string"!=typeof i||0==i.length)&&(i=a.attr("data-tipper-target")),i="string"!=typeof i||0==i.length?void 0:b(i),a.sfTipper(c,{style:f,location:g,title:d,content:e,width:h,target:i,initOnly:!0})})}return b.fn.sfTipper=function(d,e){var f=b.extend({wireup:!1},d),g=b(this);if(f.wireup===!0)return delete d.wireup,c(g,d),g;var h=g.data("sf-tipper-id");h&&0!=h.length||(h=Math.round(1e6*Math.random()),g.data("sf-tipper-id",h));var i=b.fn.sfTipper.instances[h];return null!=i?(i.me=g,i.fn(i,d,e)):(i=b.fn.sfTipper.instances[h]={me:g,meId:h,init:!1,settings:null,fn:function(c,d,e){function f(){return"tooltip"==m.style?"tipper":m.style}function i(){m.resize=!0,q[m.style].positionIt(q[m.style].getElements(g))}function j(a){var c=b(a.target);m.resize=!0,q[m.style].positionIt(q[m.style].getElements(g));var d=c.offset().top,e=d+c.height(),f=g.offset().top;(d>=f||f>=e)&&l(a)}function k(a,c){var d=a.indexOf("<!--more-->");if(d>-1){var e=a.substring(0,d),h=a.substring(d+11);if(a=e+'<div class="sf-tipper-more" style="display:none;">'+h+'</div><div class="sf-tipper-morelink"><a href="javascript: void(0);" onclick="return false;">'+m.showMoreText+"</a></div>",h.indexOf("sf-tourpoint-nav")>-1){var i=b("<div>"+a+"</div>"),j=i.find(".sf-tourpoint-nav");j.insertAfter(i.find(".sf-tipper-morelink")),a=i.html()}}c.html(a),c.find(".sf-tipper-morelink a").bind("click",function(){var a=b(this),d=c.find(".sf-tipper-more");if(d.length>0){d.is(":visible")?(d.hide(),a.text(m.showMoreText)):(d.show(),a.text(m.showLessText));var e=f();q[e].positionIt(q[e].getElements(g))}})}function l(a){var c=!1,d=!1;if(a&&a.target){var e=b(a.target);if(e.is(".sf-tipper-close")===!1&&e.closest(".sf-"+f()).length>0&&e.is("a")===!1){if(e.is(".sf-tourpoint-nav-item"))return!1;if(!(e.parent(".sf-tourpoint-nav").length>0))return!1;c=!0}else if(e.is("a")&&e.parent().is(".sf-tipper-morelink"))return d=!0,!0}return 1!=b.isFunction(m.onClose)||m.onClose(g)?(r.fadeTipper(void 0,void 0,c),!0):!1}var m=($sf.util.logger(h.toString(),"duel"),b.extend({style:"tooltip",title:"",content:"",location:"auto",close:!1,closeTippers:void 0,initOnly:!1,onClose:null,onOpen:null,onDisplay:null,autoOpen:!1,context:self,displayContext:self,width:void 0,disableClose:!1,disableFade:!1,attachIcon:!0,wireup:!1,zIndex:null,suppress:!1,suppressTippers:void 0,resume:!1,resumeTippers:void 0,destroy:!1,destroyTippers:void 0,target:void 0,showMoreText:a["sf.tipper"].more,showLessText:a["sf.tipper"].less},c.settings,d,e));c.settings=m,c.defaultWidths={qt:180,tooltip:213,tourpoint:213,howl:400};var n=m.context,o=m.displayContext,p=$sf.util.context(n,o),q={tooltip:{build:function(){return q.tipbox.build("tipper")},getElements:function(a){return q.tipbox.getElements(a)},positionIt:function(a){return q.tipbox.positionIt(a)}},tourpoint:{build:function(){return q.tipbox.build("tourpoint")},getElements:function(a){return q.tipbox.getElements(a)},positionIt:function(a){return q.tipbox.positionIt(a)}},tipbox:{build:function(a){var d=b(o.document.createElement("div")).data("sf-tipper",g.data("sf-tipper")).data("sf-tipper-target",g).attr("class","sf-"+a).attr("data-style",a);m.groupId&&d.attr("data-group",m.groupId),m.zIndex&&d.css("z-index",m.zIndex),c.outside=d,$sf.util.zIndex.getLayer("flyout",b("body:first",p.ext.document()),p.isSingle()?g:p.ext.iframe()).append(d),d.data("OnResize",i),d.data("OnScroll",j),d.data("OnClose",l);var e=b("<div class='sf-tipper-close sf-icn-close2 sf-icn' />"),f=parseInt(m.width);f=isNaN(f)||0===f?c.defaultWidths[a]:f;var h=b("<div class='sf-"+a+"-wrap' />").css("width",f+"px");c.master=h,0==m.disableClose&&h.append(e),d.append(h);var n=b("<div class='sf-"+a+"-body' />");if(h.append(n),"tourpoint"==a&&m.heading){var q=b("<div class='sf-"+a+"-titlebarheading' />");n.append(q),c.heading=q,q.html(m.heading)}else c.heading=null;if(m.title){var q=b("<h6 class='sf-"+a+"-heading' />");n.append(q),c.title=q,q.html(m.title)}else c.title=null;if(m.content){var r=b("<div class='sf-"+a+"-content' />");n.append(r),c.content=r,k(m.content,r)}var s=b(document.createElement("div"));return c.arrow=s,"howl"===a&&s.addClass("sf-"+a+"-arrow-wrap"),d.append(s),{container:d,masterContainer:h,arrowDiv:s}},getElements:function(a){return{container:c.outside,masterContainer:c.master,arrowDiv:c.arrow}},positionIt:function(a){if(a&&a.masterContainer&&a.masterContainer.length){var b="undefined"==typeof m.target?g:m.target,c="sf-"+f()+"-ptr",d="auto"===m.location?"tl":(m.location.indexOf("top")>-1?"b":"t")+(m.location.indexOf("left")>-1?"r":"l");a.arrowDiv.attr("class",c),a.container.width(a.masterContainer.outerWidth()+a.arrowDiv.width()-1);var e=$sf.util.position.get({ideal:d,container:a.container,anchor:b,path:"corner",anchorOffset:p.isSingle()?{t:0,l:0}:p.ext.iframe().offset(),containerOffset:{width:function(){return 3},height:function(){return 0}},centerAgainstAnchor:!0,context:p.ext.window()});a.arrowDiv.addClass(c+"-"+("t"===e.y?"bottom":"top")+("l"===e.x?"right":"left")),a.masterContainer.css({"float":"r"===e.x?"left":"right"}),a.container.css({left:e.coords.left+"px",top:e.coords.top+"px"})}}},qt:{buildContainer:function(){return q.bubble.build("qt")},getElements:function(a){return q.bubble.getElements(a)},load:function(a,b){return q.bubble.load("qt",a,b)},positionIt:function(a){return q.bubble.positionIt(a,"qt","bottom"===m.location?"bottom":"top")}},howl:{buildContainer:function(){return q.bubble.build("howl")},getElements:function(a){return q.bubble.getElements(a)},load:function(a,b){return q.bubble.load("howl",a,b)},build:function(a){var b=q.bubble.buildContainer("howl");return b.data("sf-tipper",g.data("sf-tipper")).data("OnResize",i).data("OnClose",l),c.outside=b,q.bubble.load("howl",b,a),{container:b,el:a}},positionIt:function(a){return q.bubble.positionIt(a,"howl","top"===m.location?"top":"bottom")}},bubble:{buildContainer:function(a){var c=b("body > .sf-"+a+":first",o.document);if(0==c.length){var d=m.zIndex?' style="position: absolute; z-index: '+m.zIndex+'"':"";if(c=b('<div class="sf-'+a+'"'+d+' data-style="'+a+'"><span class="sf-'+a+'-content"></span><span class="sf-'+a+'-arrow-wrap"><span class="sf-'+a+'-arrow"></span></span></div>'),$sf.util.zIndex.getLayer("flyout",b("body:first",p.ext.document()),p.isSingle()?g:p.ext.iframe()).append(c),"howl"==a){var e=b("<div class='sf-tipper-close sf-icn-close2 sf-icn' />");0==m.disableClose&&c.append(e)}}return c},getElements:function(a){return{container:c.outside,el:a}},load:function(a,d,e){d.css({width:"auto",top:0,left:0});var f=e.attr("data-content")?e.attr("data-content"):m.content,g=parseInt(m.width),h=!(isNaN(g)||0>=g),i=h?g:c.defaultWidths[a],j=b("<div>"+f+"</div>").css({"max-width":i,position:"absolute",padding:"0",margin:"0","font-family":"arial, verdana, helvetica, sans-serif","font-size":"11px"}).appendTo(b("body:first",o.document)),k=j.width(),l=k>i?i:k;if(j.remove(),d.find(".sf-"+a+"-content").html(f),"howl"===a){var n=e.attr("data-title")?e.attr("data-title"):""===m.title?null:m.title,p=d.find(".sf-"+a+"-heading");n?p.length<=0?b('<div class="sf-'+a+'-heading">'+n+"</div>").insertBefore(d.find(".sf-"+a+"-content")):p.text(n):p.length>0&&p.remove()}var q=d.width();$sf.util.ua.ie&&$sf.util.ua.major<=6&&"maxWidth"in o.document.body.style?d.css({width:q}):d.css(q>i?{width:i,"max-width":i}:l>q?{width:l,"max-width":l}:{width:q})},positionIt:function(a,b,c){function d(c){var d=a.container.find(".sf-"+b+"-arrow-wrap");d.css({left:a.container.width()/2-c+d.width()/2+"px"})}if(a&&a.container&&a.container.length){var e="undefined"!=typeof m.target?m.target:a.el;c="bottom"!==c?"top":"bottom";var f=$sf.util.position.get({ideal:(c.indexOf("top")>-1?"l":"u")+"c",container:a.container,anchor:e,path:"axis",anchorOffset:p.isSingle()?{t:0,l:0}:p.ext.iframe().offset(),containerOffset:{width:function(){return 0},height:function(){return 2}},slideAgainstAnchor:!0,context:p.ext.window()});a.container.css(f.coords),"u"==f.y?a.container.addClass("sf-"+b+"-below"):a.container.removeClass("sf-"+b+"-below"),d(f.coords.leftAdj,f.coords)}}}},r={initTrigger:function(a){a.hasClass("sf-tipper-target")||(a.addClass("sf-tipper-target").data("sf-tipper-style",m.style),$sf.util.event.on("dragstart.sfDialog",l),$sf.util.event.on("resize.sfDialog",i)),m.groupId&&a.data("sf-tipper-group",m.groupId),(a.is("a")&&"#"==a.attr("href")||a.is("label")||a.parents("label").length>0)&&a.bind("click.sfTipper",function(a){return b(a.target).is(".sf_tipper,.sf-tip")?!1:void 0});var d=m.initOnly;return 1==d&&(m.initOnly=!1),a.data("sf-tipper")||(a.data("sf-tipper",parseInt(1e6*Math.random())),"tooltip"!=m.style&&"howl"!=m.style&&"tourpoint"!=m.style||(1==m.attachIcon&&(a.bind("click.sfTipper dblclick.sfTipper",function(c){return b(":sf-tipper(target="+a.data("sf-tipper")+",open)").length<=0?a.data("inClick")?!1:(a.data("inClick",!0),a.sfTipper(m),setTimeout(function(){a.removeData("inClick")},1e3),void 0):!1}),"tooltip"==m.style&&a.addClass("sf-tip-ico")),m.autoOpen))?(c.init=!0,1==d?!1:!0):!1},execOpen:function(){return 1==b.isFunction(m.onOpen)&&m.onOpen(g)===!1?!1:!0},requestToReposition:function(){var a=m.reposition;return delete c.settings.reposition,1==a&&c.init?(onResize(),!1):void 0},requestToClose:function(a){var d=m.closeNow===!0,e=m.close||d;delete c.settings.close,delete c.settings.closeNow;var h=f(),i=b(".sf-"+h+":first",o.document);return i.length>0?i.data("sf-tipper")==g.data("sf-tipper")?1==e?(outsideMaster=i,r.fadeTipper(d,i,!0),!1):(c.title&&c.title.html(m.title),c.content&&k(m.content,c.content),q[a].positionIt(q[a].getElements(i)),!1):1==e?!1:(n.setTimeout(function(){g.sfTipper(m)},100),!1):1==e?!1:!0},requestToCloseTippers:function(a){var d=m.closeTippers;if("undefined"!=typeof d&&(d=b.extend({excludeMe:!1},d)),delete c.settings.closeTippers,"undefined"!=typeof d){var e=d.excludeMe===!0,f=":sf-tipper("+(d.style&&"not mine"!==d.style?"style="+("mine"===d.style?a:d.style)+",":"")+"target,open)";for(var g in b.fn.sfTipper.instances)if(b.fn.sfTipper.instances.hasOwnProperty(g)){var h=b.fn.sfTipper.instances[g],i="qt"===h.me.attr("data-style")?f.replace(",open",""):f;h.me.is(i)&&("not mine"===d.style&&h.me.is(":sf-tipper(target,style="+a+")")||e&&$sf.util.jCompat.isEl(h.me,c.me)||h.me.sfTipper({close:!0}))}return!1}return!0},requestToSuppressResumeDestroyQuickTips:function(a){var d="undefined"!=typeof m.suppressTippers?"suppress":"undefined"!=typeof m.resumeTippers?"resume":"undefined"!=typeof m.destroyTippers?"destroy":null;if(null===d)return!0;var e=b.extend({excludeMe:!1},m[d+"Tippers"]);if(delete c.settings[d+"Tippers"],"undefined"!=typeof e){var f=e.excludeMe===!0,g=":sf-tipper(style=qt,target)";for(var h in b.fn.sfTipper.instances)if(b.fn.sfTipper.instances.hasOwnProperty(h)){var i=b.fn.sfTipper.instances[h];i.me.each(function(){var a=b(this);if(a.is(g)&&(!f||!$sf.util.jCompat.isEl(a,c.me))){var e={};e[d]=!0,a.sfTipper(e)}})}return!1}return!0},fadeTipper:function(a,d,e){if(d=d?d:c.outside,!c.fading&&d&&d.length){c.fading=!0;var f=b.fn.jquery.split("."),h=(parseInt(f[0]),parseInt(f[1])),i=f.length>2?parseInt(f[2]):0;3>=h||4===h&&3>i?(b("body",n.document).die("click",d.data("OnClose")),b("body",o.document).die("click",d.data("OnClose"))):7>h?(b(n.document).undelegate("body","click",d.data("OnClose")),b(o.document).undelegate("body","click",d.data("OnClose"))):(b(n.document).off("click","body",d.data("OnClose")),b(o.document).off("click","body",d.data("OnClose"))),g.parentsUntil("body").unbind("scroll",d.data("OnScroll")),b(o).unbind("scroll",d.data("OnResize")),b(o).unbind("resize",d.data("OnResize")),p.isSingle()||(b(n).unbind("resize",d.data("OnResize")),b(n).unbind("scroll",d.data("OnResize")));var j=function(){c.fading=!1,d.remove()};a===!0||m.disableFade===!0?j():m.fadeToAnchor&&!e?d.animate({top:m.fadeToAnchor.offset().top,left:m.fadeToAnchor.offset().left,opacity:0},{duration:500,queue:!1,complete:j}):d.fadeOut(300,j)}},display:function(){return b.isFunction(m.onDisplay)===!0&&m.onDisplay(g,q[m.style].getElements(g).container)===!1?!1:!0}};switch(m.style){case"qt":if(!r.requestToCloseTippers(m.style))return g;if(!r.requestToSuppressResumeDestroyQuickTips(m.style))return g;var s=m.resume;m.resume&&(m.suppress=!1,m.resume=!1);var t=q.bubble.buildContainer("qt");b.each(g,function(){var a=b(this);r.initTrigger(a),s===!0&&a.removeAttr("data-suppressed"),m.close||m.suppress||m.destroy?(a.trigger("mouseout"),(m.suppress||m.destroy)&&(a.unbind("mouseover.sfTipper mouseout.sfTipper"),$sf.util.event.off(l),$sf.util.event.off(i)),m.suppress&&a.attr("data-suppressed",!0),m.destroy?(1===b(":sf-tipper(target,style=qt)").length?t.remove():t.hide(),a.removeClass("sf-tipper-target").removeData("sf-tipper"),delete b.fn.sfTipper.instances[h]):t.hide()):a.bind("mouseover.sfTipper",function(){m.suppress||(q.qt.load(t,a),q.qt.positionIt({el:a,container:t}),t.show())}).bind("mouseout.sfTipper",function(){t.hide()})});break;case"tooltip":case"howl":case"tourpoint":default:var u;if(u=r.requestToCloseTippers(m.style),u&&(u=r.requestToSuppressResumeDestroyQuickTips(m.style)),!u)return g;if((c.init||m.autoOpen)&&(u=r.requestToClose(m.style),!u))return g;if(u=r.initTrigger(g),!u)return g;if(u=r.execOpen(),!u)return g;var v=q[m.style].build(g);q[m.style].positionIt(v),b(o).bind("resize",i),b(o).bind("scroll",i),g.parentsUntil("body").bind("scroll",j),b(n).bind("resize",i),b(n).bind("scroll",i),r.display(),m.disableClose===!1&&n.setTimeout(function(){var a=b.fn.jquery.split("."),c=parseInt(a[1]),d=a.length>2?parseInt(a[2]):0;3>=c||4===c&&3>d?(b("body",n.document).live("click",l),b("body",o.document).live("click",l)):7>c?(b(n.document).delegate("body","click",l),b(o.document).delegate("body","click",l)):(b(n.document).on("click","body",l),b(o.document).on("click","body",l))},100),m.resize===!0||m.disableClose===!0?(delete m.resize,v.container.show()):v.container.hide().fadeIn(300)}}},i.fn(i,d,e))},b.fn.sfTipper.instances={},b.extend(b.expr[":"],{"sf-tipper":function(a,c,d){var e=b(a),f=d[3];if(!f||""==f)return e.is(".sf-tipper, .sf-howl, .sf-tourpoint, .sf-qt");var g=f.split(","),h=!1,i=!1,j=b.grep(g,function(a){return"target"!=a});if(j.length<g.length?(g=j,h=e.is(".sf-tipper-target"),i=!0):h=e.is(".sf-tipper, .sf-howl, .sf-tourpoint, .sf-qt"),h){h=0==g.length;for(var c=0;c<g.length;c++){var k=g[c];if(i)switch(k){case"open":h=b(":sf-tipper(target="+e.data("sf-tipper")+"):visible").length>0;break;case"closed":h=0==b(":sf-tipper(target="+e.data("sf-tipper")+"):visible").length;break;default:var l=k.split("=");if(l.length>1)switch(l[0]){case"style":var m=e.data("sf-tipper-style");h=m===l[1];break;case"group":var n=e.data("sf-tipper-group");h=n===l[1]}else h=!1}else switch(k){case"open":h=e.is(":visible");break;case"closed":h=!e.is(":visible");break;default:var l=k.split("=");if(l.length>1){var j=l[1];if(j&&j.length>0)switch(l[0]){case"style":var m=e.attr("data-style");h=m===j;break;case"group":var n=e.attr("data-group");h=n===j;break;case"target":var o=e.data("sf-tipper");o=o?o.toString():"nope",h=o.toString()===j}}else h=!1}if(0==h)break}}return h}}),b.fn.sfTipper}($sf.getjQuery())});