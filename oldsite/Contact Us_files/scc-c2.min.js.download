!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("scc-c2",[],e):"object"==typeof exports?exports["scc-c2"]=e():t["scc-c2"]=e()}(self,(()=>(()=>{"use strict";var t={d:(e,n)=>{for(var r in n)t.o(n,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:n[r]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r:t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},e={};t.r(e),t.d(e,{_reset:()=>L,debug:()=>R,error:()=>I,info:()=>C,log:()=>C,setDebug:()=>T,warn:()=>x});var n={};t.r(n),t.d(n,{cmdLogEvent:()=>Mi,cmdLogPerf:()=>Fi});var r,o,i,a,u,c,f=(r="",i={document:o=Object.create({get cookie(){return r},set cookie(t){r=t}})},a={},"undefined"==typeof window?{window:i,document:o,navigator:a}:{window:window||i,document:window.document||o,navigator:navigator||a}),s=function(){return f.window},l=function(){return f.document},p=function(){return f.navigator},y=function(){var t=s().location;return t&&t.hostname?t.hostname:""},v=function(){var t=y(),e=t.split("."),n=e.length;return n>2&&(t="".concat(e[n-2],".").concat(e[n-1]),2!==e[n-2].length&&3!==e[n-2].length||2!==e[n-1].length||(t="".concat(e[n-3],".").concat(t))),t},b=function(t,e){var n=A("config")[t];if(n&&e)return n[e]},d=[{name:"EMAIL",regex:/[^@^=]+@[^@]+\.[^@^&]+/}],m=function(t,e){if("string"!=typeof t)return t;for(var n=t,r=0;r<d.length;r++)for(var o=d[r].regex,i=n.match(o);i&&i[0];)i=(n=n.replace(i[0],"REDACTED")).match(o);return n},h=function(t,e){if(void 0===t)return t;void 0===e&&(e=2);for(var n=String(t);n.length<e;)n="0"+n;return n},g=function(t){if(!(arguments.length>1&&void 0!==arguments[1])||arguments[1]||void 0!==t)return!0===t||1===t||"string"==typeof t&&("true"===t.toLowerCase()||"1"===t)},w=function(t){var e=parseInt(t,10);if(!isNaN(e))return e},O=function(t){return/^-?\d+$/.test(t)},j=function(t){var e;return(t=t||(null===(e=s())||void 0===e||null===(e=e.location)||void 0===e?void 0:e.search))?("?"===t.substr(0,1)&&(t=t.substring(1)),t.split("&")):[]},_=function(t,e,n){for(var r=j(n),o=r.length-1;o>=0;o--){var i=r[o].split("=");if(e){if((i[0]+"").toLowerCase()===(t+"").toLowerCase())return i[1]}else if(i[0]===t)return i[1]}},S=!1,P={},E=function(){return"true"===_("scc_debug",!0)||S},k=function(t,e){var n="".concat(t,":").concat(e);if(P[n])return P[n];var r=s().console;if((E()||e)&&r&&r[t]){var o=Function.prototype.bind?Function.prototype.bind.call(r[t],r):function(){for(var e=arguments.length,n=new Array(e),o=0;o<e;o++)n[o]=arguments[o];Function.prototype.apply.call(r[t],r,n)};return P[n]=o,o}return function(){}},T=function(t){S=t,P={}},C=function(){return k("log").apply(void 0,arguments)},I=function(){return k("error",!0).apply(void 0,arguments)},x=function(){return k("warn",!0).apply(void 0,arguments)},R=function(){E()&&C.apply(void 0,arguments)},L=function(){S=!1,P={}},A=function(t){if(s()[c])return s()[c][t]},D=function(t,e){s()[c]&&(s()[c][t]=e)},B=function(t,e){s()[t]=e,D(t,e)};function M(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function F(t,e,n){return(e=function(t){var e=function(t){if("object"!=q(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=q(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==q(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function q(t){return q="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},q(t)}var U=function(t,e){for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){var r=t[n];try{if(e(n,r))return t}catch(t){}}return t},N=function(){for(var t={},e=0;e<arguments.length;e++){var n=e<0||arguments.length<=e?void 0:arguments[e];"object"===q(n)&&U(n,(function(e,n){t[e]=n}))}return t},V=function(t){for(var e in t)void 0===t[e]?delete t[e]:"object"===q(t[e])&&(V(t[e]),0===Object.keys(t[e]).length&&delete t[e]);return t},K=function(t){if(2===t.length)return t[1]},G=function(t,e,n){if("string"!=typeof t)return{};for(var r={},o=t.split(e),i=0;i<o.length;i++){var a=o[i].split(n);r[a[0]]=K(a)}return r},H=function(t,e,n){var r=[];return U(t,(function(t,e){r.push("".concat(t).concat(n).concat(e))})),0===r.length?"":r.join(e)},z=function(t,e){return e&&(t=function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?M(Object(n),!0).forEach((function(e){F(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):M(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}({},t)),Object.keys(t).forEach((function(n){if("object"===q(t[n])){var r=z(t[n],e);e&&(t[n]=r)}})),Object.freeze(t)},W=function(){return function(t){var e=t.getUTCFullYear()+"-"+h(t.getUTCMonth()+1);if(e+="-"+h(t.getUTCDate())+"T"+h(t.getUTCHours())+":",e+=h(t.getUTCMinutes())+":"+h(t.getUTCSeconds()),t.getUTCMilliseconds){var n=t.getUTCMilliseconds();O(n)&&(e+="."+String((n/1e3).toFixed(3)).slice(2,5))}return e+"Z"}(new Date)},$=function(t){return"[object Array]"===Object.prototype.toString.call(t)},Q=function(t,e){$(t)&&t.push({timestamp:W(),data:e})};function J(t){return J="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},J(t)}function Z(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function X(t,e,n){return(e=tt(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Y(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,tt(r.key),r)}}function tt(t){var e=function(t){if("object"!=J(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=J(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==J(e)?e:e+""}const et=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.properties=e?function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Z(Object(n),!0).forEach((function(e){X(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Z(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}({},e):{}}return e=t,(n=[{key:"clear",value:function(){this.properties={}}},{key:"get",value:function(t){if(this.properties.hasOwnProperty(t))return this.properties[t]}},{key:"set",value:function(t,e){this.properties[t]=e}},{key:"isSet",value:function(t){if(this.properties.hasOwnProperty(t))return!0}},{key:"delete",value:function(t){delete this.properties[t]}},{key:"getProperties",value:function(t){var e={},n=function(){return!1};return t||"function"!=typeof t||(n=t),U(this.properties,(function(t,r){n(t)||(e[t]=r)})),e}},{key:"merge",value:function(e){var n=this,r=e;r instanceof t&&(r=e.getProperties()),U(r,(function(t,e){n.set(t,e)}))}}])&&Y(e.prototype,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n}();var nt,rt={},ot={"first-paint":"fp","first-contentful-paint":"fcp"},it=["connectEnd","connectStart","domComplete","domContentLoadedEventEnd","domContentLoadedEventStart","domInteractive","domLoading","domainLookupEnd","domainLookupStart","fetchStart","navigationStart","requestStart","responseEnd","responseStart","loadEventStart","loadEventEnd"],at=["transferSize","encodedBodySize","decodedBodySize"],ut=function(t,e){for(var n={},r=0;r<e.length;r++){var o=e[r],i=t[o];void 0!==i&&(n[o]=Math.round(i))}return n},ct=function(t){return ut(t,it)},ft=function(t){return ut(t,at)},st=function(t,e){var n=new et;"tcc"===t&&n.set("tccin",e||"na");try{(nt=s().performance)&&nt.timing?(function(t){if(t.merge(ct(nt.timing)),nt.getEntriesByName){var e=nt.getEntriesByName(l().location.href)[0];if(!e&&nt.getEntriesByType){var n=nt.getEntriesByType("navigation");e=n[n.length-1]}e&&(t.merge(ft(e)),t.set("navigationType",e.type))}}(n),"tcc"===t&&(function(t){if(nt.getEntriesByType){var e=nt.getEntriesByType("mark");if($(e)){t.set("marks",e.slice(0,10).map((function(t){return{name:t.name,startTime:Math.round(t.startTime)}})));var n=nt.getEntriesByType("measure");$(n)&&t.set("measures",n.slice(0,10).map((function(t){return{name:t.name,duration:Math.round(t.duration)}})))}}}(n),function(t){if(nt.getEntriesByType){var e=nt.getEntriesByType("paint");e&&e.forEach((function(e){t.set(ot[e.name],Math.round(e.startTime))}))}}(n))):("tcc"===t&&n.set("tccperfapi","not supported"),I("Error loading performance lib"))}catch(t){I("_collect unable to get performance data",t)}return n.getProperties()},lt=function(t,e,n){setTimeout((function(){s()._expDataLayer=s()._expDataLayer||[],s()._expDataLayer.push({schema:"add_perf",version:"v1",data:"tcc"===t?{type:"pageperf",properties:N(e,rt,{nav_type:"hard"}),custom_properties:n}:{timing_object:N(e,rt),is_hard_navigation:!0,custom_properties:n}})}),0)},pt=function(){return!(nt=s().performance)||!nt.timing||nt.timing.loadEventStart>0},yt=!1;function vt(t){return vt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},vt(t)}function bt(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,dt(r.key),r)}}function dt(t){var e=function(t){if("object"!=vt(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=vt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==vt(e)?e:e+""}var mt=function(){return t=function t(e,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this._loaded=!1,this._onLoadFns=[],e?this._loaded=!0:n(this._triggerOnLoad.bind(this))},e=[{key:"_executeFn",value:function(t){try{t()}catch(e){I("Exception while executing onLoad callback",t,e)}}},{key:"_triggerOnLoad",value:function(){this._loaded=!0;for(var t=0;t<this._onLoadFns.length;t++)this._executeFn(this._onLoadFns[t])}},{key:"registerOnLoadFn",value:function(t){(function(t){return t&&"[object Function]"==={}.toString.call(t)})(t)&&(this._loaded?this._executeFn(t):this._onLoadFns.push(t))}}],e&&bt(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e}(),ht=s().Request,gt=ht&&"keepalive"in new ht(""),wt=void 0!==p().sendBeacon,Ot=function(t,e,n,r){var o=s().XMLHttpRequest;if(o){var i=new o;i.open(e,t,!0),U(r,(function(t,e){i.setRequestHeader(t,e)})),i.send(n)}};function jt(t){return jt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},jt(t)}function _t(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,f=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==e);c=!0);}catch(t){f=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(f)throw o}}return u}}(t,e)||St(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function St(t,e){if(t){if("string"==typeof t)return Pt(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Pt(t,e):void 0}}function Pt(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function Et(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,kt(r.key),r)}}function kt(t){var e=function(t){if("object"!=jt(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=jt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==jt(e)?e:e+""}const Tt=function(){return t=function t(e){var n=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this._eventQueue=[],this._sendBatchCallback=e,this._maxEvents=r,this._isFlushing=!1,this._bindHandlers(),this._processInterval=setInterval((function(){n._sendBatch()}),250)},(e=[{key:"_bindHandlers",value:function(){var t=this,e=this._hidePage.bind(this),n=this._showPage.bind(this),r=s().attachEvent,o=s().addEventListener;o?(o("beforeunload",e,!1),o("pagehide",e,!1),o("pageshow",n,!1)):r&&(r("beforeunload",e),r("pagehide",e),r("pageshow",n));var i=l(),a=i.addEventListener;a&&a("visibilitychange",function(){"hidden"===i.visibilityState&&t._hidePage(),"visible"===i.visibilityState&&t._showPage()}.bind(this))}},{key:"_hidePage",value:function(){for(this._isFlushing=!0;this._eventQueue.length>0;)this._sendBatch()}},{key:"_showPage",value:function(){this._isFlushing=!1}},{key:"_sendRequests",value:function(t){var e=this;Object.entries(t).forEach((function(t){var n=_t(t,2),r=n[0],o=n[1];try{var i=_t(r.split("|"),2),a=i[0],u={schemaId:i[1],data:[]};Object.values(o).forEach((function(t){if(t.events.length>10)for(var e=t.events.length/10,n=0;n<e;n++)u.data.push(N(t,{events:t.events.slice(10*n,10*n+10)}));else u.data.push(t)})),e._sendBatchCallback(u,{apiKey:a,isFlushing:e._isFlushing})}catch(t){I("Failed to send request: ".concat(t))}}))}},{key:"pushEvent",value:function(t,e){e||!gt&&!wt||this._isFlushing?this._sendBatch(t):this._eventQueue.push(t)}},{key:"_getRequestBatch",value:function(t,e,n){var r=n.apiKey,o=n.schemaId,i=n.global,a=n.contextVersion,u=n.businessContext,c="".concat(r,"|").concat(o),f=t[c]||{};t[c]=f;var s=0;u&&(e.push(u),s=e.length);var l="".concat(a,"|").concat(s);return{batch:f[l]||{global:i,businessContext:u,events:[]},batchKey:l,request:f}}},{key:"_processEvent",value:function(t,e,n){var r,o=this._getRequestBatch(t,e,n),i=o.batch,a=o.batchKey,u=o.request;return(r=i.events).push.apply(r,function(t){return function(t){if(Array.isArray(t))return Pt(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||St(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(n.events.slice(0,this._maxEvents))),n.events.length>this._maxEvents&&this.pushEvent(N(n,{events:n.events.slice(this._maxEvents)})),u[a]=i,Math.min(n.events.length,this._maxEvents)}},{key:"_sendBatch",value:function(t){var e=0,n={},r=[];for(t&&(e+=this._processEvent(n,r,t));this._eventQueue.length>0&&e<this._maxEvents;)e+=this._processEvent(n,r,this._eventQueue.shift());this._sendRequests(n)}}])&&Et(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e}();var Ct,It="_eventBusSendLog",xt=function(t,e){var n=e.apiKey,r=e.isFlushing;!function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=r.queryParams,i=r.payload,a=r.requestHeaders,u=r.credentials,c=r.sendAsBeacon,f=void 0!==c&&c,l=N({},a),y=i?JSON.stringify(i):null;if(f&&wt&&!l.Authorization&&"POST"===e)try{var v=s().Blob;p().sendBeacon(t,new v([y],l))}catch(n){Ot(t,e,y,l)}else!function(t,e,n,r,o){var i=s().fetch;gt&&i?i(t,{keepalive:!0,method:e,headers:r,body:n,credentials:o}).catch((function(o){Ot(t,e,n,r)})):Ot(t,e,n,r)}(t,e,y,l,u);!function(t,e,n,r,o){var i={endpoint:e,queryParams:n,method:r,payload:o};Q(A(t),i)}(n,t,o,e,i)}("".concat(function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return"https://".concat(t?t+".":"").concat(function(){if(u)return u;var t=v();if("secureserver.net"===(t=function(t){var e=t||v();return e.indexOf("godaddy.com")>=0||e.indexOf("secureserver.net")>=0}(t)?t:"secureserver.net")){var e=b("build","env");t="prod"===e?t:"".concat(e,"-").concat(t)}return u=t}())}("csp"),"/eventbus/web?clientid=").concat(n),"POST",It,{payload:t,sendAsBeacon:r})};function Rt(t){return Rt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rt(t)}function Lt(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,At(r.key),r)}}function At(t){var e=function(t){if("object"!=Rt(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=Rt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Rt(e)?e:e+""}function Dt(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Dt=function(){return!!t})()}function Bt(t,e,n,r){var o=Mt(Ft(1&r?t.prototype:t),e,n);return 2&r&&"function"==typeof o?function(t){return o.apply(n,t)}:o}function Mt(){return Mt="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,n){var r=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=Ft(t)););return t}(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(arguments.length<3?t:n):o.value}},Mt.apply(null,arguments)}function Ft(t){return Ft=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Ft(t)}function qt(t,e){return qt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},qt(t,e)}var Ut=function(t){try{return JSON.parse(t)}catch(t){return{}}};const Nt=new(function(t){function e(t){var n;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(n=function(t,e,n){return e=Ft(e),function(t,e){if(e&&("object"==Rt(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Dt()?Reflect.construct(e,n||[],Ft(t).constructor):e.apply(t,n))}(this,e,[t]))._updateWindow(),n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&qt(t,e)}(e,t),n=e,(r=[{key:"_updateWindow",value:function(){D("config",z(this.properties,!0))}},{key:"set",value:function(t,n){Bt(e,"set",this,3)([t,n]),this._updateWindow()}},{key:"merge",value:function(t){Bt(e,"merge",this,3)([t]),this._updateWindow()}}])&&Lt(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r}(et))({build:{env:"prod",date:"2025-05-28T12:00:43.274Z"},client:{name:"scc-c2",version:"1.2.2",hash:"b83aa08531a8c3e3fd806952e806fd339ef169f5"},cookie:{sameSite:"none"},site:{privateLabelId:""},sGtm:{enabled:g(""),whitelist:Ut(""),account:"",host:""},ga:{account:""},wGtm:{account:"",enableProxy:g(""),enabled:g(""),requireGoogleClientId:g(""),whitelist:Ut("")},eventBus:{enabled:g("true"),sccApiKey:"b18ef4f046435b64a469b32c3c1c20a3",rigorApiKey:"8da2217409854bee82e12dc4ca0b39fb"},clickListener:{enabled:!0,navDelayMs:75},timing:{auto:!0},webVitals:{enabled:!0,url:""},consent:{delayMs:500},cdep:{appId:""}});var Vt,Kt,Gt,Ht=function(t,e){var n=Nt.get(t);return e?n?n[e]:void 0:n},zt=function(t,e,n){var r=Nt.get(t),o=r||{};r||Nt.set(t,o),o[e]=n};function Wt(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var $t,Qt,Jt=[],Zt={},Xt=0,Yt=function(t){var e,n=function(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return Wt(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Wt(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,o=function(){};return{s:o,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){u=!0,i=t},f:function(){try{a||null==n.return||n.return()}finally{if(u)throw i}}}}($t);try{for(n.s();!(e=n.n()).done;){var r=e.value;if(t.startsWith("".concat(r,".")))return!1}}catch(t){n.e(t)}finally{n.f()}return!0},te=function(t,e){"debug"===t&&T("true"===e||!0===e)},ee=function(){var t,e,n=function(){for(var t={},e=0;e<Kt.length;e++){var n=Kt[e];U(n,(function(e,n){-1===Qt.indexOf(e.toLowerCase())&&(te(e,n),Yt(e)||(t[e]=n))}))}return t}();e={identity:{realm:(t=n)["".concat(Vt,".realm")]},site:{privateLabelId:t["".concat(Vt,".privateLabelId")]},timing:{auto:!g(t["".concat(Vt,".manualPagePerf")],!1)},page:{spa:t["".concat(Vt,".spa")],id:t["".concat(Vt,".pageId")],contentGroup:t["".concat(Vt,".gaContentGroup")]||t["".concat(Vt,".contentGroup")],referrerExclusion:t["".concat(Vt,".referrerExclusion")]},cookie:{sameSite:t["".concat(Vt,".cookies.sameSite")]},wGtm:{enabled:g(t["".concat(Vt,".webGtmEnabled")],!1)},clickListener:{enabled:!g(t["".concat(Vt,".listenerDisabled")],!1),navDelayMs:t["".concat(Vt,".eventDelayMs")]}},Object.keys(e).forEach((function(t){Object.keys(e[t]).forEach((function(n){var r=e[t][n];void 0!==r&&zt(t,n,r)}))})),Nt.merge(n),Q(Jt,n)};const ne={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};let re;const oe=new Uint8Array(16);function ie(){if(!re&&(re="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!re))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return re(oe)}const ae=[];for(let t=0;t<256;++t)ae.push((t+256).toString(16).slice(1));const ue=function(t,e,n){if(ne.randomUUID&&!e&&!t)return ne.randomUUID();const r=(t=t||{}).random||(t.rng||ie)();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,e){n=n||0;for(let t=0;t<16;++t)e[n+t]=r[t];return e}return function(t,e=0){return ae[t[e+0]]+ae[t[e+1]]+ae[t[e+2]]+ae[t[e+3]]+"-"+ae[t[e+4]]+ae[t[e+5]]+"-"+ae[t[e+6]]+ae[t[e+7]]+"-"+ae[t[e+8]]+ae[t[e+9]]+"-"+ae[t[e+10]]+ae[t[e+11]]+ae[t[e+12]]+ae[t[e+13]]+ae[t[e+14]]+ae[t[e+15]]}(r)};var ce=function(){return ue()};function fe(t){return fe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fe(t)}function se(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function le(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,pe(r.key),r)}}function pe(t){var e=function(t){if("object"!=fe(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=fe(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==fe(e)?e:e+""}var ye=function(){return t=function t(e){var n=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.dataLayerNames=e,this.dataLayerLog=[],this.asyncCommands=[],this.loaded=!1,this.dataLayers=[],this.dataLayerNames.forEach((function(t){var e,r=s()[t];$(r)&&(e=n.asyncCommands).push.apply(e,function(t){return function(t){if(Array.isArray(t))return se(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return se(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?se(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(r)),B(t,n),n.dataLayers.push(r)}))},(e=[{key:"start",value:function(){this.loadAsyncCommands()}},{key:"pushLog",value:function(t){Q(this.dataLayerLog,t)}},{key:"process",value:function(){}},{key:"push",value:function(t){this.loaded?this.process(t):this.asyncCommands.push(t)}},{key:"loadAsyncCommands",value:function(){for(;this.asyncCommands.length>0;)this.process(this.asyncCommands.shift());this.loaded=!0}}])&&le(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e}();function ve(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function be(t){return be="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},be(t)}function de(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function me(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,ge(r.key),r)}}function he(t,e,n){return e&&me(t.prototype,e),n&&me(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function ge(t){var e=function(t){if("object"!=be(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=be(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==be(e)?e:e+""}var we=function(){return he((function t(e,n){if(de(this,t),!e)throw"Missing version definitions";this._versionedDefinitions=e,this._interfaceType=n}),[{key:"getInterface",value:function(t,e){var n=this._versionedDefinitions[e];if(n)return n}}])}(),Oe=function(){return he((function t(e){if(de(this,t),!e)throw"Missing type definitions";this._typeVersionDefinitionMap=e}),[{key:"getInterface",value:function(t,e,n){var r=this._typeVersionDefinitionMap[n];if(r)return r.getInterface(t,e)}}])}(),je={},_e=function(t){for(var e=function(){var t,e,o=(t=r[n],e=2,function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,f=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==e);c=!0);}catch(t){f=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(f)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return ve(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ve(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),i=o[0],a=o[1];if($(a)){var u={};a.forEach((function(t){var e={};Object.keys(t).forEach((function(n){"type"!==n&&(e[n]=t[n])})),u[t.type]=new we(e,t.type)})),je[i]=new Oe(u)}else je[i]=new we(a)},n=0,r=Object.entries(t);n<r.length;n++)e()},Se="string",Pe=/^([\w-]+)$/,Ee=/^([\w/-]+)$/,ke="OPTIONAL",Te="REQUIRED",Ce="map";function Ie(t,e,n){return e=Ae(e),function(t,e){if(e&&("object"==Ne(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,xe()?Reflect.construct(e,n||[],Ae(t).constructor):e.apply(t,n))}function xe(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(xe=function(){return!!t})()}function Re(t,e,n,r){var o=Le(Ae(1&r?t.prototype:t),e,n);return 2&r&&"function"==typeof o?function(t){return o.apply(n,t)}:o}function Le(){return Le="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,n){var r=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=Ae(t)););return t}(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(arguments.length<3?t:n):o.value}},Le.apply(null,arguments)}function Ae(t){return Ae=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Ae(t)}function De(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Be(t,e)}function Be(t,e){return Be=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Be(t,e)}function Me(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Fe(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Ue(r.key),r)}}function qe(t,e,n){return e&&Fe(t.prototype,e),n&&Fe(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function Ue(t){var e=function(t){if("object"!=Ne(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=Ne(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Ne(e)?e:e+""}function Ne(t){return Ne="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ne(t)}var Ve=function(t,e,n){var r;if("CALCULATED"!==e._fieldType){var o=t[e.name];"sub"===e._resolutionType&&(o=t),r=e.parse(o)}var i=Ne(e.transformFn);if("undefined"!==i){var a=$(e.transformFn);if("function"===i||a)for(var u=a?e.transformFn:[e.transformFn],c=0;c<u.length;c++)r=u[c](r,n);else r=e.transformFn}return e._outputType&&e._outputType===Se&&(r=r.toString()),r},Ke=function(){return qe((function t(e){Me(this,t),this.name=e,this.properties=[],this.definitionType="",this._outputType,this._resolutionType=Ce,this._fieldType=ke,this.outputKey,this.allowedValuesList=[]}),[{key:"parse",value:function(t){if(this.validate(t),this._fieldType!==ke||t||"boolean"==typeof t||0===t){for(var e=new et,n=0;n<this.properties.length;n++){var r=this.properties[n],o=Ve(t,r,e.getProperties());r._resolutionType===Ce?e.set(r.outputKey||r.name,o):e.merge(o)}return e.getProperties()}}},{key:"validate",value:function(t){if(this._fieldType===Te&&!t&&"boolean"!=typeof t&&0!==t)throw"Missing required property '".concat(this.name,"'");if(this.allowedValuesList.length>0&&!this.allowedValuesList.includes(t))throw"Input '".concat(t,"' for property '").concat(this.name,"' is not one of the allowed values (").concat(this.allowedValuesList,").")}},{key:"optional",value:function(){return this._fieldType=ke,this}},{key:"required",value:function(){return this._fieldType=Te,this}},{key:"stringify",value:function(){for(var t=new et,e=0;e<this.properties.length;e++){var n=this.properties[e];t.merge(n.stringify())}return t.getProperties()}},{key:"transform",value:function(t){return this.transformFn=t,this}},{key:"transformKey",value:function(t){return this.outputKey=t,this}},{key:"allowedValues",value:function(t){return this.allowedValuesList=t,this}},{key:"outputType",value:function(t){return this._outputType=t,this}}])}(),Ge=function(t){function e(t,n){var r;return Me(this,e),(r=Ie(this,e,[t])).definitionType="Schema",r.properties=n,r}return De(e,t),qe(e,[{key:"parse",value:function(t){var n;if(this.properties&&this.properties.length>0)try{n=Re(e,"parse",this,3)([t||{}])}catch(t){throw""}return Object.freeze(n)}}])}(Ke),He=function(t){function e(){return Me(this,e),Ie(this,e,arguments)}return De(e,t),qe(e,[{key:"parse",value:function(t){return this.validate(t),t}},{key:"stringify",value:function(){var t={};return t[this.name]=this._fieldType,t}}])}(Ke),ze=function(t){function e(){return Me(this,e),Ie(this,e,arguments)}return De(e,t),qe(e,[{key:"pattern",value:function(t){return this._pattern=t,this}},{key:"validate",value:function(t){Re(e,"validate",this,3)([t]);var n=Ne(t);if("string"===n){if(this._pattern&&!t.match(this._pattern))throw"Input ".concat(t," does not match ").concat(this._pattern)}else if("undefined"!==n&&null!=t)throw"Input is not a string"}},{key:"stringify",value:function(){var t=Re(e,"stringify",this,3)([]),n=this._pattern?" | pattern: ".concat(this._pattern):"";return t[this.name]="".concat(t[this.name]," | type: string").concat(n),t}}])}(He),We=function(t){function e(){var t;return Me(this,e),(t=Ie(this,e)).definitionType="Object",t}return De(e,t),qe(e,[{key:"stringify",value:function(){if(this._resolutionType===Ce){var t={};return t["".concat(this.name," (").concat(this._fieldType,")")]=Re(e,"stringify",this,3)([]),t}return Re(e,"stringify",this,3)([])}},{key:"substitute",value:function(t){return this.properties=this.properties.concat(t),this._resolutionType="sub",this}},{key:"map",value:function(t,e){return this.name=t,this.properties=this.properties.concat(e),this._resolutionType=Ce,this}},{key:"extend",value:function(t){return t&&(this.properties=this.properties.concat(t)),this}}])}(Ke),$e=function(t){function e(){var t;return Me(this,e),(t=Ie(this,e)).definitionType="Collection",t.minElements=0,t}return De(e,t),qe(e,[{key:"parse",value:function(t){if(this.minElements&&(!t||t.length<this.minElements))throw"";if(void 0!==t){if(!$(t))throw"";return t.map(Re(e,"parse",this,1).bind(this))}}},{key:"stringify",value:function(){if(this._resolutionType===Ce){var t={};t["".concat(this.minElements,"...n")]=Re(e,"stringify",this,3)([])["".concat(this.name," (").concat(this._fieldType,")")];var n={};return n["".concat(this.name," (").concat(this._fieldType,")")]=t,n}return Re(e,"stringify",this,3)([])}},{key:"withMinElements",value:function(t){return this.minElements=t,this._fieldType=t>0?Te:ke,this}}])}(We);function Qe(t){return Qe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qe(t)}function Je(t){return function(t){if(Array.isArray(t))return Ze(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return Ze(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ze(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ze(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function Xe(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Ye(r.key),r)}}function Ye(t){var e=function(t){if("object"!=Qe(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=Qe(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Qe(e)?e:e+""}function tn(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(tn=function(){return!!t})()}function en(t,e,n,r){var o=nn(rn(1&r?t.prototype:t),e,n);return 2&r&&"function"==typeof o?function(t){return o.apply(n,t)}:o}function nn(){return nn="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,n){var r=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=rn(t)););return t}(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(arguments.length<3?t:n):o.value}},nn.apply(null,arguments)}function rn(t){return rn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},rn(t)}function on(t,e){return on=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},on(t,e)}var an=["_expDataLayer","_signalsDataLayer"],un=["add_virtual_page_view","set_config","subscribe"],cn=function(t){function n(t){var e,r=t.pageViewSchema,o=t.autoPageViewDisabled,i=t.beforeEvent,a=t.immediateSchemas,u=void 0===a?[]:a;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,n),(e=function(t,e,n){return e=rn(e),function(t,e){if(e&&("object"==Qe(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,tn()?Reflect.construct(e,n||[],rn(t).constructor):e.apply(t,n))}(this,n,[an])).pageViewSchema=r,e.autoPageViewDisabled=o,e.beforeEvent=i,e.immediateSchemas=[un].concat(Je(u)),e._initEvents(),e}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&on(t,e)}(n,t),r=n,(o=[{key:"push",value:function(t){return this.immediateSchemas.includes(t.schema)?this.process(t):en(n,"push",this,3)([t])}},{key:"_initEvents",value:function(){for(var t=!this.autoPageViewDisabled,e=this.asyncCommands.length-1;e>=0;e--){var n=this.asyncCommands[e];n.schema!==this.pageViewSchema||n.data&&n.data.virtual_path||(t=!1),"set_config"===n.schema&&this.process.apply(this,Je(this.asyncCommands.splice(e,1)))}t&&this.process({schema:this.pageViewSchema})}},{key:"process",value:function(t){en(n,"pushLog",this,3)([t]);try{var r=t.data,o=t.schema||t.interface,i=t.type,a=t.version||"v1",u=t.targets||[],c={eventId:ce()};this.beforeEvent&&this.beforeEvent(),function(){for(;Xt<Kt.length;){var t=Kt[Xt];U(t,(function(t,e){-1===Qt.indexOf(t.toLowerCase())&&Yt(t)&&(te(t,e),Zt[t]=e)})),Xt++}}(),function(t,e,n,r,o,i){var a=function(t,e,n){var r=je[t];if(r)return r.getInterface(t,n,e)}(t,e,n),u=new Ge(t,a.properties).parse(r)||{},c=new a.handler(u,e,n,o,i);c.preProcess(u),c.process(u)}(o,i,a,r,c,u)}catch(n){var f=["Unable to process",t,"\n\n"];I.apply(e,f.concat(n))}}}])&&Xe(r.prototype,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o}(ye);function fn(t){return fn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fn(t)}function sn(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,ln(r.key),r)}}function ln(t){var e=function(t){if("object"!=fn(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=fn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==fn(e)?e:e+""}function pn(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(pn=function(){return!!t})()}function yn(){return yn="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,n){var r=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=vn(t)););return t}(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(arguments.length<3?t:n):o.value}},yn.apply(null,arguments)}function vn(t){return vn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},vn(t)}function bn(t,e){return bn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},bn(t,e)}var dn=["_trfq"],mn=function(t){function e(t){var n;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(n=function(t,e,n){return e=vn(e),function(t,e){if(e&&("object"==fn(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,pn()?Reflect.construct(e,n||[],vn(t).constructor):e.apply(t,n))}(this,e,[dn])).handlers=t,n.start(),n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&bn(t,e)}(e,t),n=e,r=[{key:"process",value:function(t){var n,r,o;(n=e,r=this,"function"==typeof(o=yn(vn(1&3?n.prototype:n),"pushLog",r))?function(t){return o.apply(r,t)}:o)([t]);try{var i=Array.prototype.slice.call(t,1),a=t[0];this.handlers[a]?this.handlers[a].apply(null,i):I("Invalid command sent to data layer",this.dataLayerName,t)}catch(r){I("Unable to process",t,r)}}}],r&&sn(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r}(ye);const hn=new et;var gn,wn,On,jn=function(t,e,n,r){var o=b("cookie","sameSite")||"",i=new Date;i.setTime(i.getTime()+60*n*1e3),l().cookie="".concat(t,"=").concat(encodeURI(e),"; domain=").concat(v(),"; ")+(n?"expires=".concat(i.toGMTString(),"; "):"")+(r?"path=".concat(r,"; "):"")+(o?"SameSite=".concat(o,"; "):"")+("NONE"===o.toUpperCase()?" Secure;":"")},_n=function(t){var e=l().cookie;return wn!==e&&(On=function(t){for(var e=t.split(";"),n=Object.create(null),r=0;r<e.length;r++){var o=/^\s*([^=]+)\s*=\s*(.*)$/.exec(e[r]);if(o&&o[2]){var i=o[1].trim(),a=decodeURIComponent(o[2].trim());n[i]=a}}return n}(e),wn=e),On[t]},Sn=function(t){var e=_n(t);return G(e,"&","=")},Pn=function(){var t,e=l().querySelector("meta[name='gd:traceId']");return e&&(t=e.getAttribute("content")),t&&!Nt.get("tcc.disablePageTracing")?{trace_id:t,traced:1}:{trace_id:ce().replace(/-/g,"")}};function En(t){return En="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},En(t)}function kn(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Tn(r.key),r)}}function Tn(t){var e=function(t){if("object"!=En(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=En(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==En(e)?e:e+""}var Cn=function(){return t=function t(e,n,r,o,i){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.input=e,this.interfaceType=n,this.interfaceVersion=r,this.internal=o,this.targets=i},(e=[{key:"preProcess",value:function(t){}},{key:"process",value:function(t){}}])&&kn(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e}();function In(t){return In="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},In(t)}function xn(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,f=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==e);c=!0);}catch(t){f=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(f)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return Rn(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Rn(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Rn(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function Ln(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function An(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Ln(Object(n),!0).forEach((function(e){Dn(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Ln(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function Dn(t,e,n){return(e=Mn(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Bn(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Mn(r.key),r)}}function Mn(t){var e=function(t){if("object"!=In(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=In(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==In(e)?e:e+""}var Fn=function(){return t=function t(e){var n=e.contextFnMap,r=e.paramsFnMap,o=void 0===r?{}:r,i=e.windowName;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this._contextFnMap=n,this._paramsFnMap=o,this._windowName=i},(e=[{key:"init",value:function(t){var e=t.context,n=void 0===e?{}:e,r=t.contextFnMap,o=void 0===r?{}:r,i=t.paramsFnMap,a=void 0===i?{}:i;this.version=0,this.context=n,this._initialContext=An({},n),this._contextMap={},this._contextFnMap=N(this._contextFnMap,o),this._paramsFnMap=N(this._paramsFnMap,a)}},{key:"_buildContext",value:function(t){var e=An({},this._initialContext);return Object.entries(t).forEach((function(t){var n,r=xn(t,2),o=r[0],i=r[1],a=o.split("."),u=e;a.forEach((function(t,e){e<a.length-1?(u[t]=u[t]||{},u=u[t]):n=t})),u[n]=i})),e}},{key:"updateContext",value:function(){var t=this;if(this._contextFnMap){var e=!1,n={},r={};Object.entries(this._paramsFnMap).forEach((function(t){var e=xn(t,2),n=e[0],o=e[1];try{r[n]=o()}catch(t){I("Error executing paramsFnMap for key: ".concat(n),t)}})),Object.entries(this._contextFnMap).forEach((function(o){var i=xn(o,2),a=i[0],u=(0,i[1])(r);t._contextMap[a]!==u&&(e=!0),void 0!==u&&(n[a]=u)})),e&&(this.version++,this.context=z(this._buildContext(n)),this._contextMap=n,this._windowName&&D(this._windowName,this.context))}}}])&&Bn(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e}(),qn={"godaddy.com":1,"mediatemple.net":4500,"afternic.com":497036},Un=[function(){return Ht("site","privateLabelId")},function(){return _("plid")},function(){var t=v().replace("dev-","").replace("test-","").replace("stg-","");if(qn.hasOwnProperty(t))return qn[t]}];function Nn(t){return Nn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nn(t)}function Vn(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Kn(r.key),r)}}function Kn(t){var e=function(t){if("object"!=Nn(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=Nn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Nn(e)?e:e+""}function Gn(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Gn=function(){return!!t})()}function Hn(){return Hn="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,n){var r=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=zn(t)););return t}(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(arguments.length<3?t:n):o.value}},Hn.apply(null,arguments)}function zn(t){return zn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},zn(t)}function Wn(t,e){return Wn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Wn(t,e)}var $n={"client.userAgent":function(){return p().userAgent},"client.sdk.name":function(){return Ht("client","name")},"client.sdk.version":function(){return Ht("client","version")},"client.device.viewportWidth":function(t){var e=t.win,n=t.doc;return w(e.innerWidth?e.innerWidth:n.body.offsetWidth)},"client.device.viewportHeight":function(t){var e=t.win,n=t.doc;return w(e.innerHeight?e.innerHeight:n.body.offsetHeight)},"client.device.screenResolutionWidth":function(t){var e=t.win;return w(e.screen.width)},"client.device.screenResolutionHeight":function(t){var e=t.win;return w(e.screen.height)},"page.id":function(){return Ht("page","id")},"page.traceId":function(){return hn.get("trace_id")},"page.contentGroup":function(){return function(){var t=Ht("page","contentGroup");if("string"!=typeof t)return t;for(var e=t.split("/"),n=e.length-1;n>=0;n--)e[n]=e[n].trim(),e[n]||e.splice(n,1);if(e.length>0)for(;e.length<3;)e.push("Other");return e.join(" / ")}()},"page.host":function(){return y()},"page.path":function(t){return t.win.location.pathname},"page.virtualPath":function(){return hn.get("virtual_path")},"page.location":function(){return n=(t=j()).length?(function(t){for(var e=0;e<t.length;e++){var n=t[e].split("="),r=!1,o=n[1];o!==decodeURIComponent(n[1])&&(o=decodeURIComponent(n[1]),r=!0);var i=m(o);r&&(i=encodeURIComponent(i)),t[e]="".concat(n[0],"=").concat(i)}}(t),"?"+t.join("&")):"",(r=s().location)&&(e="".concat(r.protocol,"//").concat(r.hostname).concat(r.pathname)),n&&(e="".concat(e).concat(n)),e;var t,e,n,r},"page.referrer":function(t){return t.doc.referrer},"site.privateLabelId":function(){return function(){for(var t=0;t<Un.length;t++){var e=Un[t]();if(e&&O(e))return w(e)}}()}},Qn={win:s,doc:l};const Jn=new(function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e,n){return e=zn(e),function(t,e){if(e&&("object"==Nn(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Gn()?Reflect.construct(e,n||[],zn(t).constructor):e.apply(t,n))}(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Wn(t,e)}(e,t),n=e,r=[{key:"init",value:function(t){var n,r,o,i=t.traceId,a=t.contextFnMap,u=t.paramsFnMap;(n=e,r=this,"function"==typeof(o=Hn(zn(1&3?n.prototype:n),"init",r))?function(t){return o.apply(r,t)}:o)([{context:{traceId:i},contextFnMap:a,paramsFnMap:u}])}}],r&&Vn(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r}(Fn))({contextFnMap:$n,paramsFnMap:Qn,windowName:"globalContext"});var Zn="experiments";const Xn=new et,Yn=new Fn({contextFnMap:{experiments:function(){return Xn.get("experiments")}}});var tr=["events"];function er(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var nr={},rr=["routing","webContext"],or=function(t,e,n){var r=e.callback;setTimeout((function(){try{r(n)}catch(e){I("Failed to push datalayer event to subscriber (".concat(t,") callback"),e)}}),0)},ir=function(t,e,n){var r=e.schemaIds,o=function(t,e){e.events;var n=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n={};for(var r in t)if({}.hasOwnProperty.call(t,r)){if(e.includes(r))continue;n[r]=t[r]}return n}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.includes(n)||{}.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(e,tr),r=t.fields||[];return rr.forEach((function(t){r.includes(t)||delete n[t]})),n}(e,n);e.batch?(o.events=r?n.events.filter((function(t){return r.includes(t.schemaId)})):n.events,o.events.length>0&&or(t,e,o)):n.events.forEach((function(n){r&&!r.includes(n.schemaId)||or(t,e,N(o,{event:n}))}))},ar=function(t,e){var n=fr();n.length>=ur&&x("".concat(t," subscribed to SCC after the eventQueue was full. Some events may have been lost.")),n.forEach((function(n){return ir(t,e,n)})),nr[t]=e},ur=2e3,cr=[],fr=function(){return[].concat(cr)},sr="urn:shared:user:event:/data-platform/signals/click/v1",lr="urn:shared:user:event:/data-platform/signals/impression/v1",pr="urn:shared:user:event:/data-platform/signals/element-action/v1",yr="urn:shared:user:event:/data-platform/signals/traffic-event/v1",vr="urn:shared:user:event:/data-platform/signals/experiment-assignment/v1",br="urn:shared:user:event:/appconfig/id-percentage-evaluation/v1",dr=function(t){var e,n,r=t.globalSchemaId,o=t.businessContext,i=t.events,a=t.webContext,u=t.routing,c=void 0===u?{}:u,f=c.eventBusApiKey,s=c.hasConversion,l=c.targets;if($(i)&&0!==i.length){for(var p,y=W(),v=0;v<i.length;v++)i[v].data=N({eventCreationTimestamp:y},i[v].data);Jn.updateContext(),Yn.updateContext(),n={schemaId:r||(p=null===(e=Jn.context.site)||void 0===e?void 0:e.privateLabelId,p?"urn:shared:user:events:/v2":"urn:shared:user:events:/v1"),global:Jn.context,businessContext:o,events:i,webContext:N(Yn.context,a),routing:{eventBusApiKey:f||b("eventBus","sccApiKey"),globalContextVersion:Jn.version,hasConversion:s,targets:l}},z(n),cr.push(n),function(t){for(var e=0,n=Object.entries(nr);e<n.length;e++){var r=(u=2,function(t){if(Array.isArray(t))return t}(a=n[e])||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,f=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==e);c=!0);}catch(t){f=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(f)throw o}}return u}}(a,u)||function(t,e){if(t){if("string"==typeof t)return er(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?er(t,e):void 0}}(a,u)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),o=r[0],i=r[1];try{ir(o,i,t)}catch(t){I("Failed to push datalayer event batch to subscriber (".concat(o,")"),t)}}var a,u}(n),cr.length>ur&&cr.shift()}};function mr(t){return mr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},mr(t)}function hr(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,gr(r.key),r)}}function gr(t){var e=function(t){if("object"!=mr(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=mr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==mr(e)?e:e+""}function wr(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(wr=function(){return!!t})()}function Or(t){return Or=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Or(t)}function jr(t,e){return jr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},jr(t,e)}var _r=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e,n){return e=Or(e),function(t,e){if(e&&("object"==mr(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,wr()?Reflect.construct(e,n||[],Or(t).constructor):e.apply(t,n))}(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&jr(t,e)}(e,t),n=e,(r=[{key:"process",value:function(t){this._pushToQueue(t)}},{key:"_pushToQueue",value:function(t){this.internal.hasFired||(dr({globalSchemaId:this._getGlobalSchemaId(),businessContext:this._getBusinessContext(),events:this._getEvents(t),webContext:this._getWebContext(t),routing:{eventBusApiKey:this._getApiKey(),hasConversion:this._hasConversion(),targets:this.targets}}),this.internal.hasFired=!0)}},{key:"_getSchemaId",value:function(){}},{key:"_getEvents",value:function(t){return[{schemaId:this._getSchemaId(),data:t}]}},{key:"_getGlobalSchemaId",value:function(){}},{key:"_getBusinessContext",value:function(){}},{key:"_getApiKey",value:function(){}},{key:"_hasConversion",value:function(){}},{key:"_getWebContext",value:function(t){}}])&&hr(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r}(Cn);function Sr(t){return Sr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Sr(t)}function Pr(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Er(r.key),r)}}function Er(t){var e=function(t){if("object"!=Sr(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=Sr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Sr(e)?e:e+""}function kr(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(kr=function(){return!!t})()}function Tr(t){return Tr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Tr(t)}function Cr(t,e){return Cr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Cr(t,e)}const Ir=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e,n){return e=Tr(e),function(t,e){if(e&&("object"==Sr(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,kr()?Reflect.construct(e,n||[],Tr(t).constructor):e.apply(t,n))}(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Cr(t,e)}(e,t),n=e,(r=[{key:"_getBusinessContext",value:function(){return this.input.businessContext}},{key:"_getEvents",value:function(){return this.input.events}},{key:"_getApiKey",value:function(){return this.input.apiKey}},{key:"_getGlobalSchemaId",value:function(){return this.input.schemaId}}])&&Pr(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r}(_r);var xr=/[^a-z0-9\_\/\.\-]/g,Rr=/^([a-z0-9_\-]+)\.([a-z0-9_\-]+)\.(([a-z0-9_\/\-]+)\.)?([a-z0-9_\/\-]+)\.([a-z0-9_\-]+)\.([a-z0-9_\-]+)$/g,Lr=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.eid,n=t.customProperties,r=t.eventCategory;return N({pageLevelProperties:N(Zt),identityRealm:Ht("identity","realm")},e&&{eid:e},n&&{customProperties:n},r&&{eventCategory:r})},Ar=function(t,e,n,r){var o={traffic:Lr({eid:e,customProperties:r}),producerEventId:t};if(e){var i=function(t){return t.split(".")}(e),a=function(t){return t[t.length-1]}(i);(function(t){return!!(t.match(Rr)&&t.length<=500)})(e)&&function(t,e,n){return e===n&&t.length>=5&&t.length<=6}(i,a,n)&&(o.element=function(t,e){var n=6===t.length?1:0,r={area:t[0],product:t[1],section:t[2+n],widget:t[3+n]};return["impression","click"].includes(e)||(r.action=e),r}(i,a))}return o},Dr=function(t,e,n,r){null!=r&&null!=n&&0!==r&&0!==n&&n>r&&(t[e]=Math.round(n-r))},Br=function(t,e,n,r){var o=ct(e);!function(t,e){Dr(t,"pageLoadTime",e.loadEventStart,e.navigationStart),Dr(t,"domContentLoadedTime",e.domContentLoadedEventStart,e.navigationStart),Dr(t,"domInteractiveTime",e.domInteractive,e.navigationStart),Dr(t,"pageDownloadTime",e.responseEnd,e.responseStart),Dr(t,"domainLookupTime",e.domainLookupEnd,e.domainLookupStart),Dr(t,"serverResponseTime",e.responseStart,e.requestStart),Dr(t,"serverConnectionTime",e.connectEnd,e.connectStart),Dr(t,"redirectionTime",e.fetchStart,e.navigationStart)}(o,e);var i={navigationType:e.navigationType,timing:{navigation:o},traffic:Lr({customProperties:n}),producerEventId:t,contentLoadType:r};return"hard"===r&&(i.response=ft(e),i.timing.marks=e.marks,i.timing.measures=e.measures,i.timing.paint={firstPaint:e.fp,firstContentfulPaint:e.fcp,largestContentfulPaint:e.lcp}),i},Mr=function(t,e,n){return{timing:{paint:{largestContentfulPaint:e.LCP},vitals:{cumulativeLayoutShift:e.CLS,firstInputDelay:e.FID,timeToInteractive:e.timeToInteractive,interactionToNextPaint:e.INP}},traffic:Lr({customProperties:n}),producerEventId:t}};function Fr(t){return Fr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fr(t)}function qr(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Ur(r.key),r)}}function Ur(t){var e=function(t){if("object"!=Fr(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=Fr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Fr(e)?e:e+""}function Nr(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Nr=function(){return!!t})()}function Vr(t){return Vr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Vr(t)}function Kr(t,e){return Kr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Kr(t,e)}const Gr=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e,n){return e=Vr(e),function(t,e){if(e&&("object"==Fr(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Nr()?Reflect.construct(e,n||[],Vr(t).constructor):e.apply(t,n))}(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Kr(t,e)}(e,t),n=e,r=[{key:"_getSchemaId",value:function(){return pr}},{key:"_getActionType",value:function(t){return t.element.action}},{key:"_getEvents",value:function(t,e){var n=t.traffic||{};return n.eid=this._buildLegacyEid(this._getActionType(t),t.element,n.eid),[{schemaId:this._getSchemaId(),data:N(t,{producerEventId:e||this.internal.eventId,element:t.element,traffic:Lr(n)})}]}},{key:"_buildLegacyEid",value:function(t,e){return arguments.length>2&&void 0!==arguments[2]?arguments[2]:"".concat(e.area,".").concat(e.product,".").concat(e.section,".").concat(e.widget,".").concat(t)}},{key:"_transformLegacyPromo",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.eid,o=n.customProperties,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return{id:i.id,name:i.name,creative_name:i.creative,creative_slot:i.position,type:t,properties:o,eid:this._buildLegacyEid(t,e,r)}}},{key:"_isPromo",value:function(t){return t.promotion&&Object.keys(t.promotion).length>0}}],r&&qr(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r}(_r);function Hr(t){return Hr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Hr(t)}function zr(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Wr(r.key),r)}}function Wr(t){var e=function(t){if("object"!=Hr(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=Hr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Hr(e)?e:e+""}function $r(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return($r=function(){return!!t})()}function Qr(t){return Qr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Qr(t)}function Jr(t,e){return Jr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Jr(t,e)}const Zr=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e,n){return e=Qr(e),function(t,e){if(e&&("object"==Hr(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,$r()?Reflect.construct(e,n||[],Qr(t).constructor):e.apply(t,n))}(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Jr(t,e)}(e,t),n=e,(r=[{key:"_getSchemaId",value:function(){return sr}},{key:"_getActionType",value:function(){return"click"}}])&&zr(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r}(Gr);function Xr(t){return Xr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Xr(t)}function Yr(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function to(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,eo(r.key),r)}}function eo(t){var e=function(t){if("object"!=Xr(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=Xr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Xr(e)?e:e+""}function no(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(no=function(){return!!t})()}function ro(){return ro="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,n){var r=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=oo(t)););return t}(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(arguments.length<3?t:n):o.value}},ro.apply(null,arguments)}function oo(t){return oo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},oo(t)}function io(t,e){return io=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},io(t,e)}const ao=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e,n){return e=oo(e),function(t,e){if(e&&("object"==Xr(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,no()?Reflect.construct(e,n||[],oo(t).constructor):e.apply(t,n))}(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&io(t,e)}(e,t),n=e,r=[{key:"_getSchemaId",value:function(){return lr}},{key:"_getActionType",value:function(){return"impression"}},{key:"_getEvents",value:function(t){var n=this,r=[];return t.impressions.forEach((function(t){var o,i,a;r.push.apply(r,function(t){return function(t){if(Array.isArray(t))return Yr(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return Yr(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Yr(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}((o=e,i=n,"function"==typeof(a=ro(oo(1&3?o.prototype:o),"_getEvents",i))?function(t){return a.apply(i,t)}:a)([t,ce()])))})),r}}],r&&to(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r}(Gr);function uo(t){return uo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},uo(t)}function co(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,fo(r.key),r)}}function fo(t){var e=function(t){if("object"!=uo(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=uo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==uo(e)?e:e+""}function so(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(so=function(){return!!t})()}function lo(t){return lo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},lo(t)}function po(t,e){return po=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},po(t,e)}var yo=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e,n){return e=lo(e),function(t,e){if(e&&("object"==uo(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,so()?Reflect.construct(e,n||[],lo(t).constructor):e.apply(t,n))}(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&po(t,e)}(e,t),n=e,(r=[{key:"process",value:function(t){V(t.config),U(t.config,(function(e,n){if(!t.overwrite){var r=Nt.get(e);$(r)?n=[].concat(r,n):"object"===uo(r)&&(n=N(r,n))}Nt.set(e,n)}))}}])&&co(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r}(Cn);function vo(t){return vo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},vo(t)}function bo(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,mo(r.key),r)}}function mo(t){var e=function(t){if("object"!=vo(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=vo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==vo(e)?e:e+""}function ho(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(ho=function(){return!!t})()}function go(t){return go=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},go(t)}function wo(t,e){return wo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},wo(t,e)}var Oo=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e,n){return e=go(e),function(t,e){if(e&&("object"==vo(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,ho()?Reflect.construct(e,n||[],go(t).constructor):e.apply(t,n))}(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&wo(t,e)}(e,t),n=e,(r=[{key:"process",value:function(t){ar(t.name,{callback:t.callback,schemaIds:t.schemaIds,batch:t.batch,fields:t.fields})}}])&&bo(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r}(Cn);function jo(t){return jo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},jo(t)}function _o(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,So(r.key),r)}}function So(t){var e=function(t){if("object"!=jo(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=jo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==jo(e)?e:e+""}function Po(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Po=function(){return!!t})()}function Eo(t,e,n,r){var o=ko(To(1&r?t.prototype:t),e,n);return 2&r&&"function"==typeof o?function(t){return o.apply(n,t)}:o}function ko(){return ko="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,n){var r=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=To(t)););return t}(t,e);if(r){var o=Object.getOwnPropertyDescriptor(r,e);return o.get?o.get.call(arguments.length<3?t:n):o.value}},ko.apply(null,arguments)}function To(t){return To=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},To(t)}function Co(t,e){return Co=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Co(t,e)}var Io,xo,Ro=function(t){var e={};if($(t))for(var n=0;n<t.length;n++){var r=t[n];$(r)&&(e[r[0]]=K(r))}return e},Lo=function(t){return G(t,"^",",")},Ao=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e,n){return e=To(e),function(t,e){if(e&&("object"==jo(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Po()?Reflect.construct(e,n||[],To(t).constructor):e.apply(t,n))}(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Co(t,e)}(e,t),n=e,(r=[{key:"setTDataString",value:function(t){Eo(e,"merge",this,3)([Lo(t)])}},{key:"stringify",value:function(){var t,e=V(this.getProperties());(t=e)&&U(t,(function(e,n){var r=(e+"").split(".").join("_");delete t[e],t[r]=n}));var n=H(e,"^",",");if(n)return n}},{key:"set",value:function(t,n){"object"!==jo(n)&&"[object Function]"!=={}.toString.call(n)?Eo(e,"set",this,3)([t,n]):I("Invalid key/value pair found in Traffic's custom properties (tData). Objects, arrays, and functions are not supported.\n\nKey: '".concat(t,"' Value: '").concat(n,"' Type: '").concat(jo(n),"'."))}}])&&_o(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r}(et),Do=function(t){var e=new Ao;return e.merge(t.custom_properties),t.traffic&&e.merge(t.traffic.customProperties),e},Bo=function(t){var e=t.traffic;return e&&function(t,e){var n=t.eid||e&&e();if(!n)throw"";if(t.eid=n.toLowerCase(),function(t){return t.match(xr)}(t.eid))throw t.eid,""}(e),t};function Mo(t){return Mo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mo(t)}function Fo(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,qo(r.key),r)}}function qo(t){var e=function(t){if("object"!=Mo(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=Mo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Mo(e)?e:e+""}function Uo(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Uo=function(){return!!t})()}function No(t){return No=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},No(t)}function Vo(t,e){return Vo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Vo(t,e)}var Ko=0,Go=function(){var t=Nt.get("components");if(xo!==t)return xo=t},Ho=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e,n){return e=No(e),function(t,e){if(e&&("object"==Mo(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Uo()?Reflect.construct(e,n||[],No(t).constructor):e.apply(t,n))}(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Vo(t,e)}(e,t),n=e,(r=[{key:"_getWebContext",value:function(){return{pageViewCount:Ko}}},{key:"_getEvents",value:function(t){var e=Do(t);return[{schemaId:"urn:shared:user:event:/data-platform/signals/page-view/v1",data:{forensics:{traceIdAdopted:g(hn.get("traced")),components:Go()},traffic:Lr({customProperties:e.getProperties()}),producerEventId:this.internal.eventId}}]}},{key:"preProcess",value:function(t){var e=t.path||t.virtual_path||t.virtualPath,n=void 0!==e;if(n||(e=s().location.pathname),Io===e)throw"cmd: LogPageRequest duplicate page request detected, ignoring duplicate path";Io=e,hn.set("corrid",Math.floor(2147483647*Math.random())),n&&(ee(),hn.set("virtual_path",e)),gn.incrementPageCount(),Ko+=1,hn.get("trace_id")||hn.merge(Pn())}}])&&Fo(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r}(_r),zo=function(t){return[(new We).map("element",[new ze("area").required().pattern(Pe),new ze("product").required().pattern(Pe),new ze("section").required().pattern(Ee),new ze("widget").required().pattern(Pe)]).required().extend(t)]},Wo=function(t){return[(new We).map("traffic",[new He("customProperties")]).extend(t)]},$o=function(){return[(new We).substitute(Wo([new He("eid")]))]},Qo=function(){return[(new We).substitute(zo()),(new We).substitute($o()),(new We).map("promotion",[new He("id").required(),new He("name"),new He("creative"),new He("position")]),(new We).map("product",[new He("actionCode").required().allowedValues(["add_to_cart","product_detail"]),(new We).map("basket",[new He("couponCode"),new He("currencyCode"),new He("itemTrackingCode")]),(new $e).map("products",[new He("productId").required(),new He("productName"),new He("productInstanceId"),new He("priceUsd"),new He("quantity").required(),new He("couponCode"),new He("itemTrackingCode")]),(new $e).map("packages",[new He("id"),new He("priceUsd"),new He("quantity"),new He("category")])])]};const Jo={add_click:{v1:{handler:Zr,properties:[(new We).substitute(Qo()).transform(Bo)]}},add_element_action:{v1:{handler:Gr,properties:[(new We).substitute([(new We).substitute(zo([new He("action").required().allowedValues(["blur","drag","focus","hover","load","scroll"])])),(new We).substitute($o())]).transform(Bo)]}},add_impressions:{v1:{handler:ao,properties:[(new $e).map("impressions",[(new We).substitute(Qo()).transform(Bo)]).withMinElements(1)]}},add_virtual_page_view:{v1:{handler:Ho,properties:[new He("virtualPath").required(),(new We).substitute(Wo())]}},add_micro_events:{v1:{handler:Ir,properties:[new He("apiKey").required(),new He("schemaId").required(),(new $e).map("events",[new He("schemaId").required(),new He("data").required()]).withMinElements(1),(new We).map("businessContext",[new He("schemaId").required(),new He("data").required()])]}},subscribe:{v1:{handler:Oo,properties:[new He("name").required(),new He("callback").required(),new He("schemaIds"),new He("fields"),new He("batch")]}},set_config:{v1:{handler:yo,properties:[(new We).map("config",[(new $e).map("components",[new He("name").required(),new He("version").required().outputType(Se)]),(new $e).map("experiments",[new He("id").required(),new He("variant").required()]),(new We).map("cookie",[new He("sameSite")]),(new We).map("page",[new He("spa"),new He("id"),new He("contentGroup"),new He("referrerExclusion")]),(new We).map("identity",[new He("realm")]),(new We).map("site",[new He("privateLabelId")]),(new We).map("timing",[new He("auto")])]).required(),new He("overwrite")]}}};function Zo(t){return Zo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Zo(t)}function Xo(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Yo(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Xo(Object(n),!0).forEach((function(e){ti(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Xo(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function ti(t,e,n){return(e=function(t){var e=function(t){if("object"!=Zo(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=Zo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Zo(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var ei=function(t){var e=Xn.get(Zn)||{};Xn.set(Zn,Yo(Yo({},e),t))},ni=function(t){var e=t.event,n=e.schemaId,r=e.data,o=r.experimentId,i=r.variantId,a=r.fqAppId,u=r.settingValue,c=r.setting;if(n===vr)ei(ti({},o,i));else if(n===br&&"hivemind"===a){var f=JSON.parse(u);ei(ti({},c,f.cohortId))}};function ri(t){return ri="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ri(t)}function oi(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function ii(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?oi(Object(n),!0).forEach((function(e){ai(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):oi(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function ai(t,e,n){return(e=function(t){var e=function(t){if("object"!=ri(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=ri(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==ri(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var ui=!1,ci=function(t){var e=t.trfqMethods,n=t.legacyName,r=t.dataLayerConfig,o=t.globalContextConfig,i=t.hooks,a=i.onLoad,u=i.beforeLoad,c=i.beforeEvent,f=i.getEnvironment;if(!ui){ui=!0,zt("build","env",f&&f()||Ht("build","env")),hn.merge(Pn()),Jn.init(ii({traceId:hn.get("trace_id")},o)),_e(Jo),function(){var t=s();if(t.PerformanceObserver){var e=new t.PerformanceObserver((function(t){var e=t.getEntries(),n=e[e.length-1];n.renderTime&&(rt.lcp=Math.round(n.startTime))}));try{e.observe({type:"largest-contentful-paint",buffered:!0})}catch(t){}}}();var p=new mt("complete"===l().readyState,(function(t){s().addEventListener("load",t)})),y=gn.getSession();u&&u();var v=new cn(ii({autoPageViewDisabled:Nt.get("".concat(n,".manualPageRequest")),beforeEvent:c},r));p.registerOnLoadFn((function(){var t,r,o;a&&a(),v.start(),(t=Ht("experiments"))&&ei(t.reduce((function(t,e){return t[e.id]=e.variant,t}),{})),ar("scc-experiment-tracker",{callback:ni,schemaIds:[vr,br]}),new mn(e),Ht("timing","auto")&&function(t,e,n){if(!yt)if(yt=!0,pt())lt(t,st(t,e),n);else var r=setInterval((function(){pt()&&(clearInterval(r),lt(t,st(t,e),n))}),250)}(n,"auto"),Ht("eventBus","enabled")&&(D(It,[]),Ct=new Tt(xt),s()._signalsDataLayer=s()._signalsDataLayer||[],s()._signalsDataLayer.push({schema:"subscribe",data:{name:"scc-plugin-sink-event-bus",batch:!0,fields:["routing"],callback:function(t){var e=t.schemaId,n=t.global,r=t.businessContext,o=t.events,i=t.routing,a=i.eventBusApiKey,u=i.hasConversion,c=i.globalContextVersion;Ct.pushEvent({apiKey:a,businessContext:r,contextVersion:c,schemaId:e,global:n,events:o},u)}}})),r=Ht("cdep","appId"),o=y.visitorGuid,r&&dr({events:[{schemaId:"urn:shared:user:event:/cdep/app-evaluation/v1",data:{cdepAppId:r,bucketingId:o,bucketingIdType:"visitorId",commitHash:Ht("client","hash")}}]})})),C("SCC STARTED",Nt.getProperties())}};function fi(t){return fi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fi(t)}function si(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,li(r.key),r)}}function li(t){var e=function(t){if("object"!=fi(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=fi(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==fi(e)?e:e+""}function pi(t){return pi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},pi(t)}function yi(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,mi(r.key),r)}}function vi(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(vi=function(){return!!t})()}function bi(t){return bi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},bi(t)}function di(t,e){return di=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},di(t,e)}function mi(t){var e=function(t){if("object"!=pi(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=pi(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==pi(e)?e:e+""}var hi="_tccl_visitor",gi="_tccl_visit",wi=function(t){function e(){var t,n,r,o;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var i=arguments.length,a=new Array(i),u=0;u<i;u++)a[u]=arguments[u];return t=function(t,e,n){return e=bi(e),function(t,e){if(e&&("object"==pi(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,vi()?Reflect.construct(e,n||[],bi(t).constructor):e.apply(t,n))}(this,e,[].concat(a)),n=t,o="_scc_session",(r=mi(r="_sessionCookieName"))in n?Object.defineProperty(n,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[r]=o,t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&di(t,e)}(e,t),n=e,(r=[{key:"getSession",value:function(){var t=_n(hi),e=_n(gi);t||e?(t||(t=ce()),e||(e=ce())):t=e=ce(),jn(hi,t,525600,"/"),jn(gi,e,30,"/");var n=Sn(this._sessionCookieName);return{visitorGuid:t,visitGuid:e,pageCount:w(n.pc)||0}}}])&&yi(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r}(function(){return t=function t(){var e,n,r;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e=this,r=void 0,(n=li(n="_sessionCookieName"))in e?Object.defineProperty(e,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[n]=r},(e=[{key:"getSession",value:function(){}},{key:"saveSessionCookie",value:function(t){var e=H(t,"&","=");jn(this._sessionCookieName,e,20,"/")}},{key:"incrementPageCount",value:function(){var t=Sn(this._sessionCookieName),e=w(t.pc)||0;t.pc=e+1,t.C_TOUCH=W(),this.saveSessionCookie(t)}}])&&si(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e}());function Oi(t){return Oi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Oi(t)}function ji(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,_i(r.key),r)}}function _i(t){var e=function(t){if("object"!=Oi(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=Oi(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Oi(e)?e:e+""}function Si(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Si=function(){return!!t})()}function Pi(t){return Pi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Pi(t)}function Ei(t,e){return Ei=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Ei(t,e)}var ki=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e,n){return e=Pi(e),function(t,e){if(e&&("object"==Oi(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Si()?Reflect.construct(e,n||[],Pi(t).constructor):e.apply(t,n))}(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ei(t,e)}(e,t),n=e,(r=[{key:"_getEvents",value:function(t){var e=t.eid,n=t.type.toLowerCase(),r=function(t){return"click"===t?sr:"impression"===t?lr:["blur","drag","focus","hover","load","scroll"].includes(t)?pr:yr}(n),o=Do(t).getProperties();return[{schemaId:r,data:r!==yr?Ar(this.internal.eventId,e,n,o):{traffic:Lr({customProperties:o,eid:e,eventCategory:n}),producerEventId:this.internal.eventId}}]}}])&&ji(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r}(_r);function Ti(t){return Ti="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ti(t)}function Ci(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Ii(r.key),r)}}function Ii(t){var e=function(t){if("object"!=Ti(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=Ti(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Ti(e)?e:e+""}function xi(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(xi=function(){return!!t})()}function Ri(t){return Ri=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Ri(t)}function Li(t,e){return Li=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Li(t,e)}var Ai=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e,n){return e=Ri(e),function(t,e){if(e&&("object"==Ti(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,xi()?Reflect.construct(e,n||[],Ri(t).constructor):e.apply(t,n))}(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Li(t,e)}(e,t),n=e,(r=[{key:"_getApiKey",value:function(){return b("eventBus","rigorApiKey")}},{key:"_getEvents",value:function(t){var e=t.is_hard_navigation?"hard":"soft",n=t.timing_object;if(n){if(n.navigationStart)return[{schemaId:"urn:shared:user:event:/rigor/page-navigation/v1",data:Br(this.internal.eventId,n,t.custom_properties,e)}];if(n.timeToInteractive)return[{schemaId:"urn:shared:user:event:/rigor/page-navigation-delayed/v1",data:Mr(this.internal.eventId,n,t.custom_properties)}]}}}])&&Ci(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r}(_r),Di=function(){return[new He("custom_properties").optional()]};const Bi={add_event:{v1:{handler:ki,properties:[new He("type").required(),(new We).substitute([new He("eid").optional()]),(new We).substitute(Di())]}},add_page_view:{v1:{handler:Ho,properties:[new He("path").optional(),(new We).substitute(Di())]}},add_perf:{v1:{handler:Ai,properties:[new He("timing_object").required(),new He("is_hard_navigation").optional(),(new We).substitute(Di())]}}},Mi=function(t,e,n){s()._expDataLayer.push({schema:"add_event",version:"v1",data:{type:t,eid:e,custom_properties:Ro(n)}})},Fi=function(t,e){s()._expDataLayer.push({schema:"add_perf",version:"v1",data:{timing_object:t,custom_properties:Ro(e)}})};var qi,Ui={session:function(){return gn.getSession()}},Ni={pageCorrelationId:function(){return hn.get("corrid")}},Vi="tccl";return qi=new wi,gn=qi,_e(Bi),function(t){var e=t.legacyName,n=t.configManagerProps,r=t.hooks.beforeInit;!function(){try{var o,i=l().cookie;Object.defineProperty(document,"cookie",{get:function(){return i},set:function(t){(o=o||Object.getOwnPropertyDescriptor(Document.prototype,"cookie")).set.call(document,t),i=o.get.call(document)},configurable:!0,enumerable:!0}),function(){if(a=p().userAgent.toLowerCase(),!/msie [1-8]\./.test(a))if(i="_".concat(e,"Internal"),c=i,void 0!==s()[c]||(s()[c]={},0))I("SCC Library has already been loaded on page");else if(function(t){var e=t.dataLayerName,n=t.clientConfigPrefixes,r=t.ignoredProperties,o=t.legacyName;Vt=o,Qt=r||[],Gt=e,$t=n,Kt=s()[Gt]||[],B(Gt,Kt),D("".concat(Gt,"Log"),Jt),ee()}(n),r&&r(),"off"!==Nt.get("".concat(e,".status"))){var o=l();"prerender"!==o.visibilityState?ci(t):o.addEventListener("visibilitychange",(function(){"prerender"!==o.visibilityState&&"unloaded"!==o.visibilityState&&ci(t)}))}var i,a}()}finally{delete l().cookie}}()}({legacyName:Vi,trfqMethods:n,configManagerProps:{legacyName:Vi,dataLayerName:"_trfd",clientConfigPrefixes:[Vi]},dataLayerConfig:{pageViewSchema:"add_page_view",autoPageViewDisabled:!1,immediateSchemas:["add_page_view"]},globalContextConfig:{contextFnMap:{"context.userType":function(){return"c2"},"context.visitorId":function(t){return t.session.visitorGuid},"context.sessionId":function(t){return t.session.visitGuid},"page.sessionPageViewCount":function(t){return t.session.pageCount}},paramsFnMap:Ui},hooks:{getEnvironment:function(){var t=Nt.get("".concat(Vi,".baseHost"));if(t)return"secureserver.net"===t?"prod":"test-secureserver.net"===t?"test":"dev"},beforeLoad:function(){Yn.init({contextFnMap:Ni})}}}),{}})()));