define(["jquery","wsbcore/helper","appconfig"],function(e,n,l){function i(e){var n=s.googleMapBaseUrl||e.googleMapBaseUrl;return e.loadUsingClient&&s.clientId||"publish"===e.loadingMode&&e.clientId?n+="&client="+(s.clientId||e.clientId):s.apiKey&&(n+="&key="+(e.apiKey||s.api<PERSON>ey)),(e.loadUsingClient&&s.channel||"publish"===e.loadingMode&&e.channel)&&(n+="&channel="+(s.channel||e.channel)),n+="&language="+(e.language||s.language),n+="&callback="+(e.callbackName||s.callbackName)}function o(){require(["/i18n/resources/client","wsbcore/growl"],function(n){e("<div></div>").sfGrowl({title:n.resources.Client__Designer__Yikes_hit_a_snag,content:n.resources.Server__There_has_been_an_unexpected_error,icon:"error"})})}function a(e,n){"editor"===n&&o(),e.resolve(!1)}function r(){window.google&&(window.google=void 0)}function c(l,o){var c=i(o),t=o.loadingMode||s.loadingMode;return r(),n.require([c],e.noop,a.bind(null,l,t)),l}function t(e,n){n?u.withClientId=e:u.withApiKey=e}function g(e,n){window[e]=function(){n.resolve(window.google.maps)}}function d(n){var l=n||{},i=l.loadUsingClient?u.withClientId:u.withApiKey,o=l.callbackName||s.callbackName;return i||(i=e.Deferred(),g(o,i),c(i,l),t(i,l.loadUsingClient)),i.promise()}var s={loadUsingKey:!0,loadingMode:"editor",language:"en_US",callbackName:"onGoogleMapsReady",apiKey:l.googleMapsApiKey,clientId:l.googleMapsClientID,channel:l.googleMapsDesignerChannel,googleMapBaseUrl:l.googleMapsApiBaseUrl},u={withClientId:null,withApiKey:null};return d});
//# sourceMappingURL=googleMaps.js.map