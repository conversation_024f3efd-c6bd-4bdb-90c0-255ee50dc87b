define(["jquery","common/geo/googleMaps","common/geo/getClosestGeoLocation"],function(o,n,t){function e(o){t.initMapsApi(o),l()}function l(){for(;h.length>0;)h.pop()()}function a(){return{center:new google.maps.LatLng(33.6744664,-112.1386465),zoom:14,lat:"33.6744664",lng:"-112.1386465",mapTypeId:google.maps.MapTypeId.ROADMAP,disableDefaultUI:!0,zoomControl:!0,zoomControlOptions:{position:google.maps.ControlPosition.LEFT_BOTTOM},streetViewControl:!0,streetViewControlOptions:{position:google.maps.ControlPosition.RIGHT_BOTTOM}}}function i(o){var n=g(o);this.marker=new google.maps.Marker({map:this.map,position:n})}function r(){this.map.controls[google.maps.ControlPosition.TOP_LEFT].push(s(this.address))}function s(n){var t=o("<div></div>",{text:n,css:{margin:"5px",padding:"10px",background:"white",color:"black",border:"1px solid black","line-height":"initial"}});return t.get(0)}function g(o){var n=parseFloat(o.lat),t=parseFloat(o.lng);return new google.maps.LatLng(n,t)}function p(o){var n=g(o);this.map.setCenter(n)}function c(o){return o.lat&&o.lng}function u(o){if(c(o)){var n=this;p.call(n,o),i.call(n,o),r.call(n)}}function d(n,e,l){if(window.google){var i=o.extend({},a(),l||{}),r={map:new google.maps.Map(n,i),address:e,marker:null};i.lng||i.lat?u.call(r,{lat:i.lat,lng:i.lng}):e&&t.getCoordinates(e,function(o){u.call(r,{lat:o.lat(),lng:o.lng()})})}else h.push(d.bind(null,n,e,l))}function m(){var o=n({loadUsingClient:!0});o.done(function(o){e(o)})}function f(o,t,l){var a=n({loadingMode:"publish",clientId:o,channel:t,googleMapBaseUrl:l});a.done(function(o){e(o)})}var h=[];return{drawMap:d,runEditorMode:m,runPublishMode:f}});
//# sourceMappingURL=mapGenerator.js.map