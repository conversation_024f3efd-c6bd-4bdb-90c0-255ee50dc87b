define([],function(){function o(){a||(a=new i.Geocoder)}function n(o,n){n(o?s(o):"")}function e(o,n){if(o){var e=o.geometry;n(e&&e.location||{})}else n({})}function t(t,s,r){o();var d="getCoordinates"===t?e:n,c="getCoordinates"===t?{}:"";s?a.geocode({address:s},function(o,n){var e=n===i.GeocoderStatus.OK?o[0]:s;d(e,r)}):r(c)}function s(o){var n=o;return o.formatted_address?n=o.formatted_address:o.address_components&&(n=r(o)),n}function r(o){return[o.address_components[0]&&o.address_components[0].short_name||"",o.address_components[1]&&o.address_components[1].short_name||"",o.address_components[2]&&o.address_components[2].short_name||""].join(" ")}function d(o){i?o():require(["common/geo/googleMaps"],function(n){var e=n();e.done(function(n){i=n,o()})})}var a,i=window.google&&window.google.maps;return{getClosestStreetAddress:function(o,n){d(function(){t("getAddress",o,n)})},getCoordinates:function(o,n){d(function(){t("getCoordinates",o,n)})},initMapsApi:function(o){i=o}}});
//# sourceMappingURL=getClosestGeoLocation.js.map