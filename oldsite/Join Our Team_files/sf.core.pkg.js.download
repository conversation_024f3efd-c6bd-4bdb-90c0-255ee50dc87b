/*! duel v2.5.8 - Built 2023-06-07, 3:14 PM MST - Copyright (c) 2023 */
var $sfTabs=null;define("starfield/sf.tabs",["jq!starfield/jquery.mod","css!starfield/sf.core.css"],function(){return function(a){return a.fn.sfTabs=function(b,c){var d=a(this),e=d.data("tabs-id");(null==e||0==e.length)&&(e="sfTabs"+Math.round(65535*Math.random()),d.data("tabs-id",e));var f=a.fn.sfTabs.instances[e];return null!=f?(f.me_this=this,f.me=d,f.fn(f,b,c)):(f=a.fn.sfTabs.instances[e]={me_this:this,me:a(this),initialized:!1,fn:function(b,c,e){function f(c){if(b.meId=b.me.data("tabs-id"),"undefined"==typeof b.settings){var d={context:self,tabControlContext:self,bborder:!1,ignoreRedundantCallbacks:!0,mode:"classic",onTabSelected:null,onTabDeselected:null};b.settings=a.extend(!0,d,c)}else b.settings=a.extend(!0,b.settings,c);b.context=$sf.util.context(b.settings.tabControlContext,b.settings.context),1==b.initialized||(b.tabs=null,b.tabLastSelected=null,b.tabsHeight=0),j(),0==b.initialized&&(b.initialized=!0)}function g(c){if(null==b.tabs)return null;var d=b.tabs.find("li");if(0>c||c>=d.length)return null;var e=a(d[c]),f=b.me.children(".sf-tabs-content:eq("+c+")");return 0==f.length?null:e.is(".sf-tabs-disabled-tab")}function h(c){if(null!=b.tabs){var d=b.tabs.find("li");if(!(0>c||c>=d.length)){var e=a(d[c]),f=b.me.children(".sf-tabs-content:eq("+c+")");0!=f.length&&e.removeClass("sf-tabs-disabled-tab")}}}function i(c){if(null!=b.tabs){var d=b.tabs.find("li");if(!(0>c||c>=d.length)){var e=a(d[c]),f=b.me.children(".sf-tabs-content:eq("+c+")");0!=f.length&&e.addClass("sf-tabs-disabled-tab")}}}function j(){null!=b.tabs&&(a("*",b.tabs).unbind(),b.tabs.remove(),b.tabs=null,b.tabsHeight=0),b.wrapper&&b.wrapper.remove();var c,d=b.me.children(".sf-tabs-content");if(0!=d.length){var e=null;if(e=e=b.me.children(".sf-tabs,.sf-g-tabs,.sf-tabs-wiz,.sf-tabs-toggle"),!(e.length>0)){switch(b.tabs=a("<ul/>").addClass("sf-tabs-menu"),b.wrapper=b.wizard=b.toggle=null,b.settings.mode){case"wizard":b.tabs.addClass("sf-tabs-wiz-list"),b.wrapper=b.wizard=a("<div class='sf-tabs-wiz' />").append(b.tabs);break;case"toggle":b.tabs.addClass("sf-tabs-toggle-list"),b.wrapper=b.toggle=a("<div class='sf-tabs-toggle' />").append(b.tabs);break;default:b.tabs.addClass("sf-tabs"),b.settings.bborder===!0&&b.tabs.addClass("sf-tabs-bborder")}var f=d.filter(".sf-tabs-actv-content").length>0,g=!1;for(c=0;c<d.length;c++){var h=a(d[c]),i=a("<li />"),j=h.attr("data-icon-class"),l=a('<a href="#" onclick="return false" />').appendTo(i),m=null;j?(m=a("<span/>").addClass(j),l.append(m).append(h.attr("data-title"))):(b.wizard?m=a("<span/>").addClass("sf-tabs-wiz-txt").append(h.attr("data-title")):b.toggle?m=a("<span/>").addClass("sf-tabs-toggle-txt").append(h.attr("data-title")):l.append(h.attr("data-title")),m&&l.append(m));var n=h.is(".sf-tabs-hidden");1==n&&i.addClass("sf-tabs-hidden").hide(),1==h.is(".sf-tabs-actv-content")||0==f&&0==n?(f=!0,g=!0,b.wizard?i.addClass("sf-tabs-wiz-step-current"):b.toggle?i.addClass("sf-tabs-toggle-actv"):i.addClass("sf-tabs-actv-tab sf-g-tab-active"),h.addClass("sf-tabs-actv-content").show(),b.wizard?i.addClass("sf-tabs-wiz-step-current"):b.toggle&&i.addClass("sf-tabs-toggle-actv"),(0==b.settings.ignoreRedundantCallbacks||null!=b.tabLastSelected&&h[0]!=b.tabLastSelected)&&1==a.isFunction(b.settings.onTabSelected)&&b.settings.onTabSelected(b.me,c,h),b.tabLastSelected=h[0]):(b.wizard&&i.addClass(g?"sf-tabs-wiz-step-incomplete":"sf-tabs-wiz-step-complete"),h.hide()),b.tabs.append(i),i.bind("click",k)}b.wizard?b.tabs.children("li:last").children("a").addClass("sf-tabs-wiz-list-last"):b.toggle&&b.tabs.children("li:last").addClass("sf-tabs-toggle-list-last");var o=b.context.isSingle()?b.me:b.context.ext.iframe();0==o.parent().length?o.prepend(b.wrapper?b.wrapper:b.tabs):o.before(b.wrapper?b.wrapper:b.tabs),s(),b.tabsHeight=b.tabs.outerHeight()}}}function k(c){if(a(document.body).trigger("click",{target:this,sourcetype:"tab"}),null==b.tabs)return!1;var d=a(this).closest("li");if(0==d.length||d.is(".sf-tabs-actv-tab")||d.is(".sf-g-tab-active")||d.is(".sf-tabs-wiz-step-current")||d.is(".sf-tabs-toggle-step-current")||d.is(".sf-tabs-wiz-step-incomplete"))return!1;var e=b.tabs.find("li"),f=a.inArray(d[0],e);if(0>f)return!1;var g=b.me.children(".sf-tabs-content:eq("+f+")");return 0==g.length?!1:(n(f),!1)}function l(){0!=b.initialized&&(null!=b.tabs&&(a("*",b.tabs).unbind(),b.tabs.remove(),b.wrapper&&b.wrapper.remove(),b.tabs=b.wrapper=null,b.tabsHeight=0,b.tabLastSelected=null),b.me.children(".sf-tabs-content").show(),b.initialized=!1,delete b.settings)}function m(){var c=b.me.children(".sf-tabs-content"),d=c.filter(".sf-tabs-actv-content");return 0==c.length||0==d.length?-1:a.inArray(d[0],c)}function n(c){if(null!=b.tabs){var d=b.tabs.find("li");if(!(0>c||c>=d.length)){var e=a(d[c]),f=b.me.children(".sf-tabs-content:eq("+c+")");if(0!=f.length&&1!=e.is(".sf-tabs-disabled-tab")){if(1==a.isFunction(b.settings.onTabSelected)&&0==b.settings.onTabSelected(b.me,c,f))return!1;var g;if(d.removeClass("sf-tabs-actv-tab sf-g-tab-active sf-tabs-wiz-step-current sf-tabs-toggle-actv sf-tabs-wiz-step-incomplete sf-tabs-wiz-step-complete"),b.wizard){for(g=0;c>g;g++)a(d[g]).addClass("sf-tabs-wiz-step-complete");for(g=c+1;g<d.length;g++)a(d[g]).addClass("sf-tabs-wiz-step-incomplete");e.addClass("sf-tabs-wiz-step-current")}else b.toggle?e.addClass("sf-tabs-toggle-actv"):e.addClass("sf-tabs-actv-tab sf-g-tab-active");e.show(),b.me.children(".sf-tabs-content").not(f).each(function(b,c){o(a(c))}).removeClass("sf-tabs-actv-content").hide(),f.addClass("sf-tabs-actv-content").show()}}}}function o(c){if(1==a.isFunction(b.settings.onTabDeselected)){var d=a.inArray(c,b.me.children(".sf-tabs-content"));b.settings.onTabDeselected(b.me,d,c)}}function p(c){if(null==b.tabs)return null;var d=b.tabs.find("li");if(0>c||c>=d.length)return null;var e=a(d[c]),f=b.me.children(".sf-tabs-content:eq("+c+")");return 0==f.length?null:!e.is(":hidden")}function q(c){if(null!=b.tabs){var d=b.tabs.find("li");if(!(0>c||c>=d.length)){var e=a(d[c]),f=b.me.children(".sf-tabs-content:eq("+c+")");if(0!=f.length&&(e.addClass("sf-tabs-hidden").hide(),f.addClass("sf-tabs-hidden").hide(),m()==c)){b.me.children(".sf-tabs-content").removeClass("sf-tabs-actv-content").hide();for(var g=0;g<d.length;g++)if(g!=c&&1==p(g)){n(g);break}}}}}function r(c){if(null!=b.tabs){var d=b.tabs.find("li");if(!(0>c||c>=d.length)){var e=a(d[c]),f=b.me.children(".sf-tabs-content:eq("+c+")");0!=f.length&&(e.removeClass("sf-tabs-hidden").show(),f.removeClass("sf-tabs-hidden"),-1==m()&&n(c))}}}function s(c){if(null!=b.tabs){var d=b.tabs.find("li");if("number"!=typeof c)for(var e=0;e<d.length;e++){var f=a(d[e]),g=b.me.children(".sf-tabs-content:eq("+e+")");0!=g.length&&t(f,g.attr("data-class"))}else if(c>=0&&c<d.length){var f=a(d[c]),g=b.me.children(".sf-tabs-content:eq("+c+")");if(0==g.length)return;t(f,g.attr("data-class"))}}}function t(a,b){if(a&&0!==a.length&&"string"==typeof b){for(var c=a.attr("class")||"",d=c.split(" "),e=[],f=0;f<d.length;f++){var g=d[f];(/sf\-tabs[a-z0-9\_\.\-]*/i.test(g)||/sf\-g\-tab[a-z0-9\_\.\-]*/i.test(g))&&e.push(g)}var h=e.join(" ")+" "+b;a.attr("class",h)}}function u(){return b.tabs?b.tabs.children().length:0}function v(){for(var a=u(),b=a,c=0;a>c;c++)b-=p(c)?0:1;return b}if(0==d.length)return d;if(b.logger=$sf.util.logger(d.attr("id")).log,"string"==typeof c){var w=c.toLowerCase();switch(w){case"isinitialized":return b.initialized;case"destroy":l();break;case"isdisabled":return g(e);case"disable":i(e);break;case"enable":h(e);break;case"getselected":return m(e);case"select":n(e);break;case"isvisible":return p(e);case"hide":q(e);break;case"show":r(e);break;case"updateTab":s(e);break;case"count":return u();case"visiblecount":return v();case"init":default:f(e)}}else e=c,f(e);return b.me}},f.fn(f,b,c))},a.fn.sfTabs.instances={},a.fn.sfTabs.globals={},a.fn.sfTabs}($sf.getjQuery())}),define("starfield/sf.growl",["i18n!starfield/sf.i18n/duel","css!starfield/sf.core.css","jq!starfield/jquery.mod"],function(a){return function(b){var c={options:{icon:"",title:"",content:"",location:{},fadetime:0,width:null,onClose:null,location:{}},fadeTimer:null,_create:function(){var a=this;"string"==typeof a.options.title_text&&(a.options.title=a.options.title_text),"string"==typeof a.options.paragraph_html&&(a.options.content=a.options.paragraph_html),a._buildMain(),a._buildGrowl()},_buildMain:function(){var c=this,d=c.container=b("#sf_growl"),e=c.options;d.length<1&&(d=b("<div/>").attr("id","sf_growl"),$sf.util.ua.ie6&&d.css("position","absolute"),"undefined"!=typeof e.location.top&&d.css("top",e.location.top+"px"),"undefined"!=typeof e.location.right&&d.css("right",e.location.right+"px"),"undefined"!=typeof e.location.bottom&&d.css("bottom",e.location.bottom+"px"),"undefined"!=typeof e.location.left&&d.css("left",e.location.left+"px"),d.appendTo($sf.util.zIndex.getLayer("abstop",b("body"))),c.container=d),null!=e.width&&d.css("width",e.width);var f=c.closeDiv=b("#sf_growl_close_all",c.container);f.length<1&&(c.closeDiv=b("<div/>").attr("id","sf_growl_close_all").html("[ "+a["sf.growl"].closeAll+" ]").prependTo(c.container).bind("click.sf_growl_close_all",function(a){c.destroyAll()}))},_buildGrowl:function(){var a=this,c=a.options;a.element.addClass("sf_growl"),a.growl=b("<div/>").addClass("sf_growl_bg").append(b("<a/>").addClass("sf_growl_close sf_growl_sprite").bind("click.sf_growl",function(d){return 1!=b.isFunction(c.onClose)||c.onClose(me)?(a._fade(),!1):!1}));var d=b("<div/>").addClass("sf_growl_msg").appendTo(a.growl);if(""!==c.title){var e=null;c.icon.length>0&&(e=b("<div/>").addClass("sf_growl_ico sf_growl_sprite sf_growl_ico_"+c.icon)),null!==e&&d.append(e),d.append(b("<div/>").addClass("sf_growl_title").html(c.title))}d.append(b("<div/>").addClass("sf_growl_content").html(c.content)),a.element.append(a.growl.hide()),a.element.bind("mouseenter.sf_growl",function(){null!==a.fadeTimer&&(clearTimeout(a.fadeTimer),a.fadeTimer=null)}).bind("mouseleave.sf_growl",function(){c.fadetime>0&&(a.fadeTimer=window.setTimeout(function(){a._fade()},c.fadetime))}),a.container.append(a.element),a.growl.fadeIn(600),a.container.children().length>2&&a.closeDiv.css({display:"block"}),c.fadetime>0&&(a.fadeTimer=window.setTimeout(function(){a._fade()},c.fadetime))},_fade:function(){var a=this;this.growl.slideUp(500,function(){a.clear()})},_destroyGrowl:function(a){a="undefined"!=typeof a&&a.is(":sf-growl")?a:this.element;var b=a.children(".sf_growl_bg");a.attr("data-todestroy",!0);var c=function(){a.find("a.sf_growl_close").unbind(),a.unbind(),a.remove()};b.is(":visible")?b.slideUp(500,c):c()},_clearContainer:function(){this.container.children(":not([data-todestroy])").length<3&&this.closeDiv.slideUp(500),1===this.container.children().length&&(this.container.unbind(),this.container.remove())},clear:function(){this._destroyGrowl(),this._clearContainer()},destroy:function(a){this.clear(),this._execSuper("destroy",a)},destroyAll:function(a){var c=this;c.container.children().not(c.element).each(function(){b(this).sfGrowl("destroy")}),c.destroy(a)}};return $sf.util.selector("sf-growl",{root:{selector:".sf_growl",states:{noicon:function(a){return 0===a.find(".sf_growl_ico").length}},tests:{icon:function(a,b){return a.find(".sf_growl_ico_"+b).length>0}}}}),$sf.util.module("Growl",c)}($sf.getjQuery())}),define("starfield/sf.tipper",["i18n!starfield/sf.i18n/duel","css!starfield/sf.core.css","starfield/sf.util","jq!starfield/jquery.mod"],function(a){return function(b){function c(a,c){var d=a;0==d.is(".sf_tipper,.sf-tip")&&(d=b(".sf_tipper,.sf-tip",a)),d.each(function(){var a=b(this),d=a.attr("data-title");("string"!=typeof d||0==d.length)&&(d=a.attr("data-tipper-title"));var e=a.attr("data-content");("string"!=typeof e||0==e.length)&&(e=a.attr("data-tipper-content"));var f=a.attr("data-style");("string"!=typeof f||0==f.length)&&(f=a.attr("data-tipper-style"));var g=a.attr("data-location");("string"!=typeof g||0==g.length)&&(g=a.attr("data-tipper-location"));var h=a.attr("data-width");("string"!=typeof h||0==h.length)&&(h=a.attr("data-tipper-width"));var i=a.attr("data-target");("string"!=typeof i||0==i.length)&&(i=a.attr("data-tipper-target")),i="string"!=typeof i||0==i.length?void 0:b(i),a.sfTipper(c,{style:f,location:g,title:d,content:e,width:h,target:i,initOnly:!0})})}return b.fn.sfTipper=function(d,e){var f=b.extend({wireup:!1},d),g=b(this);if(f.wireup===!0)return delete d.wireup,c(g,d),g;var h=g.data("sf-tipper-id");h&&0!=h.length||(h=Math.round(1e6*Math.random()),g.data("sf-tipper-id",h));var i=b.fn.sfTipper.instances[h];return null!=i?(i.me=g,i.fn(i,d,e)):(i=b.fn.sfTipper.instances[h]={me:g,meId:h,init:!1,settings:null,fn:function(c,d,e){function f(){return"tooltip"==m.style?"tipper":m.style}function i(){m.resize=!0,q[m.style].positionIt(q[m.style].getElements(g))}function j(a){var c=b(a.target);m.resize=!0,q[m.style].positionIt(q[m.style].getElements(g));var d=c.offset().top,e=d+c.height(),f=g.offset().top;(d>=f||f>=e)&&l(a)}function k(a,c){var d=a.indexOf("<!--more-->");if(d>-1){var e=a.substring(0,d),h=a.substring(d+11);if(a=e+'<div class="sf-tipper-more" style="display:none;">'+h+'</div><div class="sf-tipper-morelink"><a href="javascript: void(0);" onclick="return false;">'+m.showMoreText+"</a></div>",h.indexOf("sf-tourpoint-nav")>-1){var i=b("<div>"+a+"</div>"),j=i.find(".sf-tourpoint-nav");j.insertAfter(i.find(".sf-tipper-morelink")),a=i.html()}}c.html(a),c.find(".sf-tipper-morelink a").bind("click",function(){var a=b(this),d=c.find(".sf-tipper-more");if(d.length>0){d.is(":visible")?(d.hide(),a.text(m.showMoreText)):(d.show(),a.text(m.showLessText));var e=f();q[e].positionIt(q[e].getElements(g))}})}function l(a){var c=!1,d=!1;if(a&&a.target){var e=b(a.target);if(e.is(".sf-tipper-close")===!1&&e.closest(".sf-"+f()).length>0&&e.is("a")===!1){if(e.is(".sf-tourpoint-nav-item"))return!1;if(!(e.parent(".sf-tourpoint-nav").length>0))return!1;c=!0}else if(e.is("a")&&e.parent().is(".sf-tipper-morelink"))return d=!0,!0}return 1!=b.isFunction(m.onClose)||m.onClose(g)?(r.fadeTipper(void 0,void 0,c),!0):!1}var m=($sf.util.logger(h.toString(),"duel"),b.extend({style:"tooltip",title:"",content:"",location:"auto",close:!1,closeTippers:void 0,initOnly:!1,onClose:null,onOpen:null,onDisplay:null,autoOpen:!1,context:self,displayContext:self,width:void 0,disableClose:!1,disableFade:!1,attachIcon:!0,wireup:!1,zIndex:null,suppress:!1,suppressTippers:void 0,resume:!1,resumeTippers:void 0,destroy:!1,destroyTippers:void 0,target:void 0,showMoreText:a["sf.tipper"].more,showLessText:a["sf.tipper"].less},c.settings,d,e));c.settings=m,c.defaultWidths={qt:180,tooltip:213,tourpoint:213,howl:400};var n=m.context,o=m.displayContext,p=$sf.util.context(n,o),q={tooltip:{build:function(){return q.tipbox.build("tipper")},getElements:function(a){return q.tipbox.getElements(a)},positionIt:function(a){return q.tipbox.positionIt(a)}},tourpoint:{build:function(){return q.tipbox.build("tourpoint")},getElements:function(a){return q.tipbox.getElements(a)},positionIt:function(a){return q.tipbox.positionIt(a)}},tipbox:{build:function(a){var d=b(o.document.createElement("div")).data("sf-tipper",g.data("sf-tipper")).data("sf-tipper-target",g).attr("class","sf-"+a).attr("data-style",a);m.groupId&&d.attr("data-group",m.groupId),m.zIndex&&d.css("z-index",m.zIndex),c.outside=d,$sf.util.zIndex.getLayer("flyout",b("body:first",p.ext.document()),p.isSingle()?g:p.ext.iframe()).append(d),d.data("OnResize",i),d.data("OnScroll",j),d.data("OnClose",l);var e=b("<div class='sf-tipper-close sf-icn-close2 sf-icn' />"),f=parseInt(m.width);f=isNaN(f)||0===f?c.defaultWidths[a]:f;var h=b("<div class='sf-"+a+"-wrap' />").css("width",f+"px");c.master=h,0==m.disableClose&&h.append(e),d.append(h);var n=b("<div class='sf-"+a+"-body' />");if(h.append(n),"tourpoint"==a&&m.heading){var q=b("<div class='sf-"+a+"-titlebarheading' />");n.append(q),c.heading=q,q.html(m.heading)}else c.heading=null;if(m.title){var q=b("<h6 class='sf-"+a+"-heading' />");n.append(q),c.title=q,q.html(m.title)}else c.title=null;if(m.content){var r=b("<div class='sf-"+a+"-content' />");n.append(r),c.content=r,k(m.content,r)}var s=b(document.createElement("div"));return c.arrow=s,"howl"===a&&s.addClass("sf-"+a+"-arrow-wrap"),d.append(s),{container:d,masterContainer:h,arrowDiv:s}},getElements:function(a){return{container:c.outside,masterContainer:c.master,arrowDiv:c.arrow}},positionIt:function(a){if(a&&a.masterContainer&&a.masterContainer.length){var b="undefined"==typeof m.target?g:m.target,c="sf-"+f()+"-ptr",d="auto"===m.location?"tl":(m.location.indexOf("top")>-1?"b":"t")+(m.location.indexOf("left")>-1?"r":"l");a.arrowDiv.attr("class",c),a.container.width(a.masterContainer.outerWidth()+a.arrowDiv.width()-1);var e=$sf.util.position.get({ideal:d,container:a.container,anchor:b,path:"corner",anchorOffset:p.isSingle()?{t:0,l:0}:p.ext.iframe().offset(),containerOffset:{width:function(){return 3},height:function(){return 0}},centerAgainstAnchor:!0,context:p.ext.window()});a.arrowDiv.addClass(c+"-"+("t"===e.y?"bottom":"top")+("l"===e.x?"right":"left")),a.masterContainer.css({"float":"r"===e.x?"left":"right"}),a.container.css({left:e.coords.left+"px",top:e.coords.top+"px"})}}},qt:{buildContainer:function(){return q.bubble.build("qt")},getElements:function(a){return q.bubble.getElements(a)},load:function(a,b){return q.bubble.load("qt",a,b)},positionIt:function(a){return q.bubble.positionIt(a,"qt","bottom"===m.location?"bottom":"top")}},howl:{buildContainer:function(){return q.bubble.build("howl")},getElements:function(a){return q.bubble.getElements(a)},load:function(a,b){return q.bubble.load("howl",a,b)},build:function(a){var b=q.bubble.buildContainer("howl");return b.data("sf-tipper",g.data("sf-tipper")).data("OnResize",i).data("OnClose",l),c.outside=b,q.bubble.load("howl",b,a),{container:b,el:a}},positionIt:function(a){return q.bubble.positionIt(a,"howl","top"===m.location?"top":"bottom")}},bubble:{buildContainer:function(a){var c=b("body > .sf-"+a+":first",o.document);if(0==c.length){var d=m.zIndex?' style="position: absolute; z-index: '+m.zIndex+'"':"";if(c=b('<div class="sf-'+a+'"'+d+' data-style="'+a+'"><span class="sf-'+a+'-content"></span><span class="sf-'+a+'-arrow-wrap"><span class="sf-'+a+'-arrow"></span></span></div>'),$sf.util.zIndex.getLayer("flyout",b("body:first",p.ext.document()),p.isSingle()?g:p.ext.iframe()).append(c),"howl"==a){var e=b("<div class='sf-tipper-close sf-icn-close2 sf-icn' />");0==m.disableClose&&c.append(e)}}return c},getElements:function(a){return{container:c.outside,el:a}},load:function(a,d,e){d.css({width:"auto",top:0,left:0});var f=e.attr("data-content")?e.attr("data-content"):m.content,g=parseInt(m.width),h=!(isNaN(g)||0>=g),i=h?g:c.defaultWidths[a],j=b("<div>"+f+"</div>").css({"max-width":i,position:"absolute",padding:"0",margin:"0","font-family":"arial, verdana, helvetica, sans-serif","font-size":"11px"}).appendTo(b("body:first",o.document)),k=j.width(),l=k>i?i:k;if(j.remove(),d.find(".sf-"+a+"-content").html(f),"howl"===a){var n=e.attr("data-title")?e.attr("data-title"):""===m.title?null:m.title,p=d.find(".sf-"+a+"-heading");n?p.length<=0?b('<div class="sf-'+a+'-heading">'+n+"</div>").insertBefore(d.find(".sf-"+a+"-content")):p.text(n):p.length>0&&p.remove()}var q=d.width();$sf.util.ua.ie&&$sf.util.ua.major<=6&&"maxWidth"in o.document.body.style?d.css({width:q}):d.css(q>i?{width:i,"max-width":i}:l>q?{width:l,"max-width":l}:{width:q})},positionIt:function(a,b,c){function d(c){var d=a.container.find(".sf-"+b+"-arrow-wrap");d.css({left:a.container.width()/2-c+d.width()/2+"px"})}if(a&&a.container&&a.container.length){var e="undefined"!=typeof m.target?m.target:a.el;c="bottom"!==c?"top":"bottom";var f=$sf.util.position.get({ideal:(c.indexOf("top")>-1?"l":"u")+"c",container:a.container,anchor:e,path:"axis",anchorOffset:p.isSingle()?{t:0,l:0}:p.ext.iframe().offset(),containerOffset:{width:function(){return 0},height:function(){return 2}},slideAgainstAnchor:!0,context:p.ext.window()});a.container.css(f.coords),"u"==f.y?a.container.addClass("sf-"+b+"-below"):a.container.removeClass("sf-"+b+"-below"),d(f.coords.leftAdj,f.coords)}}}},r={initTrigger:function(a){a.hasClass("sf-tipper-target")||(a.addClass("sf-tipper-target").data("sf-tipper-style",m.style),$sf.util.event.on("dragstart.sfDialog",l),$sf.util.event.on("resize.sfDialog",i)),m.groupId&&a.data("sf-tipper-group",m.groupId),(a.is("a")&&"#"==a.attr("href")||a.is("label")||a.parents("label").length>0)&&a.bind("click.sfTipper",function(a){return b(a.target).is(".sf_tipper,.sf-tip")?!1:void 0});var d=m.initOnly;return 1==d&&(m.initOnly=!1),a.data("sf-tipper")||(a.data("sf-tipper",parseInt(1e6*Math.random())),"tooltip"!=m.style&&"howl"!=m.style&&"tourpoint"!=m.style||(1==m.attachIcon&&(a.bind("click.sfTipper dblclick.sfTipper",function(c){return b(":sf-tipper(target="+a.data("sf-tipper")+",open)").length<=0?a.data("inClick")?!1:(a.data("inClick",!0),a.sfTipper(m),setTimeout(function(){a.removeData("inClick")},1e3),void 0):!1}),"tooltip"==m.style&&a.addClass("sf-tip-ico")),m.autoOpen))?(c.init=!0,1==d?!1:!0):!1},execOpen:function(){return 1==b.isFunction(m.onOpen)&&m.onOpen(g)===!1?!1:!0},requestToReposition:function(){var a=m.reposition;return delete c.settings.reposition,1==a&&c.init?(onResize(),!1):void 0},requestToClose:function(a){var d=m.closeNow===!0,e=m.close||d;delete c.settings.close,delete c.settings.closeNow;var h=f(),i=b(".sf-"+h+":first",o.document);return i.length>0?i.data("sf-tipper")==g.data("sf-tipper")?1==e?(outsideMaster=i,r.fadeTipper(d,i,!0),!1):(c.title&&c.title.html(m.title),c.content&&k(m.content,c.content),q[a].positionIt(q[a].getElements(i)),!1):1==e?!1:(n.setTimeout(function(){g.sfTipper(m)},100),!1):1==e?!1:!0},requestToCloseTippers:function(a){var d=m.closeTippers;if("undefined"!=typeof d&&(d=b.extend({excludeMe:!1},d)),delete c.settings.closeTippers,"undefined"!=typeof d){var e=d.excludeMe===!0,f=":sf-tipper("+(d.style&&"not mine"!==d.style?"style="+("mine"===d.style?a:d.style)+",":"")+"target,open)";for(var g in b.fn.sfTipper.instances)if(b.fn.sfTipper.instances.hasOwnProperty(g)){var h=b.fn.sfTipper.instances[g],i="qt"===h.me.attr("data-style")?f.replace(",open",""):f;h.me.is(i)&&("not mine"===d.style&&h.me.is(":sf-tipper(target,style="+a+")")||e&&$sf.util.jCompat.isEl(h.me,c.me)||h.me.sfTipper({close:!0}))}return!1}return!0},requestToSuppressResumeDestroyQuickTips:function(a){var d="undefined"!=typeof m.suppressTippers?"suppress":"undefined"!=typeof m.resumeTippers?"resume":"undefined"!=typeof m.destroyTippers?"destroy":null;if(null===d)return!0;var e=b.extend({excludeMe:!1},m[d+"Tippers"]);if(delete c.settings[d+"Tippers"],"undefined"!=typeof e){var f=e.excludeMe===!0,g=":sf-tipper(style=qt,target)";for(var h in b.fn.sfTipper.instances)if(b.fn.sfTipper.instances.hasOwnProperty(h)){var i=b.fn.sfTipper.instances[h];i.me.each(function(){var a=b(this);if(a.is(g)&&(!f||!$sf.util.jCompat.isEl(a,c.me))){var e={};e[d]=!0,a.sfTipper(e)}})}return!1}return!0},fadeTipper:function(a,d,e){if(d=d?d:c.outside,!c.fading&&d&&d.length){c.fading=!0;var f=b.fn.jquery.split("."),h=(parseInt(f[0]),parseInt(f[1])),i=f.length>2?parseInt(f[2]):0;3>=h||4===h&&3>i?(b("body",n.document).die("click",d.data("OnClose")),b("body",o.document).die("click",d.data("OnClose"))):7>h?(b(n.document).undelegate("body","click",d.data("OnClose")),b(o.document).undelegate("body","click",d.data("OnClose"))):(b(n.document).off("click","body",d.data("OnClose")),b(o.document).off("click","body",d.data("OnClose"))),g.parentsUntil("body").unbind("scroll",d.data("OnScroll")),b(o).unbind("scroll",d.data("OnResize")),b(o).unbind("resize",d.data("OnResize")),p.isSingle()||(b(n).unbind("resize",d.data("OnResize")),b(n).unbind("scroll",d.data("OnResize")));var j=function(){c.fading=!1,d.remove()};a===!0||m.disableFade===!0?j():m.fadeToAnchor&&!e?d.animate({top:m.fadeToAnchor.offset().top,left:m.fadeToAnchor.offset().left,opacity:0},{duration:500,queue:!1,complete:j}):d.fadeOut(300,j)}},display:function(){return b.isFunction(m.onDisplay)===!0&&m.onDisplay(g,q[m.style].getElements(g).container)===!1?!1:!0}};switch(m.style){case"qt":if(!r.requestToCloseTippers(m.style))return g;if(!r.requestToSuppressResumeDestroyQuickTips(m.style))return g;var s=m.resume;m.resume&&(m.suppress=!1,m.resume=!1);var t=q.bubble.buildContainer("qt");b.each(g,function(){var a=b(this);r.initTrigger(a),s===!0&&a.removeAttr("data-suppressed"),m.close||m.suppress||m.destroy?(a.trigger("mouseout"),(m.suppress||m.destroy)&&(a.unbind("mouseover.sfTipper mouseout.sfTipper"),$sf.util.event.off(l),$sf.util.event.off(i)),m.suppress&&a.attr("data-suppressed",!0),m.destroy?(1===b(":sf-tipper(target,style=qt)").length?t.remove():t.hide(),a.removeClass("sf-tipper-target").removeData("sf-tipper"),delete b.fn.sfTipper.instances[h]):t.hide()):a.bind("mouseover.sfTipper",function(){m.suppress||(q.qt.load(t,a),q.qt.positionIt({el:a,container:t}),t.show())}).bind("mouseout.sfTipper",function(){t.hide()})});break;case"tooltip":case"howl":case"tourpoint":default:var u;if(u=r.requestToCloseTippers(m.style),u&&(u=r.requestToSuppressResumeDestroyQuickTips(m.style)),!u)return g;if((c.init||m.autoOpen)&&(u=r.requestToClose(m.style),!u))return g;if(u=r.initTrigger(g),!u)return g;if(u=r.execOpen(),!u)return g;var v=q[m.style].build(g);q[m.style].positionIt(v),b(o).bind("resize",i),b(o).bind("scroll",i),g.parentsUntil("body").bind("scroll",j),b(n).bind("resize",i),b(n).bind("scroll",i),r.display(),m.disableClose===!1&&n.setTimeout(function(){var a=b.fn.jquery.split("."),c=parseInt(a[1]),d=a.length>2?parseInt(a[2]):0;3>=c||4===c&&3>d?(b("body",n.document).live("click",l),b("body",o.document).live("click",l)):7>c?(b(n.document).delegate("body","click",l),b(o.document).delegate("body","click",l)):(b(n.document).on("click","body",l),b(o.document).on("click","body",l))},100),m.resize===!0||m.disableClose===!0?(delete m.resize,v.container.show()):v.container.hide().fadeIn(300)}}},i.fn(i,d,e))},b.fn.sfTipper.instances={},b.extend(b.expr[":"],{"sf-tipper":function(a,c,d){var e=b(a),f=d[3];if(!f||""==f)return e.is(".sf-tipper, .sf-howl, .sf-tourpoint, .sf-qt");var g=f.split(","),h=!1,i=!1,j=b.grep(g,function(a){return"target"!=a});if(j.length<g.length?(g=j,h=e.is(".sf-tipper-target"),i=!0):h=e.is(".sf-tipper, .sf-howl, .sf-tourpoint, .sf-qt"),h){h=0==g.length;for(var c=0;c<g.length;c++){var k=g[c];if(i)switch(k){case"open":h=b(":sf-tipper(target="+e.data("sf-tipper")+"):visible").length>0;break;case"closed":h=0==b(":sf-tipper(target="+e.data("sf-tipper")+"):visible").length;break;default:var l=k.split("=");if(l.length>1)switch(l[0]){case"style":var m=e.data("sf-tipper-style");h=m===l[1];break;case"group":var n=e.data("sf-tipper-group");h=n===l[1]}else h=!1}else switch(k){case"open":h=e.is(":visible");break;case"closed":h=!e.is(":visible");break;default:var l=k.split("=");if(l.length>1){var j=l[1];if(j&&j.length>0)switch(l[0]){case"style":var m=e.attr("data-style");h=m===j;break;case"group":var n=e.attr("data-group");h=n===j;break;case"target":var o=e.data("sf-tipper");o=o?o.toString():"nope",h=o.toString()===j}}else h=!1}if(0==h)break}}return h}}),b.fn.sfTipper}($sf.getjQuery())}),define("starfield/sf.base.dialog",["jq!starfield/jquery.mod"],function(){$sf.base.dialog=function(){},$sf.base.dialog.prototype.supr=$sf.base.dialog.prototype,$sf.base.dialog.prototype.execSuper=function(a){var b=[];if(arguments.length>1)for(var c=1;c<arguments.length;c++)b.push(arguments[c]);return this.supr[a].apply(this,b)},$sf.base.dialog.prototype.init=function(a){this.me=a.element,this.context=a.context,this.body=$("body:first",this.context.document),this.layer=$sf.util.zIndex.getLayer(a.layerType?a.layerType:"dialog",this.body,a.layerAnchor),this.dialog=a.dialog,this.dialog.parent()!==this.layer&&this.layer.append(this.dialog),this.content=a.content,this.inner=a.inner,this.zIndex=a.zIndex,this.fixedPosition=a.fixedPosition===!0,this.fixedHeight=a.fixedHeight===!0,this.fixedWidth=a.fixedWidth===!0;var b=this.me.attr("id")?this.me.attr("id"):"dialog",c=$sf.util.logger(b,"duel");this.log=c.log,this.dir=c.dir,this.onWindowChange=$.isFunction(a.onWindowChange)?a.onWindowChange:function(){return!0},this.initDimensions()},$sf.base.dialog.prototype.initDimensions=function(){var a=this;a.widthIsExplicit=!1,a.heightIsExplicit=!1;var b=a.me.attr("style"),c="string"!=typeof b?[]:b.toLowerCase().split(";");$(c).each(function(b,c){var d=c.split(":");d.length>1&&"width"===$.trim(d[0])&&"auto"!=$.trim(d[1]).toLowerCase()&&(a.widthIsExplicit=!0),d.length>1&&"height"===$.trim(d[0])&&"auto"!=$.trim(d[1]).toLowerCase()&&(a.heightIsExplicit=!0)}),b=a.me.attr("width"),"string"==typeof b&&b.length>1&&(a.widthIsExplicit=!0),b=a.me.attr("height"),"string"==typeof b&&b.length>1&&(a.heightIsExplicit=!0)},$sf.base.dialog.prototype.getViewPort=function(){var a=this,b=$(a.context.window),c={top:0,left:0,width:b.width(),height:b.height()};return $sf.util.ua.ie6&&(c.width-=4),"number"==typeof a.context.window.pageYOffset?(c.top=a.context.window.pageYOffset,c.left=a.context.window.pageXOffset):a.context.document.body&&a.context.document.body.scrollTop?(c.top=a.context.document.body.scrollTop,c.left=a.context.document.body.scrollLeft):a.context.document.documentElement&&a.context.document.documentElement.scrollTop&&(c.top=a.context.document.documentElement.scrollTop,c.left=a.context.document.documentElement.scrollLeft),c.bottom=c.top+c.height,c.right=c.left+c.width,c},$sf.base.dialog.prototype.getDeltas=function(a,b,c,d,e){var f=this,g={x:0,y:0,padX:0,padY:0,marginX:d.x,marginY:d.y,idealWidth:a,widthMin:b,heightMin:c};return g.padX=f.dialog.outerWidth(!0)-f.dialog.width(),g.x=f.dialog.outerWidth(!0)-f.dialog.width()+(f.content.outerWidth(!0)-f.content.width())-g.padX,g.padY=f.dialog.outerHeight(!0)-f.dialog.height(),g.y=f.dialog.outerHeight(!0)-f.dialog.height()+(f.content.outerHeight(!0)-f.content.height())-g.padY,g.extraHeight=$.isFunction(e)?e():0,g},$sf.base.dialog.prototype.setZIndex=function(a){var b=isNaN(this.zIndex)?$sf.util.zIndex.maxForLayer()+4:this.zIndex;return("undefined"==typeof this.zIndex||this.zIndex<b)&&(this.zIndex=b),this.dialog.css({"z-index":this.zIndex}),$.isFunction(a)&&a(this.zIndex),this.zIndex},$sf.base.dialog.prototype.sizer=function(a){function b(){var a=k.dialog.is(":visible");a||k.dialog.show();var b={width:0,height:0};return b=c(b,m,l),k.inner.css({width:$sf.util.ua.ie6?b.innerWidth+"px":""}),k.dialog.width(b.width),b=d(b,m,l),a||k.dialog.hide(),k.dimensions=b,b}function c(a){k.widthIsExplicit===!1?1==k.me.is("iframe")?a.width=m.width:(a.width=l.idealWidth-l.x,$.isFunction(j)&&(a.width=j(a.width))):a.width=k.me.outerWidth()+1,a.width+=l.x;var b=m.width-2*l.marginX;return!k.fixedWidth&&a.width>b&&(b>=l.idealWidth?a.width=b:m.width>l.idealWidth&&a.width>l.idealWidth&&(a.width=l.idealWidth)),k.widthIsExplicit===!0&&a.width<l.idealWidth&&a.width+l.x>=l.idealWidth&&(a.width=l.idealWidth),a.width>m.width&&(a.width=m.width),l.widthMin&&a.width<l.widthMin+l.x&&(a.width=l.widthMin+l.x),a.innerWidth=a.width-l.x,a.outerWidth=a.width+l.padX,a}function d(a){return a.height=k.me.height()+1,0==k.me.is("iframe")&&(a.height=k.me.outerHeight()+1),a.height+=l.extraHeight+l.y,a.outerHeight=a.height+l.padY,!k.fixedHeight&&a.outerHeight+2*l.marginY>m.height&&(a.height=m.height-l.padY-2*l.marginY,a.outerHeight=a.height+l.padY),a.height<l.heightMin&&(a.height=l.heightMin),a.innerHeight=a.height-l.y-l.extraHeight,a}var e=a.idealWidth,f=a.widthMin,g=a.heightMin,h=a.margin,i=a.extraHeight,j=a.widthAdjustment,k=this,l=k.getDeltas(e,f,g,h,i),m=k.getViewPort();return{get:b,deltas:l,viewPort:m}},$sf.base.dialog.prototype.positioner=function(a){function b(b){var d=c.sizer(a),e=d.get(),f=d.viewPort,g=$.extend({left:parseInt(f.width/2-e.width/2),top:parseInt(f.height/2-e.height/2)},b);return{dimensions:e,position:g,page:f,deltas:d.deltas}}var c=this;return{get:b}},$sf.base.dialog.prototype.open=function(){$(this.context.window).bind("resize scroll",this.onWindowChange);
},$sf.base.dialog.prototype.close=function(a){$(this.context.window).unbind("resize scroll",this.onWindowChange)},$sf.base.dialog.prototype.destroy=function(){this.close(!0)}}),define("starfield/sf.base.modal",["jq!starfield/jquery.mod","starfield/sf.base.dialog"],function(){$sf.base.modal=function(){},$sf.base.modal.prototype=new $sf.base.dialog,$sf.base.modal.prototype.constructor=$sf.base.modal,$sf.base.modal.constructor=$sf.base.dialog,$sf.base.modal.prototype.init=function(a){var b=this;this.position=null,this.positionTimer=null,a.onWindowChange=function(){if(b.position&&b.position.canReposition()){var c={disableAnimations:!0};$.isFunction(a.beforeResponseToWindowResize)&&(c=$.extend(!0,c,a.beforeResponseToWindowResize())),b.position.reposition(c)}},a.fixedPosition="boolean"!=typeof a.fixedPosition?!$sf.util.ua.ie6:a.fixedPosition,this.execSuper("init",a),this.overlay=$.isPlainObject(a.overlay)?this.overlayer(a.overlay.enable!==!1?"enable":"disable",a.overlay.skin,a.overlay.useAlt,a.overlay.clickToClose):null},$sf.base.modal.prototype.positioner=function(a){function b(a,b){b||(h=a),g.positionTimer=null,$.isFunction(a.onBeforePosition)&&a.onBeforePosition();var e=$.extend(!0,{disableAnimations:!1,duration:300,usePositioning:!0},a);g.inner.stop(!0,!0),g.dialog.stop(!0,!0),g.setZIndex(e.onAfterZIndex);var f=d(e.location);f.disableAnimations=e.disableAnimations,f.positionIsExplicit=$.isPlainObject(e.location)&&e.location.hasOwnProperty("top")&&e.location.hasOwnProperty("left"),$sf.util.ua.ie6&&(f.disableAnimations=!0,f.position.top+=f.page.top),$.isFunction(e.specialSizing)&&(f=e.specialSizing(f)),"undefined"!=typeof f.disableAnimations&&f.disableAnimations===!0&&(g.inner.stop().css({height:f.dimensions.innerHeight+20+"px"}),e.duration=0),g.inner.stop().css("overflow","hidden").animate({height:f.dimensions.innerHeight+"px"},e.duration),g.dialog.css("position",g.fixedPosition?"fixed":"absolute");var i={width:f.dimensions.width+"px",height:f.dimensions.height+"px"};(e.usePositioning===!0||f.positionIsExplicit===!0)&&(i.left=f.position.left+"px",i.top=f.position.top+"px"),g.dialog.stop().css("overflow","").animate(i,e.duration,function(){c(e.usePositioning,e.afterPositioning,e.onAfterPosition)}),g.positionInfo=f}function c(a,b,c){var d=g.inner.outerWidth(),e=g.inner.outerHeight(),f=g.me.outerWidth(),h=g.me.outerHeight()+g.me.position().top,i=g.dialog.position(),j=g.inner[0].scrollHeight;if(d>=f&&e>=h){if($.isFunction(b)){var k={};k=b(),k.top&&g.dialog.css(k)}return void($.isFunction(c)&&c())}if(g.inner.css("overflow",""),j>e&&!g.fixedHeight){g.inner.css("width",d+20+"px"),a&&g.dialog.css({left:i.left-10+"px"});var l=g.dialog.width();g.dialog.css({width:l+20+"px"})}var m=g.inner[0].scrollWidth,n={};if($.isFunction(b))n=b();else if(m>d&&!g.fixedWidth){g.inner.css("height",e+20+"px");var o=g.dialog.height()+20,p=$(g.context.window).height(),q=g.fixedPosition?(p-o)/2:i.top-10;a&&(n.top=q+"px"),n.height=o+"px"}(n.top||!a&&n.height)&&g.dialog.css(n),$.isFunction(c)&&c()}function d(a){return i.get(a)}function e(a){g.initDimensions();var c=$.extend({async:!1,disableAnimations:!0,usePositioning:!0,callback:null},a),d=$.extend(!0,{},h,{disableAnimations:c.disableAnimations,usePositioning:c.usePositioning,location:$.isPlainObject(c.location)?c.location:h.location,onAfterPosition:function(){$.isFunction(c.callback)&&c.callback(),$.isFunction(h.onAfterPosition)&&h.onAfterPosition()}});if(1==c.async){if(null!=g.positionTimer)return;return void(g.positionTimer=setTimeout(function(){b(d,!0)},50))}b(d,!0)}function f(){return null!==h}var g=this,h=null,i=g.execSuper("positioner",a);return g.position={set:b,get:d,reposition:e,canReposition:f},g.position},$sf.base.modal.prototype.overlayer=function(a,b,c,d){function e(){if(l&&($sf.util.ua.ie6||"fos"===b)){var a=h.getViewPort();k.css({top:a.top+"px",left:a.left+"px",width:a.width+"px",height:a.height+"px"})}}function f(){if(l){if(!preventOverflowChanges){var a=$sf.util.ua.ie7||$sf.util.ua.ie6?i.parent("html"):i;m.overflow=a.css("overflow"),m["overflow-x"]=a.css("overflow-x"),m["overflow-y"]=a.css("overflow-y"),a.css({overflow:"hidden","overflow-x":"hidden","overflow-y":"hidden"}),i.parent("html").css({height:"100%"})}k=$("#"+dId+"-overlay"),0===k.length&&(k=$("<div />").attr("id",dId+"-overlay").addClass(classes).prependTo(j)),"fos"!==b&&c&&k.addClass("sf-dialog-overlay-alt"),k.css({display:"block","z-index":h.zIndex-1,position:$sf.util.ua.ie6||"fos"===b?"absolute":"fixed"}),e(),$.isFunction(d)&&k.bind("click",d),k.fadeIn(500,function(){k.css({display:"block"})})}}function g(a){if(k&&l){var b=a?0:500;if(k.fadeOut(b,function(){k.remove()}),!preventOverflowChanges){i.parent("html").css({height:""});for(var c in m)if(m.hasOwnProperty(c)){var e=$sf.util.ua.ie7||$sf.util.ua.ie6?i.parent("html"):i;null!=m[c]&&void 0!=m[c]?e.css(c,m[c]):e.css(c,"visible")}}$.isFunction(d)&&k.unbind("click")}}var h=this,i=h.body,j=h.layer,k=null,l=a!==!1;dId="sf-dialog-overlay-"+Math.round(65535*Math.random()),b="fos"==typeof b?"fos":"app",classes="fos"===b?"sf-g-modal-overlay":"sf-dialog-overlay",c=c===!0,d=d,preventOverflowChanges=i.find("embed[flashvars]").length>0&&$sf.util.ua.ff,h.overlaySettings={};var m=h.overlaySettings.overflow={};return{show:f,hide:g,size:e,enable:function(){l=!0},disable:function(){l=!1}}},$sf.base.modal.prototype.open=function(a){$.isPlainObject(a)&&this.position&&this.position.canReposition()&&this.position.reposition(a),this.overlay&&this.overlay.show(),this.execSuper("open")},$sf.base.modal.prototype.close=function(a){this.overlay&&this.overlay.hide(a),this.execSuper("close",a)}}),define("starfield/sf.base.flyout",["jq!starfield/jquery.mod","starfield/sf.base.dialog"],function(){$sf.base.flyout=function(){},$sf.base.flyout.prototype=new $sf.base.dialog,$sf.base.flyout.prototype.constructor=$sf.base.flyout,$sf.base.flyout.constructor=$sf.base.dialog,$sf.base.flyout.prototype.init=function(a){var b=this;this.position=null,this.positionTimer=null,a.onWindowChange=function(){if(b.position&&b.position.canReposition()){var c={disableAnimations:!0};$.isFunction(a.beforeResponseToWindowResize)&&(c=$.extend(!0,c,a.beforeResponseToWindowResize())),b.position.reposition(c)}},a.layerType="flyout",this.execSuper("init",a),this.dialogZIndex=a.zIndex,this.anchor=this.anchorer(a.anchor)},$sf.base.flyout.prototype.anchorer=function(a){var b=this,c={origin:a},d=b.dialog.attr("id");return c.origin.addClass("sf-flyout-origin").attr("data-flyout",d),b.anchor=c,b.setZIndex({dialogZIndex:b.dialogZIndex,resetAnchor:!0}),c},$sf.base.flyout.prototype.setZIndex=function(a){var b=this;a=a?a:{};var c=a.dialogZIndex&&"number"==typeof a.dialogZIndex&&a.dialogZIndex>0?a.dialogZIndex:$sf.util.zIndex.max()+10;if(b.dialogZIndex=c,b.dialog.css({"z-index":c}),$.isFunction(a.callback)&&a.callback(c),a.resetAnchor===!0){var d=b.anchor.origin;"static"==d.css("position")&&d.css("position","relative"),b.zIndex=$sf.util.zIndex.get(d),b.anchor.originalZIndex=b.zIndex,0==b.zIndex&&(b.zIndex=3),d.css("zIndex",b.zIndex)}},$sf.base.flyout.prototype.positioner=function(a){function b(a,b){b||(f=a),$.isFunction(a.onBeforePosition)&&a.onBeforePosition();var c=g.get(),d=e.dialog,h=e.anchor.origin,i={top:0,left:0},j=$.extend({width:0,height:0},a.containerOffset);e.setZIndex({dialogZIndex:e.dialogZIndex,callback:a.onAfterZIndex}),d.get(0).ownerDocument!==h.get(0).ownerDocument&&($("iframe").each(function(){$sf.util.jCompat.findEl($(this).contents(),h).length>0&&(iframe=$(this))}),iframe&&iframe.length>0&&(i=iframe.offset()));var k=$sf.util.position.get({anchor:h,ideal:a.anchorAttachment,container:d,viewPort:c.page,anchorOffset:i,containerOffset:{width:function(a){return j.width},height:function(a){return j.height}},sticky:a.sticky,centerAgainstAnchor:a.centerAgainstAnchor,slideAgainstAnchor:a.slideAgainstAnchor,fallbackOnFailPosition:a.fallbackOnFailPosition}),l=k.y+k.x;e.dialog.css({top:k.coords.top+"px",left:k.coords.left+"px"}).attr({"data-idealAttachment":a.anchorAttachment,"data-actualAttachment":l,"data-positionLeftAdj":k.coords.leftAdj?k.coords.leftAdj:0,"data-positionTopAdj":k.coords.topAdj?k.coords.topAdj:0}),$.isFunction(a.onAfterPosition)&&a.onAfterPosition(),e.positionInfo=c}function c(a){e.initDimensions();var c=$.extend({},f,{anchorAttachment:a.anchorAttachment,onAfterPosition:function(){$.isFunction(a.callback)&&a.callback(),$.isFunction(f.onAfterPosition)&&f.onAfterPosition()}});b(c,!0)}function d(){return null!==f}var e=this,f=null,g=e.execSuper("positioner",a);return e.position={set:b,get:$sf.util.position.get,reposition:c,canReposition:d},e.position},$sf.base.flyout.prototype.open=function(a){$.isPlainObject(a)&&this.position&&this.position.canReposition()&&this.position.reposition(a),this.overlay&&this.overlay.show(),this.execSuper("open")},$sf.base.flyout.prototype.close=function(a){this.execSuper("close",a)}});var $sfDialog=null;define("starfield/sf.dialog",["i18n!starfield/sf.i18n/duel","starfield/sf.base.modal","starfield/sf.base.flyout","css!starfield/sf.core.css","jq!starfield/jquery.mod","starfield/sf.tabs","starfield/sf.util"],function(a){var b=a.common;return function(c){return c.fn.sfDialog=function(d,e){var f=c(this),g=f.attr("id");(null==g||0==g.length)&&(g="sfDialog"+Math.round(65535*Math.random()),f.attr("id",g));var h=c.fn.sfDialog.instances[g];return null!=h?(h.me_this=this,h.me=f,h.fn(h,d,e)):(h=c.fn.sfDialog.instances[g]={me_this:this,me:c(this),initialized:!1,isEnabled:!0,fn:function(d,e,g){function h(e,g){if(d.meId=d.me.attr("id"),g!==!0&&"undefined"!=typeof e&&"undefined"!=typeof e.restore){var h=$(e);if(h)return}if("undefined"==typeof d.settings){var i={autoCloseOnCancel:!0,autoOpen:!0,autoResize:!1,buttons:void 0,buttonAlign:"center",cancelDisabled:!1,closeOnEscape:!0,context:self,destroyOnClose:null,dialogWidthIdeal:500,veilWidthIdeal:450,dialogHeightMin:200,disabled:!1,draggable:!0,load:null,loadFresh:!0,modal:!0,mode:"classic",message:null,noConflict:!1,overlayAlt:!1,page:null,preload:null,restore:!1,save:!1,title:a["sf.dialog"].title,titleHidden:!1,veil:null,zIndex:void 0,onButtonClick:null,onOpen:null,onClose:null,onCancel:null,onTabSelected:null,onLoad:null,onRestore:null,onWizardFinish:null,onDrag:null,onPositioned:null,footer:"",anchor:void 0,anchorAttachment:"tl",sticky:!1,centerAgainstAnchor:!1,slideAgainstAnchor:!1,dialogOffset:void 0,fallbackOnFailPosition:"auto",texts:a["sf.dialog"]};d.settings=c.extend(!0,i,e),d.msgOverlay=null,d.wizardButtons=!1,d.hideButtons=!1}else d.mode!==d.settings.mode&&(d.settings.buttons=void 0),"undefined"!=typeof e&&"undefined"!=typeof e.buttons&&(d.settings.buttons=[]),d.settings=c.extend(!0,d.settings,e);d.meIsFrame=d.me.is("iframe")===!0;var l="undefined"!=typeof d.mode&&d.mode!==d.settings.mode;d.mode=d.settings.mode,l?(d.wizardButtons="wizard"===d.mode,d.wizardButtons||(d.wizardInit=!1),d.settings.buttons="undefined"!=typeof e&&"undefined"!=typeof e.buttons?e.buttons:void 0,d.settings.buttonAlign="undefined"!=typeof e&&"undefined"!=typeof e.buttonAlign?e.buttonAlign:"center"):"wizard"===d.mode&&(d.wizardButtons="wizard"===d.mode,d.settings.buttons&&!d.wizardInit&&(d.wizardCustomButtons=c.merge([],d.settings.buttons),d.settings.buttons=void 0)),"undefined"==typeof d.settings.buttons?d.settings.buttons=[{id:"ok",text:b.ok},{id:"cancel",text:b.cancel,cancel:!0}]:d.settings.hideButtons=d.settings.buttons.constructor==Array&&0===d.settings.buttons.length,d.buttonDefaults={enabledClasses:"sf-btn-primary",disabledClasses:"sf-btn-primary sf-btn-dsabld"},d.context=d.settings.context,d.usePositioning=!0,d.positionOverridden=!1,0==d.settings.noConflict&&($sfDialog=f),1==d.me.parent().is(".sf-dialog-inner")?(d.settings.veil&&(d.base.fixedPosition=!1),d.inner=d.me.parent(),d.content=d.inner.parent(),d.buttons=d.content.children(".sf-dialog-buttons"),d.dialog=d.content.parent(".sf-dialog"),d.title=d.dialog.children(".sf-dialog-title"),d.close=d.dialog.children(".sf-dialog-close")):(d.originalParent=d.me.parent(),d.hasParent=d.originalParent.length>0,null===d.settings.destroyOnClose&&(d.settings.destroyOnClose=d.hasParent===!1),d.dialog=c("<div/>").addClass("sf-dialog"),d.title=c("<h3/>").addClass("sf-dialog-title"),d.close=c("<span/>").addClass("sf-dialog-close sf-icn-close1 sf-icn"),d.content=c("<div/>").addClass("sf-dialog-content"),d.inner=c("<div/>").addClass("sf-dialog-inner"),d.buttons=c("<div/>").addClass("sf-dialog-buttons"),d.footer=null,d.tabs=null,d.tabLastSelected=null,d.tabsHeight=0,d.content.append(d.inner),d.content.append(d.buttons),d.dialog.append(d.close),d.dialog.append(d.title),d.dialog.append(d.content),d.meBody=c("body:first",d.context.document),d.meBody.append(d.dialog),d.preventOverflowChanges=d.meBody.find("embed[flashvars]").length>0&&$sf.util.ua.ff,d.innerOverflow="",k()),"modal"===d.baseType&&(d.settings.veil?(d.base.overlay.hide(!0),d.base.overlay.disable(),d.settings.titleHidden=!0):d.settings.modal===!0?d.base.overlay.enable():(d.base.overlay.hide(!0),d.base.overlay.disable())),1==d.settings.titleHidden?d.title.hide():d.title.show(),1==d.settings.cancelDisabled||d.settings.veil?d.close.hide():d.close.show(),d.settings.veil?d.dialog.addClass("sf-dialog-veil"):(d.dialog.removeClass("sf-dialog-veil"),1==d.settings.modal?d.dialog.addClass("sf-dialog-modal"):d.dialog.addClass("sf-dialog-modeless")),0==d.initialized&&(d.draggable=!1,d.isOpen=!1,d.overlay=null),o(),C(),q(),G(),d.me.removeClass("sf-dialog"),d.meIsFrame&&d.me.attr({scrolling:"no",frameborder:"0"}).css({overflow:"visible"}),c.inArray(d.me[0],d.inner.children())<0&&d.inner.empty().append(d.me),d.me.show(),d.initialized=!0,0==P()&&(c(d.context.window).bind("unload",na),c(d.context.document).bind("keydown",L)),g!==!0&&1==d.settings.autoOpen&&N(d.settings),g!==!0&&null!=d.settings.save&&Z(e),g!==!0&&null!=d.settings.load&&Y(e),"string"==typeof d.settings.message?(d.msgOverlay||S(),require("starfield/sf.msg.overlay",function(){d.msgOverlay=d.dialog.sfMsgOverlay({style:"progress",message:d.settings.message})})):d.msgOverlay&&(R(),d.msgOverlay.sfMsgOverlay({message:null}),d.msgOverlay=null),j()}function j(){if(d.meIsFrame)try{var a=E(d.me.contents().find("html")[0].ownerDocument);"undefined"!=typeof a&&a.duelDialogInit&&a.duelDialogInit(d.me)}catch(b){return}}function k(){var a=d.settings.zIndex;"undefined"==typeof a&&(a=1),a+=d.settings.veil?4:d.settings.modal===!0?2:0,"undefined"==typeof d.settings.anchor||d.settings.veil?(d.baseType="modal",d.base=new $sf.base.modal,d.base.init({element:d.me,context:d.context,dialog:d.dialog,content:d.content,inner:d.inner,zIndex:a,overlay:m(),layerAnchor:d.settings.veil?d.settings.veil:void 0,fixedPosition:l(),beforeResponseToWindowResize:function(){return d.usePositioning=!d.positionOverridden,{usePositioning:d.usePositioning}}})):(d.baseType="flyout",d.base=new $sf.base.flyout,d.base.init({element:d.me,anchor:d.settings.anchor,context:d.context,dialog:d.dialog,content:d.content,inner:d.inner,zIndex:a,sticky:d.settings.sticky,centerAgainstAnchor:d.settings.centerAgainstAnchor,slideAgainstAnchor:d.settings.slideAgainstAnchor,fallbackOnFailPosition:d.settings.fallbackOnFailPosition,beforeResponseToWindowResize:function(){return{anchorAttachment:d.settings.anchorAttachment}}}))}function l(){return!(d.settings.veil||/msie|MSIE 6/.test(navigator.userAgent)||d.positionOverridden)}function m(){return{skin:$sf.skin,useAlt:d.settings.overlayAlt,enable:d.settings.modal===!0&&!d.settings.veil}}function n(a){d.settings.anchorAttachment=a,K()}function o(a){d.settings.draggable===!1&&d.draggable===!0?(d.title.draggable("destroy"),d.dialog.unbind("drag").unbind("dragstart").unbind("dragend").unbind("ondragstart").unbind("ondragend").draggable("destroy"),d.title.css("cursor","default"),d.draggable=!1,d.usePositioning=!d.positionOverridden):d.settings.draggable===!0&&"undefined"!=typeof d.me.draggable&&(d.draggable=!0,d.dialog.draggable({handle:d.title}),d.title.css("cursor","move"),d.settings.veil&&d.settings.veil.sfDialog("setOnDrag",ga),oa(function(){d.usePositioning=!1,c.isFunction(d.settings.onDrag)&&d.settings.onDrag(d.me,d.dialog)},"sfd"))}function p(a){d.settings.draggable===!0&&("undefined"!=typeof d.me.draggable&&d.dialog.draggable("option","disabled",!a),d.title.css("cursor",a?"move":"default"))}function q(a){var e=d.settings.texts;if(c("*",d.buttons).unbind().remove(),d.settings.hideButtons)return void d.buttons.hide();d.buttonsIdx={},d.cancel=null;var f=d.settings.buttons;if(d.wizardButtons&&"undefined"!=typeof d.tabs_menu){var g=d.tabs_menu.find("ul:first");d.wizardInit=f&&f.length>1&&"back"==f[1].id;var h=g.children("li:first").is(".sf-tabs-wiz-step-current"),i=g.children("li:last").is(".sf-tabs-wiz-step-current");d.settings.buttons=f=[{id:"cancel",text:e.wizardCancel,cancel:!0},{id:"back",text:e.wizardBack,isDisabled:h||d.wizardInit&&f[1].isDisabled},{id:i?"finish":"next",text:i?e.wizardFinish:e.wizardNext,isDisabled:d.wizardInit&&f[2].isDisabled}];var j=d.settings.buttons;if(c.isArray(d.wizardCustomButtons)&&d.wizardCustomButtons.length>0)for(var k=0,l=j.length;l>k;k++){var m=c.grep(d.wizardCustomButtons,function(a){return a.id===j[k].id});m.length>0&&m[0].attributes&&(j[k].attributes=m[0].attributes)}}"undefined"!=typeof a&&1==a&&(f=[]),0===f.length&&a!==!1&&f.push({id:"cancel",text:b.cancel,cancel:!0});for(var k=0;k<f.length;k++){var n=f[k];if("closebox"===n.type&&d.close){if(n.attributes)for(var o in n.attributes)n.attributes.hasOwnProperty(o)&&d.close.attr(o,n.attributes[o])}else{var n=c.extend(!0,{id:"",index:k,text:e.button,cancel:!1,style:"button",onClick:null,isDisabled:!1},f[k]);0==n.id.length&&(n.id="sfButton"+parseInt(65535*Math.random())),0==n.text.length&&(n.text=e.button),null==d.cancel&&k==f.length-1&&n.text==b.cancel&&(n.cancel=!0),1==n.cancel&&(n.style="link"),n.index=k,n.button=null;var p=1==Q()&&0==n.isDisabled?"enabledClasses":"disabledClasses";if("link"==n.style?(n.button=c('<a href="#" />').text(n.text),n.button.addClass(n[p]?n[p]:"fos"!==$sf.skin?"sf-dialog-link":"")):(n.button=c('<a href="#" />'),n.button.addClass(n[p]?n[p]:d.buttonDefaults[p]),n.button.text(n.text),f[k].hasIcon&&n.button.prepend("<span />")),n.button.attr("data-button",n.id),n.button.bind("click",M),n.attributes)for(var o in n.attributes)n.attributes.hasOwnProperty(o)&&n.button.attr(o,n.attributes[o]);d.buttons.append(n.button),"fos"===$sf.skin&&d.buttons.append("\n"),1==n.cancel&&(d.cancel=n.button),f[k]=n,d.buttonsIdx[n.id]=n}}0==Q()&&"fos"!=$sf.skin&&d.buttons.append('<div style="display: block; position: absolute;" class="sf-dialog-overlay sf-dialog-overlay-alt"></div>'),ea(d.settings.buttonAlign)}function r(a){var b=null;if("string"==typeof a)b=d.buttonsIdx[a];else{if(0>a||a>=d.settings.buttons.length)return b;b=d.settings.buttons[a]}return"undefined"==typeof b&&(b=null),b}function s(a){a.index<0||a.index>=d.settings.buttons.length||(d.settings.buttons[a.index]=a,q())}function t(){return d.settings.buttons}function u(a){if(1==c.isArray(a)){var b=d.settings.buttons.length;d.settings.buttons=a,d.settings.hideButtons=0===a.length,q(),(b>0&&0===a.length||a.length>0&&0===b)&&(d.settings.hideButtons||d.buttons.show(),K())}}function w(a){var b=r(a);return null==b?null:b.isDisabled}function x(a){var b=r(a);null!=b&&(b.isDisabled=!1,q())}function y(a){var b=r(a);null!=b&&(b.isDisabled=!0,q())}function z(a){return null==d.tabs?null:d.tabs.sfTabs("isdisabled",a)}function A(a){return null!=d.tabs?d.tabs.sfTabs("enable",a):void 0}function B(a){return null!=d.tabs?d.tabs.sfTabs("disable",a):void 0}function C(){d.close.unbind();var a=d.me.attr("data-title");d.title.html(null!=a&&a.length>0?a:d.settings.title),d.close.bind("click",O),D(),F()}function D(){var a=[];if(null!=d.tabs){for(var b=d.tabs.sfTabs("count"),e=0;b>e;e++)a[e]={disabled:z(e),hidden:!da(e)};d.tabs.sfTabs("destroy"),d.tabs=null,d.tabsHeight=0}var f;if(d.meIsFrame)try{f="wizard toggle tabs".indexOf(d.mode)>-1?d.me.contents().find(".sf-dialog-tab,.sf-tabs-content"):c("body > .sf-dialog-tab, body > .sf-tabs-content, body > div > .sf-dialog-tab, body > div > .sf-tabs-content",d.me.contents())}catch(g){return}else f=d.me.children(".sf-dialog-tab,.sf-tabs-content");if(0==f.length)return void d.dialog.removeClass("sf-dialog-tabs");f.css({padding:0});var e;for(e=0;e<f.length;e++){var h=f.eq(e);1==h.is(".sf-dialog-tab")&&h.removeClass("sf-dialog-tab").addClass("sf-tabs-content"),1==h.is(".sf-dialog-tab-selected")&&h.addClass(".sf-tabs-actv-content").removeClass(".sf-dialog-tab-selected"),1==h.is(".sf-dialog-tab-hidden")&&h.addClass(".sf-tabs-hidden").removeClass(".sf-dialog-tab-hidden")}var i=f.eq(0).parent();d.tabs=i.sfTabs({mode:d.mode,bborder:!0,tabControlContext:d.meIsFrame?E(d.me.contents().find("html")[0].ownerDocument):window.self,onTabSelected:function(a,b,e){if(1==c.isFunction(d.settings.onTabSelected)&&0==d.settings.onTabSelected(d.me,b,e))return!1;"wizard"==d.mode&&setTimeout(function(){q(),F()},0);var f=$sf.util.ua.ie&&$sf.util.ua.major<9,g=f?10:0;d.meIsFrame&&(d.me.width(e.outerWidth()+g),d.me.height(e.outerHeight()+g),f&&(d.me.attr("frameborder","0"),d.me.css({overflow:"hidden",border:"none"}))),"wizard"==d.tabsMode&&setTimeout(function(){q(),F()},0),K({async:!0,noanim:!1})}});var j=".sf-tabs:first,.sf-tabs-wiz:first,.sf-tabs-toggle:first";if(d.tabs_menu=i.children(j),0==d.tabs_menu.length&&(d.tabs_menu=d.me.parent().children(j)),d.content.before(d.tabs_menu),d.dialog.addClass("sf-dialog-tabs"),1==d.dialog.is(":visible")&&(d.tabsHeight=d.tabs_menu.outerHeight()),a.length>0)for(var e=0;e<a.length;e++)a[e].disabled&&B(e),a[e].hidden&&ba(e)}function E(a){return a.parentWindow||a.defaultView}function F(){if("wizard"==d.mode&&"undefined"!=typeof d.tabs_menu){var a=d.tabs_menu.find("ul:first"),b=a.children("li.sf-tabs-wiz-step-current").index(),e=d.tabs.sfTabs("visibleCount"),f=d.title.children("span.sf-dialog-wiz-title-steps");0==f.length&&(f=c('<span class="sf-dialog-wiz-title-steps"></span>').appendTo(d.title)),f.text("("+d.settings.texts.stepOf.sfFormat(b+1,e)+")")}}function G(){null!=d.settings.footer&&""!==d.settings.footer&&(d.footer=c("<div>"+d.settings.footer+"</div>").addClass("sf-dialog-footer"),d.content.append(d.footer))}function H(a,b){if(1!=a&&J(!0),0!=d.initialized){if(d.initialized=!1,d.meIsFrame){var c=function(){try{"undefined"!=typeof d.tabs&&null!=d.tabs&&(d.tabs.sfTabs("destroy"),d.tabs=null,d.tabsHeight=0)}catch(a){}};b?c():setTimeout(c,500)}if(d.oldButtons=d.settings.buttons,"classic"!==d.mode&&(d.settings.buttons=void 0),!a||d.settings.destroyOnClose!==!1){if("undefined"!=typeof d.dialog){var e=function(){d.hasParent===!0&&(d.me.hide(),d.originalParent.append(d.me)),d.base.destroy(),delete d.base,d.dialog.remove(),delete d.dialog};b?e():setTimeout(e,1e3)}d.contentShiftEventId&&($sf.util.event.off(d.contentShiftEventId),delete d.contentShiftEventId),delete d.settings}}}function I(){J(!0,null,null,!0)}function J(a,b,e,g){0!=P()&&(d.resizeTimer&&(clearInterval(d.resizeTimer),delete d.resizeTimer),0==d.settings.noConflict&&($sfDialog=f),"undefined"==typeof a&&(a=!1),"undefined"==typeof b&&(b=null),"undefined"==typeof e&&(e=!1),((null==b||1!=b.cancel)&&e!==!0||1!=c.isFunction(d.settings.onCancel)||0!=d.settings.onCancel(b,e,d.me)||0!=a)&&(1!=c.isFunction(d.settings.onClose)||0!=d.settings.onClose(b,e,d.me)||0!=a)&&(c.fn.sfDialog.opens--,c.fn.sfDialog.saves={},c.fn.sfDialog.savesArr=[],d.base.close(),d.dialog.unbind(),d.buttons&&c("*",d.buttons).unbind(),d.tabs&&d.me.find(".sf-tabs-menu *").unbind(),d.close&&c("*",d.close).unbind(),c(d.context.window).unbind("unload",na),c(d.context.document).unbind("keydown",L),1==d.draggable&&(d.dialog.draggable("destroy"),d.title.css("cursor","default"),d.draggable=!1),d.settings.veil?(d.settings.veil.sfDialog("enable").sfDialog({closeOnEscape:d.veilCloseOnEscape,onClose:d.veilClose}),la()):d.dialog.fadeOut(g?0:500),d.isOpen=!1,d.settings.modal&&d.notTabbable&&d.notTabbable.unbind("focus.sfd"),H(!0,g)))}function K(a){var b=c.extend(!0,{async:!1,noanim:!0,usePositioning:d.usePositioning===!0,anchorAttachment:d.settings.anchorAttachment,callback:null},a);b.disableAnimations=b.noanim,d.base.position&&d.base.position.reposition(b),$sf.util.event.trigger("resize resize.sfDialog",{moduleType:"sfDialog",moduleElement:d.me})}function L(a){if(!d.msgOverlay){switch(a.keyCode){case 27:"undefined"!=typeof d.settings&&0==d.settings.cancelDisabled&&1==d.settings.closeOnEscape&&1==Q()&&1==P()&&setTimeout(U,10)}return!0}}function M(a){function b(a){switch(f){case"back":if(a>0){var e=a-1;z(e)?b(e):(aa(e),q())}break;case"next":var e=a+1;k>e-1&&(z(e)?b(e):(d.settings.buttons[1].isDisabled=!1,aa(e),q()));break;case"finish":d.settings.buttons[1].isDisabled=!1;var h=!0;c.isFunction(d.settings.onWizardFinish)&&(h=!(0==d.settings.onWizardFinish(g,!1))),h&&J(!1,g,!1)}}c(document.body).trigger("click",a);var e=c(a.target).closest("a[data-button]");if(0==e.length)return!1;var f=e.attr("data-button");if(0==f.length)return!1;var g=d.buttonsIdx[f];if(null!=g){if(0==g.cancel&&(0==Q()||1==g.isDisabled))return!1;if(1==c.isFunction(g.onClick)){var h=g.onClick(d.me,f,g,a);if(0==h)return a.stopPropagation(),!1}if("undefined"!=typeof d.settings){if(1==c.isFunction(d.settings.onButtonClick)&&0==d.settings.onButtonClick(d.me,f,g,a))return a.stopPropagation(),!1;if(1==g.cancel&&1==d.settings.autoCloseOnCancel)return J(!1,g,!1),!1;if("wizard"===d.mode){var i=_(),j=d.tabs_menu.find("ul:first"),k=j.children().length;b(i)}}}return!1}function N(a){var b="undefined"!=typeof a&&a.fromMethod===!0;if(d.initialized===!1||b){var e=a;b&&"undefined"!=typeof d.settings&&(e=c.extend(!0,{},d.settings),d.hasParent||("modal"===d.baseType&&d.base.overlay.hide(!0),d.me=d.me.remove()),d.settings.destroyOnClose=!0,I(),d.settings=e,delete a.fromMethod),h(a)}if(d.settings.noConflict===!1&&($sfDialog=f),P()!==!0){if(d.dialog.show(),d.buttonsWidth=0,d.buttons.children().each(function(a,b){var e=c(b).outerWidth(!0);d.buttonsWidth+=e}),d.isOpen=!0,d.base.open(),fa(!0),d.contentShiftEventId||(d.contentShiftEventId=$sf.util.event.on("contentShift",K)),d.settings.veil){var g=d.settings.veil.sfDialog("settings");d.veilClose=g.onClose,d.veilCloseOnEscape=g.closeOnEscape,d.settings.veil.sfDialog("disable").sfDialog({onClose:ma,closeOnEscape:!1}),ka()}1==c.isFunction(d.settings.onOpen)&&d.settings.onOpen(d.dialog,d.me);var i=_();if(aa(0>i?0:i),d.settings.autoResize&&(d.resizeTimer=setInterval(K,500)),d.settings.modal){var j=c(":input, :button, a[href]",d.dialog);d.notTabbable=c(":input, :button, a[href]").not(j),d.firstTabbable=c(":input, a[href]",d.content).first(),d.notTabbable.bind("focus.sfd",function(){c(this).blur(),d.firstTabbable.length>0&&d.firstTabbable.focus()})}c.fn.sfDialog.opens++}}function O(){J(!1,null,!0)}function P(){return d.initialized&&d.isOpen}function Q(){return d.isEnabled}function R(){if(1!=Q()&&0!=d.initialized){d.isEnabled=!0,d.inner.find(".sf-dialog-overlay,.sf-dialog-overlay-alt").remove();var a=function(){d.inner.css("overflow",d.innerOverflow)};$sf.util.ua.webkit?setTimeout(a,200):a(),q()}}function S(){0!=Q()&&0!=d.initialized&&(d.isEnabled=!1,d.innerOverflow=d.inner.css("overflow"),d.inner.css("overflow","hidden"),d.inner[0].scrollTop=0,"fos"!=$sf.skin&&d.inner.append('<div style="display: block; position: absolute; z-index:100;" class="sf-dialog-overlay sf-dialog-overlay-alt"></div>'),q())}function T(a){1==P()?J():N(a)}function U(){if(0==P())return!0;if(0==d.settings.noConflict&&($sfDialog=f),null!=d.cancel){if(1==c.isFunction(d.cancel.onClick)&&0==d.cancel.onClick(d.me,d.cancel.id))return!1;if(1==c.isFunction(d.settings.onButtonClick)&&0==d.settings.onButtonClick(d.me,d.cancel.id))return!1}return J(!1,null,!0),!0}function V(a){var b={preload:[]},d=c.extend(!0,b,a);c.each(d.preload,function(a,b){var d=b.split("#")[0],e=c.fn.sfDialog.resources[d];"undefined"==typeof e&&(d.toLowerCase().lastIndexOf(".js")==d.length-3?(d=d.substr(0,d.length-3),require(d,function(a){a=a.join("\r\n"),c.fn.sfDialog.resources[d]={url:d,data:a}})):c.ajax({dataType:"text",url:d,success:function(a){c.fn.sfDialog.resources[d]={url:d,data:a}},error:function(){c.fn.sfDialog.resources[d]={url:d,data:null}}}))})}function W(a,b){var e=b.split("#"),f=(e[0],"");if(null!=d.settings.page&&(f=d.settings.page),xml=c(a),f.length>0){var g=xml.filter("#"+f),h=xml.filter('[data-page-id="'+f+'"]');d.me.empty().attr({style:g.attr("style"),"data-title":g.attr("data-title")}).append(g.html()),h.length>0&&d.meBody.append(h)}else{var i=xml.find("title");i.length>0&&d.title.text(i.text()),d.me.empty().append(xml)}}function X(){var a={};if(null==d.load_url)return a;var b=d.load_url.split("#");if(b.length<2)return a;var c=b[1],e=c.split("&");for(i=0;i<e.length;i++)v=e[i],args=v.split("="),2==args.length&&(a[args[0]]=args[1]);return a}function Y(a){var b={load:null},e=c.extend(!0,b,a);if("string"==typeof e.load&&0!=e.load.length){d.load_url=a.load,a.load=null,"undefined"!=typeof d.settings&&d.settings.loadFresh===!0&&(delete d.settings,h(a,!0));var f=d.load_url.split("#"),g=f[0],i=c.fn.sfDialog.resources[g];if("undefined"!=typeof i){var j=!1;return null!=i.data?(W(i.data,d.load_url),j=!0):d.me.html(d.settings.texts.error),null!=i.title&&(d.settings.title=i.title),C(),q(),1==c.isFunction(d.settings.onLoad)&&d.settings.onLoad(d.me,a,j),fa(!0),void(d.settings.veil&&ka())}d.settings.veil?d.dialog.hide():(d.me.html(d.settings.texts.loading),q(!0),fa()),g.toLowerCase().lastIndexOf(".js")==g.length-3?(g=g.substr(0,g.length-3),require(g,function(b){if(null!=b&&(b.title&&(d.settings.title=b.title),b=b.data),0!=P()){d.dialog.show(),c.fn.sfDialog.resources[g]={url:g,data:b};var e=!1;null!=b?(W(b,d.load_url),e=!0):d.me.html(d.settings.texts.error),C(),q(),1==c.isFunction(d.settings.onLoad)&&d.settings.onLoad(d.me,a,e),K({async:!0,noanim:!0,callback:function(){d.settings.veil&&ka()}})}})):c.ajax({dataType:"text",url:g,success:function(b){0!=P()&&(d.dialog.show(),c.fn.sfDialog.resources[g]={url:g,data:b},W(b,d.load_url),C(),q(),1==c.isFunction(d.settings.onLoad)&&d.settings.onLoad(d.me,a,!0),K({async:!0,noanim:!0,callback:function(){d.settings.veil&&ka()}}))},error:function(){0!=P()&&(d.dialog.show(),c.fn.sfDialog.resources[g]={url:g,data:null},d.me.html(d.settings.texts.error),q(),1==c.isFunction(d.settings.onLoad)&&d.settings.onLoad(d.me,a,!1),K({async:!0,noanim:!0}))}})}}function Z(a){var b={save:null},e=c.extend(!0,b,a);if("string"==typeof e.save&&0!=e.save.length){var g=e.save;a.save=null;var h=c("input[type='radio'],input[type='checkbox'],select,textarea",f),i=c("input[type='radio']",f),j=[];for(i.each(function(a){var b=c(h[a]);1==b.prop("checked")&&j.push(b)}),h.each(function(a){var b=c(h[a]);if(1==b.is("input"))0==b.prop("checked")?b.removeAttr("checked"):b.attr("checked","checked");else if(1==b.is("select")){var d=b.prop("selectedIndex");b.children().removeAttr("selected"),b.children().eq(d).attr("selected","selected")}}),k=0;k<j.length;k++){var k=j[k];k.attr("checked","checked")}var l=d.me.html().replace(/ selected=\"\"/g,"");l=l.replace(/ selected=\"\"/g,"");var m={name:g,settings:c.extend(!0,{},d.settings),meId:d.me.attr("id"),html:l,title:d.me.attr("data-title"),styles:d.me.attr("style")};m.settings.save=null,m.settings.load=null,m.settings.restore=null,c.fn.sfDialog.saves[g]=m,c.fn.sfDialog.savesArr.push(m)}}function $(a){var b={restore:null},e=c.extend(!0,b,a);if("undefined"!=typeof e.restore){var f=e.restore;"undefined"!=typeof a&&"undefined"!=typeof a.restore&&delete a.restore;var g;return"string"==typeof e.restore?g=c.fn.sfDialog.saves[f]:1==f&&c.fn.sfDialog.savesArr.length>0&&(g=c.fn.sfDialog.savesArr.pop()),"undefined"==typeof g?!1:(d.settings=g.settings,d.me.empty().append(g.html).attr({style:g.styles,"data-title":g.title}),C(),q(),fa(!0),c.isFunction(d.settings.onRestore)===!0&&d.settings.onRestore(a),!0)}}function _(){return null==d.tabs?-1:d.tabs.sfTabs("getSelected")}function aa(a){if(null!=d.tabs){var b=d.tabs.sfTabs("select",a);return"wizard"===d.mode&&a>0&&(d.settings.buttons[1].isDisabled=!1),
fa(),b}}function ba(a){return null!=d.tabs?d.tabs.sfTabs("hide",a):void 0}function ca(a){return null!=d.tabs?d.tabs.sfTabs("show",a):void 0}function da(a){return d.tabs.sfTabs("isVisible",a)}function ea(a){switch("wizard"===d.mode&&(a="right"),d.buttons.removeClass("sf-dialog-buttons-left sf-dialog-buttons-right"),a.toLowerCase()){case"left":d.settings.buttonAlign="left",d.buttons.addClass("sf-dialog-buttons-left");break;case"right":d.settings.buttonAlign="right",d.buttons.addClass("sf-dialog-buttons-right");break;default:d.settings.buttonAlign="center"}}function fa(a){function b(a){if(d.settings.veil){a.disableAnimations=!0,a.position.top=d.settings.veil.sfDialog("getVeilTop");var b=a.page.height-(a.position.top-a.page.top)-a.deltas.marginY;a.dimensions.height>b&&(a.dimensions.height=b,a.dimensions.innerHeight=a.dimensions.height-a.deltas.y-a.deltas.extraHeight)}return a}function e(){var a=(0==d.settings.titleHidden?d.title.outerHeight(!0):0)+d.buttons.outerHeight(!0)+(d.footer?d.footer.outerHeight(!0):0)+(d.tabsHeight>0?d.tabsHeight:0===d.tabsHeight&&null!=d.tabs&&1==d.dialog.is(":visible")?d.tabs_menu.outerHeight():0);return a}function f(a){var b=d.me.children("div.sf-tabs-actv-content"),e=d.dialog.find("ul.sf-tabs-bborder"),f=a;if(e.length>0&&b.length>0){var g=e.outerWidth(!0)-e.width();e.children("li").each(function(){g+=c(this).outerWidth(!0)}),f=g>b.width()?g:b.width()}return 0==b.length&&(b=d.me.children("div:first")),1==b.length&&b.css("width").indexOf("px")>0&&(a=b.width()>f?b.width():f),a}function g(){var a={};return d.settings.veil&&(a.top=d.settings.veil.sfDialog("getVeilTop"),a.left=d.settings.veil.sfDialog("getVeilLeft")+(d.settings.veil.sfDialog("getVeilWidth")-d.dialog.width())/2),a}0!=P()&&d.base.positioner({idealWidth:d.settings.veil?d.settings.veilWidthIdeal:d.settings.dialogWidthIdeal,widthMin:d.buttonsWidth+10,heightMin:d.settings.veil?0:d.settings.dialogHeightMin,margin:c.fn.sfDialog.globals.dialogMargin,extraHeight:e,widthAdjustment:f}).set({anchorAttachment:d.settings.anchorAttachment,disableAnimations:a,specialSizing:b,afterPositioning:d.settings.veil?g:null,onAfterPosition:function(){c.isFunction(d.settings.onAfterPosition)&&d.settings.onAfterPosition(d.me)},usePositioning:d.usePositioning===!0,containerOffset:d.settings.dialogOffset})}function ga(a,b){K()}function ha(){if(!d.content)return 0;var a="wizard toggle tabs".indexOf(d.mode)<=-1?d.content.offset().top:d.tabs_menu.offset().top;return a}function ia(){if(!d.dialog)return 0;var a=d.dialog.offset().left;return a}function ja(){if(!d.dialog)return 0;var a=d.dialog.width();return a}function ka(){var a=d.dialog.height();parseInt(d.content.position().top);d.dialog.show().css({height:"10px",position:"absolute",overflow:"hidden"}).animate({height:a+"px"}),d.content.css({position:"relative",top:"-"+a+"px"}).animate({top:"0px"},function(){d.content.css({position:"",top:""})}),d.settings.veil.sfDialog("setVeilDraggable",!1)}function la(){d.dialog.animate({height:"0px"},function(){d.dialog.hide()});var a=d.dialog.height();d.content.css({position:"relative",top:"0px"}).animate({top:"-"+a+"px"},function(){d.content.css({position:"",top:""})}),d.settings.veil.sfDialog("setVeilDraggable",!0)}function ma(a,b,c){return d.me.sfDialog("close"),b===!0}function na(){H()}function oa(a,b){if(1==d.settings.draggable&&"undefined"!=typeof d.me.draggable){var e="string"==typeof b&&b.length>0?"."+b:"";d.dialog.bind("drag"+e+", ondrag"+e,function(b){c.isFunction(a)&&a(b)}).bind("dragstart"+e+", ondragstart"+e,function(){$sf.util.event.trigger("dragstart dragstart.sfDialog",{moduleType:"sfDialog",moduleElement:d.me})}).bind("dragstop"+e+", ondragstop"+e,function(){$sf.util.event.trigger("dragend dragend.sfDialog",{moduleType:"sfDialog",moduleElement:d.me})})}}if(0==f.length)return f;if(d.logger=$sf.util.logger(f.attr("id"),"duel").log,d.logdir=$sf.util.logger(f.attr("id"),"duel").dir,"open"===e&&"undefined"!=typeof g&&(g.hasOwnProperty("modal")&&"undefined"!=typeof d.settings||(e=g)),"string"==typeof e){var pa=e.toLowerCase();switch(pa){case"isinitialized":return d.initialized;case"open":g="undefined"!=typeof g?c.extend(!0,{fromMethod:!0},g):void 0,N(g);break;case"close":J();break;case"isopen":return P();case"toggle":T(g);break;case"cancel":U();break;case"destroy":H();break;case"enable":R();break;case"disable":S();break;case"save":Z(g);break;case"preload":V(g);break;case"load":Y(g);break;case"restore":$(g);break;case"isenabled":return Q();case"getbutton":return r(g);case"setbutton":s(g);break;case"getbuttons":return t(g);case"setbuttons":u(g);break;case"isbuttondisabled":return w(g);case"disablebutton":y(g);break;case"enablebutton":x(g);break;case"istabdisabled":return z(g);case"disabletab":B(g);break;case"enabletab":A(g);break;case"resize":K(g);break;case"getselectedtab":return _(g);case"selecttab":aa(g);break;case"hidetab":ba(g);break;case"showtab":ca(g);break;case"setbuttonalign":ea(g);break;case"setondrag":oa(g);break;case"setveildraggable":p(g);break;case"setattachment":n(g);break;case"getloadqs":return X();case"getloadurl":return d.load_url;case"getveiltop":return ha();case"getveilleft":return ia();case"getveilwidth":return ja();case"zelement":return d.dialog;case"istabvisible":return da(g);case"settings":return d.settings;case"init":default:h(g)}}else g=e,h(g);return d.me}},h.fn(h,d,e))},c.fn.sfDialog.instances={},c.fn.sfDialog.resources={},c.fn.sfDialog.saves={},c.fn.sfDialog.savesArr=[],c.fn.sfDialog.opens=0,c.fn.sfDialog.globals={dialogMargin:{x:40,y:20}},c.fn.sfDialog}($sf.getjQuery())}),define("starfield/sf.alerts",["css!starfield/sf.core.css","jq!starfield/jquery.mod"],function(){return function(a){var b={options:{autoCloseTimeout:0,style:null,message:null,destroyOnClose:null,hideClose:!1,onClose:null,onClosed:null,onShown:null},closed:!1,timer:null,_create:function(){this.orphan=0===this.element.parent().length,null===this.options.destroyOnClose&&(this.options.destroyOnClose=this.orphan),this._build()},_build:function(){var b=this,c=b.options;b.msgFromEl=null===c.message?b.element.html():"";var d=b.element,e=c.message?c.message:""!==d.html().replace(/[ \s\t]/g,"")?d.html():"",f=null!==c.style?c.style:d.attr("data-style");d.attr("data-style",f);var g=a('<a href="javascript: void(0);" onclick="return false;" class="sf-alert-close sf-icn sf-icn-close2"><span class="sf-offscrn">Close</span></a>').bind("click",function(){b.close()});d.empty().bind("mouseover",function(a){b._hover(a)}).prepend(g).addClass("sf-alert").addClass("error success info".indexOf(f)>-1?"sf-alert-"+f:"warn"===f?"sf-alert-warning":"sf-alert-info"),c.hideClose===!0&&g.hide();a("<div/>").insertAfter(g).addClass("sf-icn-alert").addClass("error success info".indexOf(f)>-1?"sf-icn-alert-"+f:"warn"===f?"sf-icn-alert-warning":"sf-icn-alert-info").append(a('<span class="sf-offscrn"/>').text("error"===f?"error":"warn"===f?"warning":"success"===f?"confirmation":"information")).after(a('<div class="sf-alert-msg"/>').html(e?e:""));d.hide().fadeIn(function(){a.isFunction(c.onShown)&&c.onShown(d)}),c.autoCloseTimeout>0&&(b.timer=window.setTimeout(function(){b.close()},c.autoCloseTimeout))},_hover:function(){this.timer&&(window.clearTimeout(this.timer),this.timer=null)},close:function(b,c,d){function e(){f.element.removeClass("sf-alert sf-alert-warning sf-alert-info sf-alert-success sf-alert-error").empty(),a.isFunction(h.onClosed)&&h.onClosed(g),f.options.destroyOnClose===!0?f.destroy(c,!0):a.isFunction(c)&&c()}"boolean undefined".indexOf(typeof b)<0&&b.hasOwnProperty("force")&&(c=b.cb,d=b.now,b=b.force);var f=this,g=f.element,h=f.options;return f.closed=!0,a.isFunction(h.onClose)!==!0||h.onClose(g)!==!1||"undefined"!=typeof b&&b===!0?(f.timer&&(window.clearTimeout(f.timer),f.timer=null),void(d===!0?e():g.fadeOut(e))):!1},destroy:function(b,c){!a.isFunction(b)&&"undefined"!=typeof b&&b.hasOwnProperty("callback")&&(c=b.removeElement,b=b.callback);var d=this;d.closed||d.close(!0,void 0,!0),d.orphan||c===!0?d.element.remove():d.msgFromEl.length>0&&d.element.html(d.msgFromEl),d._execSuper("destroy",b)}};return $sf.util.selector("sf-alert",{root:{selector:".sf-alert",tests:{style:function(a,b){return a.attr("data-style")===b}}}}),$sf.util.module("Alert",b)}($sf.getjQuery())}),define("starfield/sf.msg.overlay",["css!starfield/sf.core.css","jq!starfield/jquery.mod","domReady!"],function(){return function(a){var b={supportLegacyOptions:!0,options:{style:"progress",message:null},message:null,initialized:!1,_create:function(){this.element.data("sf-msgo-id",this.id),this._setMessage(),this.element.addClass(this.baseClass)},_init:function(b){var c=!1;"undefined"!=typeof b&&a.isPlainObject(b)&&(a.extend(!0,this.options,b),c=!0),(!this.initialized||c)&&this._setMessage()},_setMessage:function(){this.options.message="string"!=typeof this.options.message?null:this.options.message,null===this.options.message?this.initialized&&this.destroy():this.initialized?this._setupOverlay(this.overlay):this._build()},_build:function(){var b,c,d=this,e=d.element;e.children().first().is(".sf-msg-overlay-wrap")?(b=d.wrap=e.children().first(),b.empty()):(b=d.wrap=a("<div class='sf-msg-overlay-wrap' />"),e.prepend(b)),c=d.overlay=a("<div class='sf-msg-overlay' />"),b.append(c),c.data("sf-element",d.element),d.initialized=!0,d.pos=e.css("position"),"static"==d.pos&&e.css("position","relative"),$sf.util.ua.ie&&$sf.util.ua.major<=6&&(d.height=e.css("height"),d.overflow=e.css("overflow")),a(window).bind("resize.sfmsol-"+d.id,function(){d._build()}),setTimeout(function(){d._setupOverlay(c)},0)},_setupOverlay:function(a){var b=this.element;if($sf.util.ua.ie&&$sf.util.ua.major<=6){var c=b.height();b.css({height:c+"px",overflow:"hidden"})}a.html(("progress"==this.options.style?'<img src="{0}/sf.core/images/sf-spinner.gif" width="16" height="16" class="sf-msg-overlay-status" /> '.sfFormat($sf.require.img_root):"")+this.options.message);var d=a.innerHeight()/2*-1;a.css("margin-top",d+"px")},set:function(a){var b="",c="";"string"==typeof a?b=a:("undefined"!=typeof a.message&&(b=a.message),"undefined"!=typeof a.style&&(c=a.style)),""!==b&&(this.options.message=b,""!==c&&(this.options.style=c),this._setMessage())},destroy:function(b){var c=this.element;a(window).unbind("resize.sfmsol-"+this.id),this.wrap&&this.wrap.length>0&&this.wrap.remove(),"static"==this.pos&&c.css("position","static"),$sf.util.ua.ie&&$sf.util.ua.major<=6&&c.css({height:this.height,overflow:this.overflow}),this.initialized=!1,c.removeClass(this.baseClass).removeData("sf-msgo-id"),this._execSuper("destroy",b)}};return $sf.util.selector("sf-msgoverlay",{root:{selector:".sf-msgoverlay",tests:{id:function(a,b){return a.attr("id")===b}}}}),$sf.util.module("MsgOverlay",b)}($sf.getjQuery())}),define("starfield/sf.i18n/duel",[],function(){return{common:{yes:"Yes",no:"No",ok:"OK",cancel:"Cancel",close:"Close",select:"Select",current:"Current",newStr:"New"},"sf.alerts":{},"sf.upload":{select:"Select Files",upload:"Start Upload",intro:"Select files to upload.",introWithDrop:"Select files to upload, or drop files here.",introMaxItems:"Maximum uploads reached.",statusPending:"{0} file(s) pending.",statusUploading:"{0} file(s) uploading...",statusFinished:"<strong>Finished!</strong> {0} file(s) uploaded with {1} error(s).",pending:"Pending",finished:"Finished",uploading:"Uploading...",dd_info:"Drop files here to upload",dd_dest:"Destination",dd_limits:"Restrictions...",dd_cancel:"Cancel",cancel:"Are you sure you want to cancel all uploads?",maxSize:"Maximum size exceeded.",yes:"Yes",no:"No",error:"Error",retry:"Try again"},"sf.dialog":{title:"&lt;Untitled&gt;",button:"Button",stepOf:"Step {0} of {1}",error:"Woops! An error has occurred.",loading:"Loading, please wait...",wizardNext:"Next",wizardBack:"Back",wizardFinish:"Finish",wizardCancel:"Cancel"},"sf.tipper":{more:"Show More",less:"Show Less"},"sf.growl":{closeAll:"close all"},"sf.share":{fbThanks:"Thank you for liking this page",fbAlso:"You can also like this page.",fbTitle:"Post {{domain}} to your Facebook wall",fbLikePage:"Like this page on Facebook:",twLoad:"Loading Tweet Box...",twDup:"Tweet duplicates are not allowed. Please change your message.",twErr:"There was an error posting your Tweet.",twOK:"Your Tweet was posted successsfully.",twUnavail:"Tweet posting is temporarily unavailable.<br/>Please try again later.",twPost:"Post to Twitter:",twFollowPage:"Follow this page on Twitter:",gplusBSupport:"It appears Google Plus doesn't support your browser.",gpPost:"Post to Google Plus:",gpFollowPage:"Follow this page on Google:",checkSite:"Check out this site!",shareSite:"Share this site"},"sf.validator":{pwMatch:"Password matches",xToYCharsLong:"be {0}-{1} characters long",tooMany:"({0} fewer)",tooFew:"({0} more)",hasLower:"include a lower case letter",hasUpper:"include an upper case letter",startsWithLetter:"starts with a letter",hasSpecial:"include a special character ({0})",excludeChars:"not include these characters ({0})",hasNum:"include a number",req:"is required",validEmail:"Valid email address",criteria:"Criteria:"},"sf.colorpicker":{newColor:"New",currentColor:"Current",ok:"Select",cancel:"Cancel",advanced:"Advanced",basic:"Basic"},"sf.tourguide":{more:"Show More",less:"Show Less",newFeatures:"New Features",newFeature:"New Feature",hide:"Hide"},"sf.pagehelp":{pageHelp:"Page Help",showHelp:"Show Page Help",hideHelp:"Hide Page Help",quickA:"Quick answers for the current page",home:"Home"},"sf.datagrid":{filter:"Filter",items:"Items",selected:"Selected",page:"Page",of:"of",display:"Display",none:"None",all:"All",actions:"Actions",loading:"Loading data...",noDataToDisplay:"No data to display.",noDataForPage:"No data retrieved for this page.",dataRetrivalError:"Problem loading data. Please try again later."},"sf.player":{relatedVid:"Related videos"},"sf.session":{title:"Security Logout",message:"To protect your account, we're going to log you out because of inactivity.<br /><br /><p>Click <strong>Stay Logged In</strong> to extend your session.</p>",keepAlive:"Stay Logged In",logout:"Log out",timeLeft:"seconds to<br />automatic logout",attention:"ATTENTION"},"sf.browser":{latest:"Get the latest",updateTitle:"If you update your browser",updateMessage:"You'll get the most out of this site and others on the web.",learn:"Learn more &raquo;",warnTitle:"Are you sure?",warnMessage:"Your current browser version could negatively impact your experience on this site and others utilizing technology found in newer browsers. We want you to have the outstanding site experience that comes from using a fully-supported browser. Upgrade now to enjoy this site’s functionality in its entirety.",ignore:"Ignore",reqTitle:"Browser Upgrade Needed",reqMessage:"This site can't be accessed with your current browser. To provide a richer user experience, the site uses advanced functionality found only in newer browsers.<p>Click <strong>Get Now</strong> for the latest browser.</p>",ff:"Firefox",ffLink:"https://www.mozilla.org/en-US/firefox/new/",chrome:"Chrome",chromeLink:"https://www.google.com/intl/us/chrome/browser/desktop/",ie:"Internet Explorer",ieLink:"http://windows.microsoft.com/en-US/internet-explorer/download-ie",safari:"Safari",safariLink:"https://support.apple.com/en_US/downloads/safari",feedback:"Feedback",leaveFeedback:"Leave Feedback",feedbackLink:"https://www.godaddy.com/help/which-browsers-work-with-your-products-6451",feedbackPL:"http://help.secureserver.net/article/6451",getNow:"Get Now"},culture:"en"}}),define("starfield/sf.core.pkg",["css!starfield/sf.core.css","starfield/sf.tabs","starfield/sf.growl","starfield/sf.tipper","starfield/sf.base.dialog","starfield/sf.base.modal","starfield/sf.base.flyout","starfield/sf.dialog","starfield/sf.alerts","starfield/sf.msg.overlay","starfield/sf.i18n/duel"],function(){});