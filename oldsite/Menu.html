<!DOCTYPE html>
<!-- saved from url=(0046)http://www.champions-sportsgrill.com/menu.html -->
<html lang="en" dir="ltr" data-tcc-ignore=""><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><title>Menu</title><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><link rel="stylesheet" type="text/css" href="./Menu_files/site.css"><script> if (typeof ($sf) === "undefined") { $sf = { baseUrl: "https://p3pprd001.cloudstorage.secureserver.net/wsbv7-assets/WSB7_J_20250303_2130_DEP-03042_5487/v2", skin: "app", preload: 0, require: { jquery: "https://p3pprd001.cloudstorage.secureserver.net/wsbv7-assets/WSB7_J_20250303_2130_DEP-03042_5487/v2/libs/jquery/jq.js", paths: { "wsbcore": "common/wsb/core", "knockout": "libs/knockout/knockout" } } }; } </script><script id="duel" src="./Menu_files/duel.js.download"></script><script charset="utf-8" async="" src="./Menu_files/jq.js.download"></script><script> define('jquery', ['jq!starfield/jquery.mod'], function(m) { return m; }); define('appconfig', [], { documentDownloadBaseUrl: 'https://nebula.wsimg.com' }); </script><meta http-equiv="Content-Location" content="menu.html"><meta name="generator" content="Starfield Technologies; Go Daddy Website Builder 7.0.5350"><meta property="og:type" content="website"><meta property="og:title" content="Menu"><meta property="og:site_name" content="Champions Sports Grilll"><meta property="og:url" content="http://www.champions-sportsgrill.com/menu.html"><meta property="og:image" content="https://nebula.wsimg.com/546aa179c19a0abab771af1f358d4843?AccessKeyId=1549AF287DE5730D6508&amp;disposition=0&amp;alloworigin=1"><script charset="utf-8" async="" src="./Menu_files/media.gallery.js.download"></script><script charset="utf-8" async="" src="./Menu_files/cookiemanager.js.download"></script><script charset="utf-8" async="" src="./Menu_files/iebackground.js.download"></script><script charset="utf-8" async="" src="./Menu_files/util.instances.js.download"></script><script charset="utf-8" async="" src="./Menu_files/util.model.js.download"></script><script charset="utf-8" async="" src="./Menu_files/documentHelper.js.download"></script><script charset="utf-8" async="" src="./Menu_files/util.window.js.download"></script><link rel="stylesheet" href="chrome-extension://ihcjicgdanjaechkgeegckofjjedodee/app/content-style.css"></head><body cz-shortcut-listen="true"><style data-inline-fonts="">/* vietnamese */
@font-face {
  font-family: 'Allura';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/allura/v22/9oRPNYsQpS4zjuA_hAgWDto.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Allura';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/allura/v22/9oRPNYsQpS4zjuA_hQgWDto.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Allura';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/allura/v22/9oRPNYsQpS4zjuA_iwgW.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic */
@font-face {
  font-family: 'Amatic SC';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/amaticsc/v27/TUZyzwprpvBS1izr_vOEDuSfQZQ.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* hebrew */
@font-face {
  font-family: 'Amatic SC';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/amaticsc/v27/TUZyzwprpvBS1izr_vOECOSfQZQ.woff2) format('woff2');
  unicode-range: U+0307-0308, U+0590-05FF, U+200C-2010, U+20AA, U+25CC, U+FB1D-FB4F;
}
/* vietnamese */
@font-face {
  font-family: 'Amatic SC';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/amaticsc/v27/TUZyzwprpvBS1izr_vOEBeSfQZQ.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Amatic SC';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/amaticsc/v27/TUZyzwprpvBS1izr_vOEBOSfQZQ.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Amatic SC';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/amaticsc/v27/TUZyzwprpvBS1izr_vOECuSf.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* vietnamese */
@font-face {
  font-family: 'Arizonia';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/arizonia/v22/neIIzCemt4A5qa7mv5WOFqwKUQ.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Arizonia';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/arizonia/v22/neIIzCemt4A5qa7mv5WPFqwKUQ.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Arizonia';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/arizonia/v22/neIIzCemt4A5qa7mv5WBFqw.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin */
@font-face {
  font-family: 'Averia Sans Libre';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/averiasanslibre/v20/ga6XaxZG_G5OvCf_rt7FH3B6BHLMEdVOEoI.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin */
@font-face {
  font-family: 'Cabin Sketch';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/cabinsketch/v22/QGYpz_kZZAGCONcK2A4bGOj8mNhN.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* vietnamese */
@font-face {
  font-family: 'Francois One';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/francoisone/v21/_Xmr-H4zszafZw3A-KPSZut9zgiRi_Y.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Francois One';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/francoisone/v21/_Xmr-H4zszafZw3A-KPSZut9zwiRi_Y.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Francois One';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/francoisone/v21/_Xmr-H4zszafZw3A-KPSZut9wQiR.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Fredericka the Great';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/frederickathegreat/v22/9Bt33CxNwt7aOctW2xjbCstzwVKsIBVV--StxbcVcg.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Fredericka the Great';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/frederickathegreat/v22/9Bt33CxNwt7aOctW2xjbCstzwVKsIBVV--Sjxbc.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin */
@font-face {
  font-family: 'Jacques Francois Shadow';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/jacquesfrancoisshadow/v26/KR1FBtOz8PKTMk-kqdkLVrvR0ECFrB6Pin-2_p8Suno.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin */
@font-face {
  font-family: 'Josefin Slab';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/josefinslab/v28/lW-swjwOK3Ps5GSJlNNkMalNpiZe_ldbOR4W71msR349Kg.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Kaushan Script';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/kaushanscript/v18/vm8vdRfvXFLG3OLnsO15WYS5DG72wNJHMw.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Kaushan Script';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/kaushanscript/v18/vm8vdRfvXFLG3OLnsO15WYS5DG74wNI.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Love Ya Like A Sister';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/loveyalikeasister/v22/R70EjzUBlOqPeouhFDfR80-0FhOqJubN-BeL-3xdgGE.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Love Ya Like A Sister';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/loveyalikeasister/v22/R70EjzUBlOqPeouhFDfR80-0FhOqJubN-BeL9Xxd.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Merriweather';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/merriweather/v32/u-4D0qyriQwlOrhSvowK_l5UcA6zuSYEqOzpPe3HOZJ5eX1WtLaQwmYiScCmDxhtNOKl8yDr3icaGV31GvU.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Merriweather';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/merriweather/v32/u-4D0qyriQwlOrhSvowK_l5UcA6zuSYEqOzpPe3HOZJ5eX1WtLaQwmYiScCmDxhtNOKl8yDr3icaEF31GvU.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Merriweather';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/merriweather/v32/u-4D0qyriQwlOrhSvowK_l5UcA6zuSYEqOzpPe3HOZJ5eX1WtLaQwmYiScCmDxhtNOKl8yDr3icaG131GvU.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Merriweather';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/merriweather/v32/u-4D0qyriQwlOrhSvowK_l5UcA6zuSYEqOzpPe3HOZJ5eX1WtLaQwmYiScCmDxhtNOKl8yDr3icaGl31GvU.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Merriweather';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/merriweather/v32/u-4D0qyriQwlOrhSvowK_l5UcA6zuSYEqOzpPe3HOZJ5eX1WtLaQwmYiScCmDxhtNOKl8yDr3icaFF31.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Offside';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/offside/v25/HI_KiYMWKa9QrAykc5joR6-d.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Offside';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/offside/v25/HI_KiYMWKa9QrAykc5boRw.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4taVIGxA.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4kaVIGxA.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4saVIGxA.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4jaVIGxA.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* hebrew */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4iaVIGxA.woff2) format('woff2');
  unicode-range: U+0307-0308, U+0590-05FF, U+200C-2010, U+20AA, U+25CC, U+FB1D-FB4F;
}
/* math */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B5caVIGxA.woff2) format('woff2');
  unicode-range: U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;
}
/* symbols */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B5OaVIGxA.woff2) format('woff2');
  unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
}
/* vietnamese */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4vaVIGxA.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4uaVIGxA.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4gaVI.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Oswald';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/oswald/v56/TK3_WkUHHAIjg75cFRf3bXL8LICs1_FvsUtiZTaR.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Oswald';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/oswald/v56/TK3_WkUHHAIjg75cFRf3bXL8LICs1_FvsUJiZTaR.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Oswald';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/oswald/v56/TK3_WkUHHAIjg75cFRf3bXL8LICs1_FvsUliZTaR.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Oswald';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/oswald/v56/TK3_WkUHHAIjg75cFRf3bXL8LICs1_FvsUhiZTaR.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Oswald';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/oswald/v56/TK3_WkUHHAIjg75cFRf3bXL8LICs1_FvsUZiZQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Over the Rainbow';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/overtherainbow/v22/11haGoXG1k_HKhMLUWz7Mc7vvW5ulvqs9eA2.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Over the Rainbow';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/overtherainbow/v22/11haGoXG1k_HKhMLUWz7Mc7vvW5ulvSs9Q.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Pacifico';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/pacifico/v22/FwZY7-Qmy14u9lezJ-6K6MmTpA.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Pacifico';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/pacifico/v22/FwZY7-Qmy14u9lezJ-6D6MmTpA.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Pacifico';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/pacifico/v22/FwZY7-Qmy14u9lezJ-6I6MmTpA.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Pacifico';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/pacifico/v22/FwZY7-Qmy14u9lezJ-6J6MmTpA.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Pacifico';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/pacifico/v22/FwZY7-Qmy14u9lezJ-6H6Mk.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Romanesco';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/romanesco/v21/w8gYH2ozQOY7_r_J7mSX1XYKmOo.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Romanesco';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/romanesco/v21/w8gYH2ozQOY7_r_J7mSX23YK.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Sacramento';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sacramento/v16/buEzpo6gcdjy0EiZMBUG4CMf_exL.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Sacramento';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sacramento/v16/buEzpo6gcdjy0EiZMBUG4C0f_Q.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Seaweed Script';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/seaweedscript/v16/bx6cNx6Tne2pxOATYE8C_Rsoe3WA8qY2VQ.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Seaweed Script';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/seaweedscript/v16/bx6cNx6Tne2pxOATYE8C_Rsoe3WO8qY.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Special Elite';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/specialelite/v19/XLYgIZbkc4JPUL5CVArUVL0ntn4OSEFt.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Special Elite';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/specialelite/v19/XLYgIZbkc4JPUL5CVArUVL0ntnAOSA.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* cyrillic-ext */
@font-face {
  font-family: 'Bitter';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8fbfOLXOXWh2.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Bitter';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8fbfOLzOXWh2.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Bitter';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8fbfOLfOXWh2.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Bitter';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8fbfOLbOXWh2.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Bitter';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8fbfOLjOXQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sourcesanspro/v22/6xK3dSBYKcSV-LCoeQqfX1RYOo3qNa7lqDY.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sourcesanspro/v22/6xK3dSBYKcSV-LCoeQqfX1RYOo3qPK7lqDY.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sourcesanspro/v22/6xK3dSBYKcSV-LCoeQqfX1RYOo3qNK7lqDY.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sourcesanspro/v22/6xK3dSBYKcSV-LCoeQqfX1RYOo3qO67lqDY.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sourcesanspro/v22/6xK3dSBYKcSV-LCoeQqfX1RYOo3qN67lqDY.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sourcesanspro/v22/6xK3dSBYKcSV-LCoeQqfX1RYOo3qNq7lqDY.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sourcesanspro/v22/6xK3dSBYKcSV-LCoeQqfX1RYOo3qOK7l.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
</style><style type="text/css"> #wsb-element-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9{top:-215px;left:46px;position:absolute;z-index:12}#wsb-element-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9{width:769px;height:98px}#wsb-element-00000000-0000-0000-0000-000401601928{top:-106px;left:858px;position:absolute;z-index:52}#wsb-element-00000000-0000-0000-0000-000401601928 .wsb-htmlsnippet-element{width:41px;height:40px;overflow:hidden;margin:auto}#wsb-element-00000000-0000-0000-0000-000394044459{top:48px;left:328px;position:absolute;z-index:47}#wsb-element-00000000-0000-0000-0000-000394044459 .wsb-image-inner{}#wsb-element-00000000-0000-0000-0000-000394044459 .wsb-image-inner div{width:98px;height:35px;position:relative;overflow:hidden}#wsb-element-00000000-0000-0000-0000-000394044459 img{position:absolute}#wsb-element-00000000-0000-0000-0000-000394044455{top:-246px;left:-5px;position:absolute;z-index:11}#wsb-element-00000000-0000-0000-0000-000394044455 .wsb-shape{width:914px;height:227px;-webkit-border-radius:10px;-moz-border-radius:10px;-o-border-radius:10px;border-radius:10px;padding:0px;background:#fff;-moz-opacity:0.97;-khtml-opacity:0.97;opacity:0.97;box-sizing:content-box;-moz-box-sizing:content-box}#wsb-element-00000000-0000-0000-0000-000394044440{top:-106px;left:46px;position:absolute;z-index:13}#wsb-element-00000000-0000-0000-0000-000394044440 .txt{width:822px;height:69px}#wsb-element-ab29dbdf-277c-4900-987d-23ba3cf7e3eb{top:13px;left:36px;position:absolute;z-index:92}#wsb-element-ab29dbdf-277c-4900-987d-23ba3cf7e3eb>div{width:835px;height:1214px;padding:0px}#wsb-element-a2c77403-b65e-45ed-ab8c-ee3b8916326c{top:1728px;left:337px;position:absolute;z-index:91}#wsb-element-a2c77403-b65e-45ed-ab8c-ee3b8916326c .wsb-button{width:225px;height:60px}#wsb-element-00000000-0000-0000-0000-000400752185{top:1852px;left:262px;position:absolute;z-index:51}#wsb-element-00000000-0000-0000-0000-000400752185 .wsb-image-inner{-webkit-border-radius:40px;-moz-border-radius:40px;-o-border-radius:40px;border-radius:40px;padding:0px;-moz-opacity:0.87;-khtml-opacity:0.87;opacity:0.87}#wsb-element-00000000-0000-0000-0000-000400752185 .wsb-image-inner div{width:402px;height:308px;position:relative;overflow:hidden}#wsb-element-00000000-0000-0000-0000-000400752185 img{position:absolute;-webkit-border-radius:40px;-moz-border-radius:40px;-o-border-radius:40px;border-radius:40px}#wsb-element-00000000-0000-0000-0000-000400564479{top:1457px;left:95px;position:absolute;z-index:50}#wsb-element-00000000-0000-0000-0000-000400564479 .txt{width:760px;height:80px}#wsb-element-00000000-0000-0000-0000-000400547105{top:1599px;left:289px;position:absolute;z-index:49}#wsb-element-00000000-0000-0000-0000-000400547105 .wsb-button{color:#fff;border:solid 1.11px #666;-webkit-border-radius:9px;-moz-border-radius:9px;-o-border-radius:9px;border-radius:9px;background:-webkit-gradient(linear,left top,left bottom,color-stop(0,red),color-stop(1,#c00000));background:-webkit-linear-gradient(top,red 0%,#c00000 100%);background:-moz-linear-gradient(top,red 0%,#c00000 100%);background:-o-linear-gradient(top,red 0%,#c00000 100%);background:-ms-linear-gradient(top,red 0%,#c00000 100%);background:linear-gradient(to bottom,red,#c00000);filter:progid:DXImageTransform.Microsoft.gradient(gradientType=0,startColorstr='#ffff0000',endColorstr='#ffc00000');-ms-filter:progid:DXImageTransform.Microsoft.gradient(gradientType=0,startColorStr='#ffff0000',endColorStr='#ffc00000');-moz-opacity:0.88;-khtml-opacity:0.88;opacity:0.88;width:329px;height:79px}#wsb-element-cd816cfe-ed60-439d-8dc9-9226081b914f{top:1327px;left:53px;position:absolute;z-index:97}#wsb-element-cd816cfe-ed60-439d-8dc9-9226081b914f .txt{width:802px;height:62px} </style><div class="wsb-canvas body"><div class="wsb-canvas-page-container" style="min-height: 100%; padding-top: 254px; position: relative;"><div class="wsb-canvas-scrollable" style="filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=&#39;#bf0000&#39;, endColorstr=&#39;#ffffff&#39;,GradientType=1 ); background-image: url(https://nebula.wsimg.com/7104c9f53a311b825478fb0ee132c3be?AccessKeyId=531592D248B589D87A56&amp;alloworigin=1); background: url(https://nebula.wsimg.com/7104c9f53a311b825478fb0ee132c3be?AccessKeyId=531592D248B589D87A56&amp;alloworigin=1), -moz-linear-gradient(left, #bf0000 0%, #ffffff 100%);; background: url(https://nebula.wsimg.com/7104c9f53a311b825478fb0ee132c3be?AccessKeyId=531592D248B589D87A56&amp;alloworigin=1), -webkit-gradient(linear, left top, right top, color-stop(0%,#bf0000), color-stop(100%,#ffffff)); background: url(https://nebula.wsimg.com/7104c9f53a311b825478fb0ee132c3be?AccessKeyId=531592D248B589D87A56&amp;alloworigin=1), -webkit-linear-gradient(left, #bf0000 0%, #ffffff 100%); background: url(https://nebula.wsimg.com/7104c9f53a311b825478fb0ee132c3be?AccessKeyId=531592D248B589D87A56&amp;alloworigin=1), -o-linear-gradient(left, #bf0000 0%,#ffffff 100%); background: url(https://nebula.wsimg.com/7104c9f53a311b825478fb0ee132c3be?AccessKeyId=531592D248B589D87A56&amp;alloworigin=1), -ms-linear-gradient(left, #bf0000 0%,#ffffff 100%); background: url(https://nebula.wsimg.com/7104c9f53a311b825478fb0ee132c3be?AccessKeyId=531592D248B589D87A56&amp;alloworigin=1), linear-gradient(to right, #bf0000 0%,#ffffff 100%); background-position-x: left; background-position-y: top; background-position: left top; background-repeat: repeat; position: absolute; width: 100%; height: 100%;"></div><div id="wsb-canvas-template-page" class="wsb-canvas-page page" style="height: 3285px; margin: auto; width: 903px; background-color: #ff8d02; position: relative; "><div id="wsb-canvas-template-container" style="position: absolute;"> <div id="wsb-element-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9" class="wsb-element-navigation" data-type="element"> <div style="width: 769px; height: 98px;" class="wsb-nav nav_theme nav-text-center nav-horizontal nav-btn-right wsb-navigation-rendered-top-level-container" id="wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9"><style> #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container ul > li > a {color:#0000ff;} #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container ul > li:hover, #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container ul > li:hover > a, #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container ul > li.active:hover, #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container ul > li.active > a:hover, #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container ul > li.active .nav-subnav li:hover, #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container ul > li.active .nav-subnav li:hover > a {background-color: !important;color:#ff0000 !important;} #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container > ul.wsb-navigation-rendered-top-level-menu > li.active, #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container > ul.wsb-navigation-rendered-top-level-menu > li.active > a {background-image:none;background-color:#ffff56;color:#bf0000;} </style><ul class="wsb-navigation-rendered-top-level-menu "><li style="width: auto"><a href="http://www.champions-sportsgrill.com/home.html" target="" data-title="Home" data-pageid="00000000-0000-0000-0000-000000026933" data-url="home.html">Home</a></li><li style="width: auto"><a href="http://www.champions-sportsgrill.com/photo-gallery.html" target="" data-title="Photo Gallery" data-pageid="00000000-0000-0000-0000-000000054257" data-url="photo-gallery.html">Photo Gallery</a></li><li style="width: auto" class="active"><a href="http://www.champions-sportsgrill.com/menu.html" target="" data-title="Menu" data-pageid="00000000-0000-0000-0000-000008564320" data-url="menu.html">Menu</a></li><li style="width: auto"><a href="http://www.champions-sportsgrill.com/location---contact.html" target="" data-title="Location &amp; Contact" data-pageid="00000000-0000-0000-0000-000000054270" data-url="location---contact.html">Location &amp; Contact</a></li><li style="width: auto"><a href="http://www.champions-sportsgrill.com/join-our-team.html" target="" data-title="Join Our Team" data-pageid="00000000-0000-0000-0000-000394047957" data-url="join-our-team.html">Join Our Team</a></li><li style="width: auto"><a href="http://www.champions-sportsgrill.com/feedback---comments.html" target="" data-title="Feedback &amp; Comments" data-pageid="00000000-0000-0000-0000-000394420685" data-url="feedback---comments.html">Feedback &amp; Comments</a></li><li style="width: auto"><a href="http://www.champions-sportsgrill.com/specials---coupons.html" target="" data-title="Specials &amp; Coupons" data-pageid="00000000-0000-0000-0000-000394443923" data-url="specials---coupons.html">Specials &amp; Coupons</a></li><li style="width: auto"><a href="http://www.champions-sportsgrill.com/happenings.html" target="" data-title="Happenings" data-pageid="00000000-0000-0000-0000-000394444207" data-url="happenings.html">Happenings</a></li><li style="width: auto"><a href="http://www.champions-sportsgrill.com/private-event.html" target="" data-title="Private Event" data-pageid="00000000-0000-0000-0000-000394858045" data-url="private-event.html">Private Event</a></li><li style="width: auto"><a href="http://www.champions-sportsgrill.com/donations--fundraisers----sponsorships.html" target="" data-title="Donations, Fundraisers, &amp; Sponsorships" data-pageid="bc305eb3-d88f-4eee-8d25-9a89e574ea91" data-url="donations--fundraisers----sponsorships.html">Donations, Fundraisers, &amp; Sponsorships</a></li></ul></div> </div><div id="wsb-element-00000000-0000-0000-0000-000401601928" class="wsb-element-htmlsnippet" data-type="element">




        <div class="wsb-htmlsnippet-element"><style>.ig-b- { display: inline-block; }
.ig-b- img { visibility: hidden; }
.ig-b-:hover { background-position: 0 -60px; } .ig-b-:active { background-position: 0 -120px; }
.ig-b-32 { width: 32px; height: 32px; background: url(//badges.instagram.com/static/images/ig-badge-sprite-32.png) no-repeat 0 0; }
@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min--moz-device-pixel-ratio: 2), only screen and (-o-min-device-pixel-ratio: 2 / 1), only screen and (min-device-pixel-ratio: 2), only screen and (min-resolution: 192dpi), only screen and (min-resolution: 2dppx) {
.ig-b-32 { background-image: url(//badges.instagram.com/static/images/<EMAIL>); background-size: 60px 178px; } }</style>
<a href="http://instagram.com/champions_sg?ref=badge" class="ig-b- ig-b-32"><img src="./Menu_files/ig-badge-32.png" alt="Instagram"></a></div>
</div><div id="wsb-element-00000000-0000-0000-0000-000394044455" class="wsb-element-shape" data-type="element"> <div class="wsb-shape shape_header customStyle shadow_drop_shadow"></div> </div><div id="wsb-element-00000000-0000-0000-0000-000394044440" class="wsb-element-text" data-type="element"> <div class="txt "><p style="text-align: center;"><span style="font-size:48px;"><span style="font-family:verdana,geneva,sans-serif;"><strong><span style="color:#cc0000;"><span style="line-height: 64px;">&nbsp;Champions Sports Grill&nbsp;</span></span></strong></span></span></p></div> </div><div id="wsb-element-ab29dbdf-277c-4900-987d-23ba3cf7e3eb" class="wsb-element-gallery" data-type="element"> <div class="false customStyle "><div id="desktop-ab29dbdf-277c-4900-987d-23ba3cf7e3eb" class="wsb-media-gallery" style="height: 1214px; width: 835px;"><div class="wsb-media-gallery-arrows-left-arrow" style="top: 585px;"></div><ul class="wsb-media-gallery-slider bordered" style="margin-left: 22px; width: 761px; height: 1184.4px;"><li class="" style="width: 761px; left: 0px; z-index: 93;"><img src="./Menu_files/0f5632eca641851b9740ec49d53ca16c" class="autosize" style="width: auto; height: 1184px; left: -74px; top: 0px;"></li><li class="" style="width: 761px; left: 761px;"><img src="./Menu_files/95b20187b1275ddb4ef922bffbc22b0a" class="autosize" style="width: 761px; height: auto; left: 0px; top: -35.5px;"></li><li class="" style="width: 761px; left: 761px;"><img src="./Menu_files/566d0a6ade0d7303f5bd46e5d19adb3b" class="autosize" style="width: 761px; height: auto; left: 0px; top: -31.5px;"></li><li class="loading" style="width: 761px; left: 761px;"></li><li class="" style="width: 761px; left: 761px;"><img src="./Menu_files/c3ce5ca1106fac202882896acb8eaf08" class="autosize" style="width: 761px; height: auto; left: 0px; top: -27px;"></li><li class="" style="width: 761px; left: 761px;"><img src="./Menu_files/8e7d7559a0e4016c026699952372444b" class="autosize" style="width: 761px; height: auto; left: 0px; top: -25px;"></li></ul><div class="wsb-media-gallery-arrows-right-arrow" style="top: 585px;"></div></div></div><script type="text/javascript"> require(['designer/app/builder/ui/controls/media/gallery/media.gallery'], function (gallery) { var $element = $('#desktop-ab29dbdf-277c-4900-987d-23ba3cf7e3eb.wsb-media-gallery'); var model = { ID: 'ab29dbdf-277c-4900-987d-23ba3cf7e3eb', mode: 'desktop', preview: false, Layer: 92, Width: '835px', Height: '1214px', GalleryAssets: [{"id":"0f5632eca641851b9740ec49d53ca16c:1549AF287DE5730D6508","src":"https://nebula.wsimg.com/0f5632eca641851b9740ec49d53ca16c?AccessKeyId=1549AF287DE5730D6508&disposition=0&alloworigin=1","caption":"","link":"","type":"image"},{"id":"95b20187b1275ddb4ef922bffbc22b0a:1549AF287DE5730D6508","src":"https://nebula.wsimg.com/95b20187b1275ddb4ef922bffbc22b0a?AccessKeyId=1549AF287DE5730D6508&disposition=0&alloworigin=1","caption":"","link":"","type":"image"},{"id":"566d0a6ade0d7303f5bd46e5d19adb3b:1549AF287DE5730D6508","src":"https://nebula.wsimg.com/566d0a6ade0d7303f5bd46e5d19adb3b?AccessKeyId=1549AF287DE5730D6508&disposition=0&alloworigin=1","caption":"","link":"","type":"image"},{"id":"fe3e13f617b53bed6f20b9d9a2e50ddd:1549AF287DE5730D6508","src":"https://nebula.wsimg.com/fe3e13f617b53bed6f20b9d9a2e50ddd?AccessKeyId=1549AF287DE5730D6508&disposition=0&alloworigin=1","caption":"","link":"","type":"image"},{"id":"c3ce5ca1106fac202882896acb8eaf08:1549AF287DE5730D6508","src":"https://nebula.wsimg.com/c3ce5ca1106fac202882896acb8eaf08?AccessKeyId=1549AF287DE5730D6508&disposition=0&alloworigin=1","caption":"","link":"","type":"image"},{"id":"8e7d7559a0e4016c026699952372444b:1549AF287DE5730D6508","src":"https://nebula.wsimg.com/8e7d7559a0e4016c026699952372444b?AccessKeyId=1549AF287DE5730D6508&disposition=0&alloworigin=1","caption":"","link":"","type":"image"}], GalleryAutoStart: true, GalleryCaption: true, GalleryAutoSize: true, GallerySpeed: 10, GalleryTheme: 0, GalleryTransition: 'Slide' }; gallery.render($element, model); }); </script> </div><div id="wsb-element-a2c77403-b65e-45ed-ab8c-ee3b8916326c" class="wsb-element-button" data-type="element"> <div><a id="wsb-button-a2c77403-b65e-45ed-ab8c-ee3b8916326c" class="wsb-button button " href="https://nebula.wsimg.com/d366f572029b7b8d83b4d3dbd51af6aa?AccessKeyId=1549AF287DE5730D6508&amp;disposition=0&amp;alloworigin=1" target="_blank"><span class="button-content wsb-button-content" style="white-space:nowrap">View / Print our Banquet Menu</span></a></div> </div><div id="wsb-element-00000000-0000-0000-0000-000400752185" class="wsb-element-image" data-type="element"> <div class="wsb-image-inner "><div class="customStyle"><img src="./Menu_files/546aa179c19a0abab771af1f358d4843" style="vertical-align:middle;width:402px;height:308px;"></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000400564479" class="wsb-element-text" data-type="element"> <div class="txt "><h1 style="text-align: center;">Please click on the button&nbsp;below to print our&nbsp;<br></h1><h1 style="text-align: center;">menu&nbsp;<span style="line-height: 1.1; background-color: rgba(0, 0, 0, 0);">offered at Champions.</span><br></h1><p style="text-align: center;"><em>(Prices and item selections&nbsp;are subject to change)</em></p></div> </div><div id="wsb-element-00000000-0000-0000-0000-000400547105" class="wsb-element-button" data-type="element"> <div><a id="wsb-button-00000000-0000-0000-0000-000400547105" class="wsb-button customStyle shadow_curved_horizontal" href="https://nebula.wsimg.com/c122e4b024507542b32f650f55cbf4f6?AccessKeyId=1549AF287DE5730D6508&amp;disposition=0&amp;alloworigin=1" target="_blank"><span class="button-content wsb-button-content" style="white-space:nowrap">View / Print Our General Menu</span></a></div> </div><div id="wsb-element-cd816cfe-ed60-439d-8dc9-9226081b914f" class="wsb-element-text" data-type="element"> <div class="txt "><p style="text-align: center;"><span style="font-size:20px;"><span style="color:#FF0000;"><strong></strong></span></span><span style="font-size:24px;"><span style="color:#FF0000;"><strong>DELIVERY AVAIALABLE</strong></span></span><span style="font-size:20px;"><span style="color:#FF0000;"><strong> :</strong></span></span>&nbsp;<span style="font-size:18px;">DOWNRIVER FOOD EXPRESS .... Call </span><strong><span style="font-size:20px;">************</span></strong><span style="font-size:18px;"> or download the app on your phones&nbsp; &nbsp;"</span><span style="font-size:20px;"><em><span style="color:#0000FF;">Five Star Food Express</span></em></span><span style="font-size:18px;">"&nbsp; or&nbsp;&nbsp;&nbsp;website&nbsp; </span><span style="font-size:20px;"><span style="color:#008000;"><em>www.downriverexpress.com</em></span></span><span style="font-size:18px;"></span></p></div> </div> </div></div><div id="wsb-canvas-template-footer" class="wsb-canvas-page-footer footer" style="margin: auto; min-height:100px; height: 100px; width: 903px; position: relative;"><div id="wsb-canvas-template-footer-container" class="footer-container" style="position: absolute"> <div id="wsb-element-00000000-0000-0000-0000-000394044459" class="wsb-element-image"> <div class="wsb-image-inner "><div class="img"><a href="https://www.godaddy.com/websites/website-builder?cvosrc=assets.wsb_badge.wsb_badge" rel=""><img src="./Menu_files/fab625f9c7d3d65638893f3418a0e7d9" style="vertical-align:middle;width:98px;height:35px;"></a></div></div> </div> </div></div><div class="view-as-mobile" style="padding:10px;position:relative;text-align:center;display:none;"><a href="http://www.champions-sportsgrill.com/menu.html#" onclick="return false;">View on Mobile</a></div></div></div><script type="text/javascript"> require(['jquery', 'common/cookiemanager/cookiemanager', 'designer/iebackground/iebackground'], function ($, cookieManager, bg) { if (cookieManager.getCookie("WSB.ForceDesktop")) { $('.view-as-mobile', '.wsb-canvas-page-container').show().find('a').bind('click', function () { cookieManager.eraseCookie("WSB.ForceDesktop"); window.location.reload(true); }); } bg.fixBackground(); }); </script><script> "undefined" === typeof _trfq || (window._trfq = []); "undefined" === typeof _trfd && (window._trfd = []), _trfd.push({ "ap": "WSBv7" }); </script><script src="./Menu_files/scc-c2.min.js.download" async=""></script> </body></html>