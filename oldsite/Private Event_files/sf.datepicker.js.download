/*! duel v2.5.8 - Built 2023-06-07, 3:14 PM MST - Copyright (c) 2023 */
define("starfield/sf.datepicker",["starfield/sf.base.modal","css!starfield/sf.datepicker.css","jq!starfield/jquery.mod","domReady!"],function(){return function(a){function b(a,b,c,d,e){this.i={},this.uid=function(){return this.uidd||(this.uidd=(new Date).getTime()),this.uidd++};var f=null;if("string"==typeof a)var g=d.ext.document().getElementById(a);else var g=a;g&&"object"==typeof g&&g.tagName&&"input"!=String(g.tagName).toLowerCase()&&(f=g),g=null,"object"==typeof a&&a.length||(a=[a]);for(var h=0;h<a.length;h++)"string"==typeof a[h]&&(a[h]=d.intr.document().getElementById(a[h])||null),null!=a[h]&&a[h].tagName&&"input"==String(a[h].tagName).toLowerCase()&&(this.i[this.uid()]=a[h]),a[h]=null;this.skin=b||"dhx_skyblue",this.setSkin=function(a){this.skin=a,this.base.className="dhtmlxcalendar_container dhtmlxcalendar_skin_"+this.skin},this.base=d.ext.document().createElement("DIV"),this.base.className="dhtmlxcalendar_container",this.base.style.display="none",null!=f?(this._hasParent=!0,f.appendChild(this.base),f=null):d.ext.body().appendChild(this.base),this.setParent=function(a){this._hasParent&&("object"==typeof a?a.appendChild(this.base):"string"==typeof a&&d.ext.document().getElementById(a).appendChild(this.base))},this.setSkin(this.skin),this.base.onclick=function(a){a=a||event,a.cancelBubble=!0},this.loadUserLanguage=function(a){if(this.langData[a]&&(this.lang=a,this.setWeekStartDay(this.langData[this.lang].weekstart),this.msCont))for(var b=0,c=0;c<this.msCont.childNodes.length;c++)for(var d=0;d<this.msCont.childNodes[c].childNodes.length;d++)this.msCont.childNodes[c].childNodes[d].innerHTML=this.langData[this.lang].monthesSNames[b++]},this.contMonth=d.ext.document().createElement("DIV"),this.contMonth.className="dhtmlxcalendar_month_cont",this.contMonth.onselectstart=function(a){return a=a||event,a.cancelBubble=!0,a.returnValue=!1,!1},this.base.appendChild(this.contMonth);var i=d.ext.document().createElement("UL");i.className="dhtmlxcalendar_line",this.contMonth.appendChild(i);var j=d.ext.document().createElement("LI");j.className="dhtmlxcalendar_cell dhtmlxcalendar_month_hdr",j.innerHTML="<div class='dhtmlxcalendar_month_arrow dhtmlxcalendar_month_arrow_left' onmouseover='this.className=\"dhtmlxcalendar_month_arrow dhtmlxcalendar_month_arrow_left_hover\";' onmouseout='this.className=\"dhtmlxcalendar_month_arrow dhtmlxcalendar_month_arrow_left\";'></div><span class='dhtmlxcalendar_month_label_month'>Month</span><span class='dhtmlxcalendar_month_label_year'>Year</span><div class='dhtmlxcalendar_month_arrow dhtmlxcalendar_month_arrow_right' onmouseover='this.className=\"dhtmlxcalendar_month_arrow dhtmlxcalendar_month_arrow_right_hover\";' onmouseout='this.className=\"dhtmlxcalendar_month_arrow dhtmlxcalendar_month_arrow_right\";'></div>",i.appendChild(j);var k=this;j.onclick=function(a){a=a||event;var b=a.target||a.srcElement;if(b.className&&0===b.className.indexOf("dhtmlxcalendar_month_arrow")){k._hideSelector();var c=b.parentNode.firstChild==b?-1:1;return void k._drawMonth(new Date(k._activeMonth.getFullYear(),k._activeMonth.getMonth()+c,1,0,0,0,0))}return b.className&&"dhtmlxcalendar_month_label_month"==b.className?(a.cancelBubble=!0,k._showSelector("month",31,21,"selector_month",!0),void k.callEvent("onShowMonthSelector",[b])):b.className&&"dhtmlxcalendar_month_label_year"==b.className?(a.cancelBubble=!0,k._showSelector("year",42,21,"selector_year",!0),void k.callEvent("onShowYearSelector",[b])):void k._hideSelector()},this.contDays=d.ext.document().createElement("DIV"),this.contDays.className="dhtmlxcalendar_days_cont",this.base.appendChild(this.contDays),this.setWeekStartDay=function(a){0==a&&(a=7),this._wStart=Math.min(Math.max(isNaN(a)?1:a,1),7),this._drawDaysOfWeek()},this._drawDaysOfWeek=function(){if(0==this.contDays.childNodes.length){var a=d.ext.document().createElement("UL");a.className="dhtmlxcalendar_line",this.contDays.appendChild(a)}else var a=this.contDays.firstChild;var b=this._wStart,c=this.langData[this.lang].daysSNames;c.push(String(this.langData[this.lang].daysSNames[0]).valueOf());for(var e=0;7>e;e++){if(null==a.childNodes[e]){var f=d.ext.document().createElement("LI");a.appendChild(f)}else var f=a.childNodes[e];f.className="dhtmlxcalendar_cell"+(b>=6?" dhtmlxcalendar_day_weekday_cell":"")+(0==e?"_first":""),f.innerHTML=c[b],++b>7&&(b=1)}null!=this._activeMonth&&this._drawMonth(this._activeMonth)},this._wStart=this.langData[this.lang].weekstart,this.setWeekStartDay(this._wStart),this.contDates=d.ext.document().createElement("DIV"),this.contDates.className="dhtmlxcalendar_dates_cont",this.base.appendChild(this.contDates),this.contDates.onclick=function(a){a=a||event;var b=a.target||a.srcElement;if(null!=b._date&&!b._css_dis){var c=k._activeDate.getHours(),d=k._activeDate.getMinutes();if(k.checkEvent("onBeforeChange")&&!k.callEvent("onBeforeChange",[new Date(b._date.getFullYear(),b._date.getMonth(),b._date.getDate(),c,d)]))return;null!=k._activeDateCell&&(k._activeDateCell._css_date=!1,k._updateCellStyle(k._activeDateCell._q,k._activeDateCell._w));var e=k._hasParent&&k._activeDate.getFullYear()+"_"+k._activeDate.getMonth()!=b._date.getFullYear()+"_"+b._date.getMonth();k._activeDate=new Date(b._date.getFullYear(),b._date.getMonth(),b._date.getDate(),c,d),k._activeDateCell=b,k._activeDateCell._css_date=!0,k._activeDateCell._css_hover=!1,k._lastHover=null,k._updateCellStyle(k._activeDateCell._q,k._activeDateCell._w),e&&k._drawMonth(k._activeDate),k._activeInp&&k.i[k._activeInp]&&(k.i[k._activeInp].value=k._dateToStr(new Date(k._activeDate.getTime()))),k._hasParent||k._hide(),k.callEvent("onClick",[new Date(k._activeDate.getTime())])}},this.contDates.onmouseover=function(a){a=a||event;var b=a.target||a.srcElement;null!=b._date&&(b._css_hover=!0,k._updateCellStyle(b._q,b._w),k._lastHover=b)},this.contDates.onmouseout=function(){k._clearDayHover()},this._lastHover=null,this._clearDayHover=function(){this._lastHover&&(this._lastHover._css_hover=!1,this._updateCellStyle(this._lastHover._q,this._lastHover._w),this._lastHover=null)};for(var h=0;6>h;h++){var i=d.ext.document().createElement("UL");i.className="dhtmlxcalendar_line",this.contDates.appendChild(i);for(var l=0;7>l;l++){var j=d.ext.document().createElement("LI");j.className="dhtmlxcalendar_cell",i.appendChild(j)}}this.contTime=d.ext.document().createElement("DIV"),this.contTime.className="dhtmlxcalendar_time_cont",this.base.appendChild(this.contTime),this.showTime=function(){String(this.base.className).search("dhtmlxcalendar_time_hidden")>0&&(this.base.className=String(this.base.className).replace(/dhtmlxcalendar_time_hidden/gi,""))},this.hideTime=function(){String(this.base.className).search("dhtmlxcalendar_time_hidden")<0&&(this.base.className+=" dhtmlxcalendar_time_hidden")};var i=d.ext.document().createElement("UL");i.className="dhtmlxcalendar_line",this.contTime.appendChild(i);var j=d.ext.document().createElement("LI");j.className="dhtmlxcalendar_cell dhtmlxcalendar_time_hdr",j.innerHTML="<div class='dhtmlxcalendar_time_label'></div><span class='dhtmlxcalendar_label_hours'></span><span class='dhtmlxcalendar_label_colon'>:</span><span class='dhtmlxcalendar_label_minutes'></span>",i.appendChild(j),j.onclick=function(a){a=a||event;var b=a.target||a.srcElement;return b.className&&"dhtmlxcalendar_label_hours"==b.className?(a.cancelBubble=!0,void k._showSelector("hours",3,115,"selector_hours",!0)):b.className&&"dhtmlxcalendar_label_minutes"==b.className?(a.cancelBubble=!0,void k._showSelector("minutes",59,115,"selector_minutes",!0)):void k._hideSelector()},this._activeMonth=null,this._activeDate=new Date,this._activeDateCell=null,this.setDate=function(a){""===a&&(a=new Date),a instanceof Date||(a=this._strToDate(a,!1)),"Invalid Date"===a&&(a=new Date);var b=a.getTime();this._isOutOfRange(b)||(this._activeDate=new Date(b),this._drawMonth(this._activeDate),this._updateVisibleHours(),this._updateVisibleMinutes())},this.getDate=function(a){var b=new Date(this._activeDate.getTime());return a?this._dateToStr(b):b},this._drawMonth=function(a){if(a instanceof Date){isNaN(a.getFullYear())&&(a=new Date(this._activeMonth.getFullYear(),this._activeMonth.getMonth(),1,0,0,0,0)),this._activeMonth=new Date(a.getFullYear(),a.getMonth(),1,0,0,0,0),this._activeDateCell=null;var b=new Date(this._activeMonth.getTime()),c=b.getDay(),d=c-this._wStart;0>d&&(d+=7),b.setDate(b.getDate()-d);for(var e=a.getMonth(),f=new Date(this._activeDate.getFullYear(),this._activeDate.getMonth(),this._activeDate.getDate(),0,0,0,0).getTime(),g=0,h=0;6>h;h++)for(var i=this._wStart,j=0;7>j;j++){var k=new Date(b.getFullYear(),b.getMonth(),b.getDate()+g++,0,0,0,0);this.contDates.childNodes[h].childNodes[j].innerHTML=k.getDate();var l=(k.getDay(),k.getTime());this.contDates.childNodes[h].childNodes[j]._date=new Date(l),this.contDates.childNodes[h].childNodes[j]._q=h,this.contDates.childNodes[h].childNodes[j]._w=j,this.contDates.childNodes[h].childNodes[j]._css_month=k.getMonth()==e,this.contDates.childNodes[h].childNodes[j]._css_date=l==f,this.contDates.childNodes[h].childNodes[j]._css_weekend=i>=6,this.contDates.childNodes[h].childNodes[j]._css_dis=this._isOutOfRange(l),this.contDates.childNodes[h].childNodes[j]._css_holiday=1==this._holidays[l],this._updateCellStyle(h,j),l==f&&(this._activeDateCell=this.contDates.childNodes[h].childNodes[j]),++i>7&&(i=1)}this.contMonth.firstChild.firstChild.childNodes[1].innerHTML=this.langData[this.lang].monthesFNames[a.getMonth()],this.contMonth.firstChild.firstChild.childNodes[2].innerHTML=a.getFullYear()}},this._updateCellStyle=function(a,b){var c=this.contDates.childNodes[a].childNodes[b],d="dhtmlxcalendar_cell dhtmlxcalendar_cell";d+=c._css_month?"_month":"",d+=c._css_date?"_date":"",d+=c._css_weekend?"_weekend":"",d+=c._css_holiday?"_holiday":"",d+=c._css_dis?"_dis":"",d+=c._css_hover&&!c._css_dis?"_hover":"",c.className=d,c=null},this._initSelector=function(a,b){if(this._selCover||(this._selCover=d.ext.document().createElement("DIV"),this._selCover.className="dhtmlxcalendar_selector_cover",this.base.appendChild(this._selCover)),this._sel||(this._sel=d.ext.document().createElement("DIV"),this._sel.className="dhtmlxcalendar_selector_obj",this.base.appendChild(this._sel),this._sel.appendChild(d.ext.document().createElement("TABLE")),this._sel.firstChild.className="dhtmlxcalendar_selector_table",this._sel.firstChild.cellSpacing=0,this._sel.firstChild.cellPadding=0,this._sel.firstChild.border=0,this._sel.firstChild.appendChild(d.ext.document().createElement("TBODY")),this._sel.firstChild.firstChild.appendChild(d.ext.document().createElement("TR")),this._sel.firstChild.firstChild.firstChild.appendChild(d.ext.document().createElement("TD")),this._sel.firstChild.firstChild.firstChild.appendChild(d.ext.document().createElement("TD")),this._sel.firstChild.firstChild.firstChild.appendChild(d.ext.document().createElement("TD")),this._sel.firstChild.firstChild.firstChild.childNodes[0].className="dhtmlxcalendar_selector_cell_left",this._sel.firstChild.firstChild.firstChild.childNodes[1].className="dhtmlxcalendar_selector_cell_middle",this._sel.firstChild.firstChild.firstChild.childNodes[2].className="dhtmlxcalendar_selector_cell_right",this._sel.firstChild.firstChild.firstChild.childNodes[0].innerHTML="&nbsp;",this._sel.firstChild.firstChild.firstChild.childNodes[2].innerHTML="&nbsp;",this._sel.firstChild.firstChild.firstChild.childNodes[0].onmouseover=function(){this.className="dhtmlxcalendar_selector_cell_left dhtmlxcalendar_selector_cell_left_hover"},this._sel.firstChild.firstChild.firstChild.childNodes[0].onmouseout=function(){this.className="dhtmlxcalendar_selector_cell_left"},this._sel.firstChild.firstChild.firstChild.childNodes[2].onmouseover=function(){this.className="dhtmlxcalendar_selector_cell_right dhtmlxcalendar_selector_cell_right_hover"},this._sel.firstChild.firstChild.firstChild.childNodes[2].onmouseout=function(){this.className="dhtmlxcalendar_selector_cell_right"},this._sel.firstChild.firstChild.firstChild.childNodes[0].onclick=function(a){a=a||event,a.cancelBubble=!0,k._scrollYears(-1)},this._sel.firstChild.firstChild.firstChild.childNodes[2].onclick=function(a){a=a||event,a.cancelBubble=!0,k._scrollYears(1)},this._sel._ta={},this._selHover=null,this._sel.onmouseover=function(a){a=a||event;var b=a.target||a.srcElement;b._cell===!0&&(k._selHover!=b&&k._clearSelHover(),null!=String(b.className).match(/^\s{0,}dhtmlxcalendar_selector_cell\s{0,}$/gi)&&(b.className+=" dhtmlxcalendar_selector_cell_hover",k._selHover=b))},this._sel.onmouseout=function(){k._clearSelHover()},this._sel.appendChild(d.ext.document().createElement("DIV")),this._sel.lastChild.className="dhtmlxcalendar_selector_obj_arrow"),1!=this._sel._ta[a]){if("month"==a){this._msCells={},this.msCont=d.ext.document().createElement("DIV"),this.msCont.className="dhtmlxcalendar_area_"+b,this._sel.firstChild.firstChild.firstChild.childNodes[1].appendChild(this.msCont);for(var c=0,e=0;4>e;e++){var f=d.ext.document().createElement("UL");f.className="dhtmlxcalendar_selector_line",this.msCont.appendChild(f);for(var g=0;3>g;g++){var h=d.ext.document().createElement("LI");h.innerHTML=this.langData[this.lang].monthesSNames[c],h.className="dhtmlxcalendar_selector_cell",f.appendChild(h),h._month=c,h._cell=!0,this._msCells[c++]=h}}this.msCont.onclick=function(a){a=a||event,a.cancelBubble=!0;var b=a.target||a.srcElement;null!=b._month&&(k._hideSelector(),k._updateActiveMonth(),k._drawMonth(new Date(k._activeMonth.getFullYear(),b._month,1,0,0,0,0)),k._doOnSelectorChange())}}if("year"==a){this._ysCells={},this.ysCont=d.ext.document().createElement("DIV"),this.ysCont.className="dhtmlxcalendar_area_"+b,this._sel.firstChild.firstChild.firstChild.childNodes[1].appendChild(this.ysCont);for(var e=0;4>e;e++){var f=d.ext.document().createElement("UL");f.className="dhtmlxcalendar_selector_line",this.ysCont.appendChild(f);for(var g=0;3>g;g++){var h=d.ext.document().createElement("LI");h.className="dhtmlxcalendar_selector_cell",h._cell=!0,f.appendChild(h)}}this.ysCont.onclick=function(a){a=a||event,a.cancelBubble=!0;var b=a.target||a.srcElement;null!=b._year&&(k._hideSelector(),k._drawMonth(new Date(b._year,k._activeMonth.getMonth(),1,0,0,0,0)),k._doOnSelectorChange(),k.callEvent("onYearSelected",[b]))}}if("hours"==a){this._hsCells={},this.hsCont=d.ext.document().createElement("DIV"),this.hsCont.className="dhtmlxcalendar_area_"+b,this._sel.firstChild.firstChild.firstChild.childNodes[1].appendChild(this.hsCont);for(var c=0,e=0;4>e;e++){var f=d.ext.document().createElement("UL");f.className="dhtmlxcalendar_selector_line",this.hsCont.appendChild(f);for(var g=0;6>g;g++){var h=d.ext.document().createElement("LI");h.innerHTML=this._fixLength(c,2),h.className="dhtmlxcalendar_selector_cell",f.appendChild(h),h._hours=c,h._cell=!0,this._hsCells[c++]=h}}this.hsCont.onclick=function(a){a=a||event,a.cancelBubble=!0;var b=a.target||a.srcElement;null!=b._hours&&(k._hideSelector(),k._activeDate.setHours(b._hours),k._updateActiveHours(),k._updateVisibleHours(),k._doOnSelectorChange())}}if("minutes"==a){this._rsCells={},this.rsCont=d.ext.document().createElement("DIV"),this.rsCont.className="dhtmlxcalendar_area_"+b,this._sel.firstChild.firstChild.firstChild.childNodes[1].appendChild(this.rsCont);for(var c=0,e=0;4>e;e++){var f=d.ext.document().createElement("UL");f.className="dhtmlxcalendar_selector_line",this.rsCont.appendChild(f);for(var g=0;3>g;g++){var h=d.ext.document().createElement("LI");h.innerHTML=this._fixLength(c,2),h.className="dhtmlxcalendar_selector_cell",f.appendChild(h),h._minutes=c,h._cell=!0,this._rsCells[c]=h,c+=5}}this.rsCont.onclick=function(a){a=a||event,a.cancelBubble=!0;var b=a.target||a.srcElement;null!=b._minutes&&(k._hideSelector(),k._activeDate.setMinutes(b._minutes),k._updateActiveMinutes(),k._updateVisibleMinutes(),k._doOnSelectorChange())}}this._sel._ta[a]=!0}},this._showSelector=function(a,b,c,d,e){return e===!0&&null!=this._sel&&this._isSelectorVisible()&&a==this._sel._t?void this._hideSelector():(this._sel&&this._sel._ta[a]||this._initSelector(a,d),this._selCover.style.display="",this._sel._t=a,this._sel.style.left=b+"px",this._sel.style.top=c+"px",this._sel.style.display="",this._sel.className="dhtmlxcalendar_selector_obj dhtmlxcalendar_"+d,void this._doOnSelectorShow(a))},this._doOnSelectorShow=function(a){"month"==a&&this._updateActiveMonth(),"year"==a&&this._updateYearsList(this._activeMonth),"hours"==a&&this._updateActiveHours(),"minutes"==a&&this._updateActiveMinutes()},this._hideSelector=function(){this._sel&&(this._sel.style.display="none",this._selCover.style.display="none",this.callEvent("onSelectorHide",[]))},this._isSelectorVisible=function(){return this._sel?"none"!=this._sel.style.display:!1},this._doOnSelectorChange=function(a){this.callEvent("onChange",[new Date(this._activeMonth.getFullYear(),this._activeMonth.getMonth(),this._activeDate.getDate(),this._activeDate.getHours(),this._activeDate.getMinutes(),this._activeDate.getSeconds()),a])},this._clearSelHover=function(){this._selHover&&(this._selHover.className=String(this._selHover.className.replace(/dhtmlxcalendar_selector_cell_hover/gi,"")),this._selHover=null)},this._updateActiveMonth=function(){"undefined"!=typeof this._msActive&&"undefined"!=typeof this._msCells[this._msActive]&&(this._msCells[this._msActive].className="dhtmlxcalendar_selector_cell"),this._msActive=this._activeMonth.getMonth(),this._msCells[this._msActive].className="dhtmlxcalendar_selector_cell dhtmlxcalendar_selector_cell_active"},this._updateActiveYear=function(){var a=this._activeMonth.getFullYear();this._ysCells[a]&&(this._ysCells[a].className="dhtmlxcalendar_selector_cell dhtmlxcalendar_selector_cell_active")},this._updateYearsList=function(a){for(var b in this._ysCells)this._ysCells[b]=null,delete this._ysCells[b];for(var c=12*Math.floor(a.getFullYear()/12),d=0;4>d;d++)for(var e=0;3>e;e++)this.ysCont.childNodes[d].childNodes[e].innerHTML=c,this.ysCont.childNodes[d].childNodes[e]._year=c,this.ysCont.childNodes[d].childNodes[e].className="dhtmlxcalendar_selector_cell",this._ysCells[c++]=this.ysCont.childNodes[d].childNodes[e];this._updateActiveYear()},this._scrollYears=function(a){var b=(0>a?this.ysCont.firstChild.firstChild._year:this.ysCont.lastChild.lastChild._year)+a,c=new Date(b,this._activeMonth.getMonth(),1,0,0,0,0);this._updateYearsList(c)},this._updateActiveHours=function(){"undefined"!=typeof this._hsActive&&"undefined"!=typeof this._hsCells[this._hsActive]&&(this._hsCells[this._hsActive].className="dhtmlxcalendar_selector_cell"),this._hsActive=this._activeDate.getHours(),this._hsCells[this._hsActive].className="dhtmlxcalendar_selector_cell dhtmlxcalendar_selector_cell_active"},this._updateVisibleHours=function(){this.contTime.firstChild.firstChild.childNodes[1].innerHTML=this._fixLength(this._activeDate.getHours(),2)},this._updateActiveMinutes=function(){"undefined"!=typeof this._rsActive&&"undefined"!=typeof this._rsCells[this._rsActive]&&(this._rsCells[this._rsActive].className="dhtmlxcalendar_selector_cell"),this._rsActive=this._activeDate.getMinutes(),"undefined"!=typeof this._rsCells[this._rsActive]&&(this._rsCells[this._rsActive].className="dhtmlxcalendar_selector_cell dhtmlxcalendar_selector_cell_active")},this._updateVisibleMinutes=function(){this.contTime.firstChild.firstChild.childNodes[3].innerHTML=this._fixLength(this._activeDate.getMinutes(),2)},this._fixLength=function(a,b){for(;String(a).length<b;)a="0"+String(a);return a},this._dateFormat="",this._dateFormatRE=null,this.setDateFormat=function(a){this._dateFormat=a,this._dateFormatRE=new RegExp(String(this._dateFormat).replace(/%[a-zA-Z]+/g,function(a){var b=a.replace(/%/,"");switch(b){case"m":case"d":case"H":case"i":case"s":return"\\d{2}";case"Y":return"\\d{4}"}return a}))},this._strToDate=function(a,b,c){var d={Y:!1,m:!1,d:!1,H:!1,i:!1,s:!1},e=String(a).match(/[0-9]{1,}/g),f=(c||this._dateFormat).match(/%[a-zA-Z]/g);if(!e)return"Invalid Date";for(var g=0;g<f.length;g++){var h=f[g].replace(/%/g,"");"undefined"!=typeof d[h]&&(d[h]=Number(e[g]))}if(b)return d;for(var e in d)d[e]===!1&&(d[e]=0);return new Date(d.Y,d.m-1,d.d,d.H,d.i,d.s,0)},this._dateToStr=function(a,b){if(a instanceof Date)var c=function(a){return 1==String(a).length?"0"+String(a):a},d=function(b){switch(b){case"%d":return c(a.getDate());case"%j":return a.getDate();case"%D":return k.langData[k.lang].daysSNames[a.getDay()];case"%l":return k.langData[k.lang].daysFNames[a.getDay()];case"%m":return c(a.getMonth()+1);case"%n":return date.getMonth()+1;case"%M":return k.langData[k.lang].monthesSNames[a.getMonth()];case"%F":return k.langData[k.lang].monthesFNames[a.getMonth()];case"%y":return c(a.getYear()%100);case"%Y":return a.getFullYear();case"%g":return(a.getHours()+11)%12+1;case"%h":return c((a.getHours()+11)%12+1);case"%G":return a.getHours();case"%H":return c(a.getHours());case"%i":return c(a.getMinutes());case"%s":return c(a.getSeconds());case"%a":return a.getHours()>11?"pm":"am";case"%A":return a.getHours()>11?"PM":"AM";case"%%":default:return b}},e=String(b||this._dateFormat).replace(/%[a-zA-Z]/g,d);return e||String(a)},this._updateDateStr=function(a){if(""==a)return this.setDate(new Date),void this.callEvent("onChange",[null,!0]);if(this._dateFormatRE&&a.match(this._dateFormatRE)){var b=this._strToDate(a,!0),c=new Date(this._activeMonth.getFullYear(),this._activeMonth.getMonth(),this._activeDate.getDate(),this._activeDate.getHours(),this._activeDate.getMinutes(),this._activeDate.getSeconds());b.Y!==!1&&b.Y!=c.getFullYear()&&this._activeDate.setFullYear(b.Y),b.m!==!1&&(b.m--,b.m!=c.getMonth()&&this._activeDate.setMonth(b.m)),b.d!==!1&&b.d!=c.getDate()&&this._activeDate.setDate(b.d),b.H!==!1&&b.H!=c.getHours()&&this._activeDate.setHours(b.H),b.i!==!1&&b.i!=c.getMinutes()&&this._activeDate.setMinutes(b.i),b.s!==!1&&b.s!=c.getSeconds()&&this._activeDate.setSeconds(b.s),this._drawMonth(this._activeDate),this._updateVisibleMinutes(),this._updateVisibleHours(),this._sel&&this._isSelectorVisible()&&this._doOnSelectorShow(this._sel._t),this._doOnSelectorChange(!0)}},this.setFormatedDate=function(a,b,c,d){var e=this._strToDate(b,!1,a);return d?e:void this.setDate(e)},this.getFormatedDate=function(a,b){return b&&b instanceof Date||(b=new Date(this._activeDate)),this._dateToStr(b,a)},this.show=function(a){if(!a&&this._hasParent)return void this._show();if("object"==typeof a&&"undefined"!=typeof a._dhtmlxcalendar_uid&&this.i[a._dhtmlxcalendar_uid]==a)return void this._show(a._dhtmlxcalendar_uid);if("undefined"==typeof a)for(var b in this.i)a||(a=b);a&&this._show(a)},this.hide=function(){this._isVisible()&&this._hide()},this.isVisible=function(){return this._isVisible()},this.draw=function(){this.show()},this.close=function(){this.hide()},this._activeInp=null,this.pos="bottom",this.setPosition=function(a,b){return"right"==a||"bottom"==a?void(this.pos=a):void(this._hasParent||("undefined"==typeof a||isNaN(a)||(this.base.style.left=a+"px"),"undefined"==typeof b||isNaN(b)||(this.base.style.top=b+"px")))},this._show=function(a,b){return b===!0&&this._activeInp==a&&this._isVisible()?void this._hide():(a?("right"==this.pos?(this.base.style.left=this._getLeft(this.i[a])+this.i[a].offsetWidth-1+"px",this.base.style.top=this._getTop(this.i[a])+"px"):(this.base.style.left=this._getLeft(this.i[a])+"px",this.base.style.top=this._getTop(this.i[a])+this.i[a].offsetHeight-1+"px"),this._activeInp=a):(this.base.style.left="0px",this.base.style.top="0px"),this._hideSelector(),void(this.base.style.display=""))},this._hide=function(){this._hideSelector(),this.base.style.display="none",this._activeInp=null},this._isVisible=function(){return"none"!=this.base.style.display},this._getLeft=function(a){return this._posGetOffset(a).left},this._getTop=function(a){return this._posGetOffset(a).top},this._posGetOffsetSum=function(a){for(var b=0,c=0;a;)b+=parseInt(a.offsetTop),c+=parseInt(a.offsetLeft),a=a.offsetParent;return{top:b,left:c}},this._posGetOffsetRect=function(a){var b=a.getBoundingClientRect(),c=d.ext.body(),e=d.ext.document().documentElement,f={left:0,top:0};d.isSingle()||(f=d.ext.iframe().offset());var g=d.ext.window().pageYOffset||e.scrollTop||c.scrollTop,h=d.ext.window().pageXOffset||e.scrollLeft||c.scrollLeft,i=e.clientTop||c.clientTop||0,j=e.clientLeft||c.clientLeft||0,k=b.top+f.top+g-i,l=b.left+f.left+h-j;return{top:Math.round(k),left:Math.round(l)}},this._posGetOffset=function(a){return this[a.getBoundingClientRect?"_posGetOffsetRect":"_posGetOffsetSum"](a)},this._rangeActive=!1,this._rangeFrom=null,this._rangeTo=null,this._rangeSet={},this.setInsensitiveDays=function(a){for(var b=this._extractDates(a),c=0;c<b.length;c++)this._rangeSet[new Date(b[c].getFullYear(),b[c].getMonth(),b[c].getDate(),0,0,0,0).getTime()]=!0;this._drawMonth(this._activeDate)},this.clearInsensitiveDays=function(){this._clearRangeSet(),this._drawMonth(this._activeDate)},this._holidays={},this.setHolidays=function(a){if(null==a)this._clearHolidays();else if(null!=a)for(var b=this._extractDates(a),c=0;c<b.length;c++)this._holidays[new Date(b[c].getFullYear(),b[c].getMonth(),b[c].getDate(),0,0,0,0).getTime()]=!0;this._drawMonth(this._activeDate)},this._extractDates=function(a){("string"==typeof a||a instanceof Date)&&(a=[a]);for(var b=[],c=0;c<a.length;c++)if("string"==typeof a[c])for(var d=a[c].split(","),e=0;e<d.length;e++)b.push(this._strToDate(d[e],!1));else a[c]instanceof Date&&b.push(a[c]);return b},this._clearRange=function(){this._rangeActive=!1,this._rangeType=null,this._rangeFrom=null,this._rangeTo=null},this._clearRangeSet=function(){for(var a in this._rangeSet)this._rangeSet[a]=null,delete this._rangeSet[a]},this._clearHolidays=function(){for(var a in this._holidays)this._holidays[a]=null,delete this._holidays[a]},this._isOutOfRange=function(a){if(1==this._rangeSet[a])return!0;if(this._rangeActive){if("in"==this._rangeType&&(a<this._rangeFrom||a>this._rangeTo))return!0;if("out"==this._rangeType&&a>=this._rangeFrom&&a<=this._rangeTo)return!0;if("from"==this._rangeType&&a<this._rangeFrom)return!0;if("to"==this._rangeType&&a>this._rangeTo)return!0}return!1},this.clearSensitiveRange=function(){this._clearRange(),this._drawMonth(this._activeDate)},this.setSensitiveRange=function(a,b,c){var d=!1;if(null!=a&&null!=b){if(a instanceof Date||(a=this._strToDate(a,!1)),b instanceof Date||(b=this._strToDate(b,!1)),a.getTime()>b.getTime())return;this._rangeFrom=new Date(a.getFullYear(),a.getMonth(),a.getDate(),0,0,0,0).getTime(),this._rangeTo=new Date(b.getFullYear(),b.getMonth(),b.getDate(),0,0,0,0).getTime(),this._rangeActive=!0,this._rangeType="in",d=!0}d||null==a||null!=b||(a instanceof Date||(a=this._strToDate(a,!1)),this._rangeFrom=new Date(a.getFullYear(),a.getMonth(),a.getDate(),0,0,0,0).getTime(),this._rangeTo=null,c===!0&&this._rangeFrom++,this._rangeActive=!0,this._rangeType="from",d=!0),d||null!=a||null==b||(b instanceof Date||(b=this._strToDate(b,!1)),this._rangeFrom=null,this._rangeTo=new Date(b.getFullYear(),b.getMonth(),b.getDate(),0,0,0,0).getTime(),c===!0&&this._rangeTo--,this._rangeActive=!0,this._rangeType="to",d=!0),d&&this._drawMonth(this._activeDate)},this.setInsensitiveRange=function(a,b){if(null!=a&&null!=b){if(a instanceof Date||(a=this._strToDate(a,!1)),b instanceof Date||(b=this._strToDate(b,!1)),a.getTime()>b.getTime())return;return this._rangeFrom=new Date(a.getFullYear(),a.getMonth(),a.getDate(),0,0,0,0).getTime(),this._rangeTo=new Date(b.getFullYear(),b.getMonth(),b.getDate(),0,0,0,0).getTime(),this._rangeActive=!0,this._rangeType="out",void this._drawMonth(this._activeDate)}return null!=a&&null==b?void this.setSensitiveRange(null,a,!0):null==a&&null!=b?void this.setSensitiveRange(b,null,!0):void 0},this._doOnClick=function(a){a=a||event;var b=a.target||a.srcElement;return b._dhtmlxcalendar_uid&&b._dhtmlxcalendar_uid!=k._activeInp&&k._isVisible()?void k._hide():void(b._dhtmlxcalendar_uid&&k.i[b._dhtmlxcalendar_uid]||(k._isSelectorVisible()?k._hideSelector():!k._hasParent&&k._isVisible()&&k._hide()))},this._doOnKeyDown=function(a){a=a||event,27==a.keyCode&&(k._isSelectorVisible()?k._hideSelector():k._isVisible()&&!k._hasParent&&k._hide())},this._doOnInpClick=function(a){a=a||event;var b=a.target||a.srcElement;b._dhtmlxcalendar_uid&&(k._updateDateStr(b.value),k._show(b._dhtmlxcalendar_uid,!0))},this._doOnInpKeyUp=function(a){a=a||event;var b=a.target||a.srcElement;13!=a.keyCode&&b._dhtmlxcalendar_uid&&k._updateDateStr(b.value)},this._doOnUnload=function(){"undefined"!=typeof k&&null!==k&&k.unload()},d.ext.window().addEventListener?(d.ext.body().addEventListener("click",k._doOnClick,!1),d.ext.window().addEventListener("keydown",k._doOnKeyDown,!1),d.ext.window().addEventListener("unload",k._doOnUnload,!1)):(d.ext.body().attachEvent("onclick",k._doOnClick),d.ext.body().attachEvent("onkeydown",k._doOnKeyDown),d.ext.window().attachEvent("onunload",k._doOnUnload)),this.attachObj=function(a){var b=this.uid();this.i[b]=a,this._attachEventsToObject(b)},this.detachObj=function(a){var b=a._dhtmlxcalendar_uid;null!=this.i[b]&&(this._detachEventsFromObject(b),this.i[b]._dhtmlxcalendar_uid=null,this.i[b]=null,delete this.i[b])},this._attachEventsToObject=function(a){this.i[a]._dhtmlxcalendar_uid=a,d.ext.window().addEventListener?(this.i[a].addEventListener("click",k._doOnInpClick,!1),this.i[a].addEventListener("keyup",k._doOnInpKeyUp,!1)):(this.i[a].attachEvent("onclick",k._doOnInpClick),this.i[a].attachEvent("onkeyup",k._doOnInpKeyUp))},this._detachEventsFromObject=function(a){d.ext.window().addEventListener?(this.i[a].removeEventListener("click",k._doOnInpClick,!1),this.i[a].removeEventListener("keyup",k._doOnInpKeyUp,!1)):(this.i[a].detachEvent("onclick",k._doOnInpClick),this.i[a].detachEvent("onkeyup",k._doOnInpKeyUp))};for(var m in this.i)this._attachEventsToObject(m);return this.evs={},this.attachEvent=function(a,b){var c=this.uid();return this.evs[c]={name:String(a).toLowerCase(),func:b},c},this.detachEvent=function(a){this.evs[a]&&(this.evs[a].name=null,this.evs[a].func=null,this.evs[a]=null,delete this.evs[a])},this.callEvent=function(a,b){var c=!0,d=String(a).toLowerCase();b=b||[];for(var e in this.evs)if(this.evs[e].name==d){var f=this.evs[e].func.apply(this,b);c=c&&f}return c},this.checkEvent=function(a){var b=!1,c=String(a).toLowerCase();for(var d in this.evs)b=b||this.evs[d].name==c;return b},this.unload=function(){d.ext.window().addEventListener?(d.ext.body().removeEventListener("click",k._doOnClick,!1),d.ext.window().removeEventListener("keydown",k._doOnKeyDown,!1),d.ext.window().removeEventListener("unload",k._doOnUnload,!1)):(d.ext.body().detachEvent("onclick",k._doOnClick),d.ext.body().detachEvent("onkeydown",k._doOnKeyDown),d.ext.window().detachEvent("onunload",k._doOnKeyDown)),this._doOnClick=null,this._doOnKeyDown=null,this._doOnUnload=null,this._activeDate=null,this._activeDateCell=null,this._activeInp=null,this._activeMonth=null,this._dateFormat=null,this._dateFormatRE=null,this._lastHover=null,this.uid=null,this.uidd=null;for(var a in this.i)this.i[a]._dhtmlxcalendar_uid=null,delete this.i[a]._dhtmlxcalendar_uid,d.ext.window().addEventListener?(this.i[a].removeEventListener("click",k._doOnInpClick,!1),this.i[a].removeEventListener("keyup",k._doOnInpKeyUp,!1)):(this.i[a].detachEvent("onclick",k._doOnInpClick),this.i[a].detachEvent("onkeyup",k._doOnInpKeyUp)),this.i[a]=null,delete this.i[a];this.i=null,this._doOnInpClick=null,this._doOnInpKeyUp=null;for(var a in this.evs)this.detachEvent(a);for(this.evs=null,this.attachEvent=null,this.detachEvent=null,this.checkEvent=null,this.callEvent=null,this.contMonth.onselectstart=null,this.contMonth.firstChild.firstChild.onclick=null,this.contMonth.firstChild.firstChild.firstChild.onmouseover=null,this.contMonth.firstChild.firstChild.firstChild.onmouseout=null,this.contMonth.firstChild.firstChild.lastChild.onmouseover=null,this.contMonth.firstChild.firstChild.lastChild.onmouseout=null;this.contMonth.firstChild.firstChild.childNodes.length>0;)this.contMonth.firstChild.firstChild.removeChild(this.contMonth.firstChild.firstChild.lastChild);
for(this.contMonth.firstChild.removeChild(this.contMonth.firstChild.firstChild),this.contMonth.removeChild(this.contMonth.firstChild),this.contMonth.parentNode.removeChild(this.contMonth),this.contMonth=null;this.contDays.firstChild.childNodes.length>0;)this.contDays.firstChild.removeChild(this.contDays.firstChild.lastChild);for(this.contDays.removeChild(this.contDays.firstChild),this.contDays.parentNode.removeChild(this.contDays),this.contDays=null,this.contDates.onclick=null,this.contDates.onmouseover=null,this.contDates.onmouseout=null;this.contDates.childNodes.length>0;){for(;this.contDates.lastChild.childNodes.length>0;)this.contDates.lastChild.lastChild._css_date=null,this.contDates.lastChild.lastChild._css_month=null,this.contDates.lastChild.lastChild._css_weekend=null,this.contDates.lastChild.lastChild._css_hover=null,this.contDates.lastChild.lastChild._date=null,this.contDates.lastChild.lastChild._q=null,this.contDates.lastChild.lastChild._w=null,this.contDates.lastChild.removeChild(this.contDates.lastChild.lastChild);this.contDates.removeChild(this.contDates.lastChild)}for(this.contDates.parentNode.removeChild(this.contDates),this.contDates=null,this.contTime.firstChild.firstChild.onclick=null;this.contTime.firstChild.firstChild.childNodes.length>0;)this.contTime.firstChild.firstChild.removeChild(this.contTime.firstChild.firstChild.lastChild);if(this.contTime.firstChild.removeChild(this.contTime.firstChild.firstChild),this.contTime.removeChild(this.contTime.firstChild),this.contTime.parentNode.removeChild(this.contTime),this.contTime=null,this._lastHover=null,this.msCont){this.msCont.onclick=null,this._msActive=null;for(var a in this._msCells)this._msCells[a]._cell=null,this._msCells[a]._month=null,this._msCells[a].parentNode.removeChild(this._msCells[a]),this._msCells[a]=null;for(this._msCells=null;this.msCont.childNodes.length>0;)this.msCont.removeChild(this.msCont.lastChild);this.msCont.parentNode.removeChild(this.msCont),this.msCont=null}if(this.ysCont){this.ysCont.onclick=null;for(var a in this._ysCells)this._ysCells[a]._cell=null,this._ysCells[a]._year=null,this._ysCells[a].parentNode.removeChild(this._ysCells[a]),this._ysCells[a]=null;for(this._ysCells=null;this.ysCont.childNodes.length>0;)this.ysCont.removeChild(this.ysCont.lastChild);this.ysCont.parentNode.removeChild(this.ysCont),this.ysCont=null}if(this.hsCont){this.hsCont.onclick=null,this._hsActive=null;for(var a in this._hsCells)this._hsCells[a]._cell=null,this._hsCells[a]._hours=null,this._hsCells[a].parentNode.removeChild(this._hsCells[a]),this._hsCells[a]=null;for(this._hsCells=null;this.hsCont.childNodes.length>0;)this.hsCont.removeChild(this.hsCont.lastChild);this.hsCont.parentNode.removeChild(this.hsCont),this.hsCont=null}if(this.rsCont){this.rsCont.onclick=null,this._rsActive=null;for(var a in this._rsCells)this._rsCells[a]._cell=null,this._rsCells[a]._minutes=null,this._rsCells[a].parentNode.removeChild(this._rsCells[a]),this._rsCells[a]=null;for(this._rsCells=null;this.rsCont.childNodes.length>0;)this.rsCont.removeChild(this.rsCont.lastChild);this.rsCont.parentNode.removeChild(this.rsCont),this.rsCont=null}if(this._selCover&&(this._selCover.parentNode.removeChild(this._selCover),this._selCover=null),this._sel){for(var a in this._sel._ta)this._sel._ta[a]=null;for(this._sel._ta=null,this._sel._t=null,this._sel.onmouseover=null,this._sel.onmouseout=null;this._sel.firstChild.firstChild.firstChild.childNodes.length>0;)this._sel.firstChild.firstChild.firstChild.lastChild.onclick=null,this._sel.firstChild.firstChild.firstChild.lastChild.onmouseover=null,this._sel.firstChild.firstChild.firstChild.lastChild.onmouseout=null,this._sel.firstChild.firstChild.firstChild.removeChild(this._sel.firstChild.firstChild.firstChild.lastChild);for(this._sel.firstChild.firstChild.removeChild(this._sel.firstChild.firstChild.firstChild),this._sel.firstChild.removeChild(this._sel.firstChild.firstChild);this._sel.childNodes.length>0;)this._sel.removeChild(this._sel.lastChild);this._sel.parentNode.removeChild(this._sel),this._sel=null}this.base.onclick=null,this.base.parentNode.removeChild(this.base),this.base=null,this._clearDayHover=null,this._clearSelHover=null,this._doOnSelectorChange=null,this._doOnSelectorShow=null,this._drawMonth=null,this._fixLength=null,this._getLeft=null,this._getTop=null,this._hide=null,this._hideSelector=null,this._initSelector=null,this._isSelectorVisible=null,this._isVisible=null,this._posGetOffset=null,this._posGetOffsetRect=null,this._posGetOffsetSum=null,this._scrollYears=null,this._show=null,this._showSelector=null,this._strToDate=null,this._updateActiveHours=null,this._updateActiveMinutes=null,this._updateActiveMonth=null,this._updateActiveYear=null,this._updateCellStyle=null,this._updateDateStr=null,this._updateVisibleHours=null,this._updateVisibleMinutes=null,this._updateYearsList=null,this.hide=null,this.hideTime=null,this.setDate=null,this.setDateFormat=null,this.show=null,this.showTime=null,this.unload=null;for(var a in this)delete this[a];a=k=null},this.setDate(this._activeDate),this}var c=a(".sf-datepicker").size(),d=a({}),e=a.fn.sfDatePicker=function(b,f){"string"==typeof b?this.each(function(){"function"==typeof this[b]&&this[b](f)}):(me=a(this),"object"==typeof b&&"undefined"!=typeof b.wireup&&(me=a(".sf-datepicker")),"object"==typeof b&&"undefined"!=typeof b.rangeGroup&&(a(this).attr("data-range-group",b.rangeGroup),a(this).attr("data-range-position",b.rangePosition)),a(me[0]).hasClass("sf-datepicker")||(c+=me.length),me.each(function(){var f=a(this),g=f.attr("data-min-date");"string"==typeof g&&(g=new Date(g));var h=f.attr("data-max-date");"string"==typeof h&&(h=new Date(h));var i=a.extend({prefix:"dp-style",zIndex:void 0,dateFormat:f.attr("data-date-format")||"%m/%d/%Y",dateOverride:f.attr("data-date-override")||null,rangeGroup:f.attr("data-range-group")||null,beforeToday:"true"===f.attr("data-before-today")||!1,minDate:g,maxDate:h,onSelect:function(){},onChange:function(){},afterInit:function(){},context:window,displayContext:window,zAnchor:null},b),j=($sf.util.zIndex,null),k=$sf.util.context(i.context,i.displayContext);"undefined"==typeof e.instances&&(e.instances=[]),e.instances.push(f),a.extend(this,{init:function(){$el=a(this),$el.addClass("sf-datepicker");var b={width:$el.outerWidth(),height:$el.outerHeight()},e=($el.offset(),""+i.prefix+Math.round(65535*Math.random()));j=$sf.util.logger(e,"duel");var f=a('<a href="#" class="trigger"><span></span></a>'),g=a('<span id="'+e+'"></span>'),h=a("<span />"),l=$sf.util.zIndex.getLayer("flyout",a(k.ext.body()),k.isSingle()?$el:k.ext.iframe());h.css({position:"relative",display:"inline-block"}),$el.wrap(h),g.addClass("sf-dp-wrapper dp-wrapper").css({height:b.height,zIndex:"",display:"none"}),f.addClass("sf-dp-trigger"),f.insertAfter($el),g.appendTo(l);var m=new dhtmlxCalendarObject(e,!1,null,k,j);m.setDateFormat(i.dateFormat),m.loadUserLanguage("en-us"),m.setSkin("gd"),m.draw(),m.show(),m.hide(),m.setWeekStartDay(7),m.hideTime(),i.dateOverride&&"none"!==i.dateOverride&&m.setDate(m.getFormatedDate(m._dateFormat,m._strToDate(i.dateOverride))),m.setHolidays(m.getFormatedDate(m._dateFormat,m.getDate())),$el.val()&&"Invalid Date"!==m._strToDate($el.val())&&m.setDate(m._strToDate($el.val()));var n=a(m.base);n.css({position:"absolute"});var o=a('<a class="today" href="#"></a>');n.append(o),this.set("uid",e),this.set("container",n),this.set("input",$el),this.set("wrapper",g),this.set("trigger",f),this.set("layer",l),this.set("cal",m),this.set("today",o),this.set("defaultDate",this._getDefaultDate()),this.bindings(),i.rangeGroup&&this.rangedBindings(),this.setDate("none"===i.dateOverride?"":this.get("defaultDate")),i.minDate||i.maxDate?this._setRange(i.minDate,i.maxDate):i.beforeToday||this._setMinRange(),c--,0===c&&(i.afterInit.call(me),d.trigger("dp.allInitsFinished.dp-on"+this.get("uid"))),this.positionTrigger();var p=this,q=function(a){var b=a.args[0]&&a.args[0].moduleElement?a.args[0].moduleElement:null;b&&(k.isSingle()?b.has(p.get("input")).length>0:$sf.util.jCompat.isEl(b,k.ext.iframe()))&&(p.showAll(),p.positionWrapper())},r=k.ext.window();r.$sf.util.event.on("dragstart",function(a){var b=a.args[0]&&a.args[0].moduleElement?a.args[0].moduleElement:null;b&&(k.isSingle()?b.has(p.get("input")).length>0:$sf.util.jCompat.isEl(b,k.ext.iframe()))&&p.hideAll(!0)}),r.$sf.util.event.on("dragend",q),r.$sf.util.event.on("resize",q)},destroy:function(){var b=k.ext.window();b.$sf.util.event.off("dragstart"),b.$sf.util.event.off("dragend"),b.$sf.util.event.off("resize");var c=this.get("cal");c.unload();var d=this.get("wrapper"),e=this.get("input").before(d),f=".dp-on"+this.get("uid");e.unbind("click"+f),e.unbind("change"+f),a(k.intr.window()).unbind("resize"+f),k.isSingle()||(a(k.ext.window()).unbind("resize"+f),a(k.intr.window()).unbind("scroll"+f)),d.remove(),this.get("container").remove(),this.get("trigger").remove()},positionTrigger:function(){var a=this.get("input"),b=this.get("trigger");if("undefined"!=typeof b){var c={top:0,left:0};b.css({position:"absolute",top:c.top+"px",left:a.outerWidth(!0)+c.left-b.outerWidth()+"px",display:!k.isSingle()&&!k.intr.isInBounds(a)||"true"===b.attr("data-hidden")?"none":"block"})}},positionWrapper:function(){var b=this.get("input"),c=this.get("wrapper");if("undefined"!=typeof c){var d=null!==$sf.util.zIndex.getStackingParent(b),e=k.ext.offset(b,d);k.isSingle()&&d&&(e.top-=a(k.ext.window()).scrollTop(),e.left-=a(k.ext.window()).scrollLeft()),c.css({position:d?"fixed":"absolute",top:e.top+"px",left:e.left+"px",display:!k.isSingle()&&!k.intr.isInBounds(b)||"true"===c.attr("data-hidden")?"none":"block"})}},enable:function(){var a=this.get("input"),b=this.get("trigger");a.removeAttr("disabled"),this.get("trigger").removeAttr("data-disabled"),b.css("cursor","")},disable:function(){var a=this.get("input"),b=this.get("trigger");a.attr("disabled",!0),this.get("trigger").attr("data-disabled",!0),b.css("cursor","default")},show:function(){var a=this.get("trigger"),b=this.get("wrapper"),c=this.get("layer"),d=this.get("container"),e=this.get("input"),f=k.ext.offset(e);this.get("cal");this.positionWrapper();var g=e.outerWidth()-d.outerWidth()+1,h=g,j=f.left-(d.outerWidth()-e.outerWidth());if(0>j)var h=e.outerWidth()-27;var l={top:a.outerHeight(),left:h};e.addClass("dp-showing"),a.addClass("dp-trigger-active");var m=isNaN(i.zIndex)?$sf.util.zIndex.getTop(null!==i.zAnchor?i.zAnchor:e):i.zIndex;b.css({zIndex:$sf.util.zIndex.maxForLayer(c)+1}).removeAttr("data-hidden"),a.css({zIndex:m+10}).removeAttr("data-hidden"),d.css({top:l.top-2,left:l.left-1}),this._updateCalDateFromInput(),b.show(),d.show(),this._appendArrow()},bindings:function(){function b(){f.positionTrigger(),f.positionWrapper()}function c(){try{b()}catch(a){n.unbind("resize"+m)}}var f=this,g=this.get("trigger"),h=(this.get("input"),this.get("container"),this.get("wrapper"),this.get("input")),j=this.get("cal"),l=this.get("today"),m=".dp-on"+f.get("uid"),n=a(k.ext.window()),o=a(k.intr.window());h.bind("click"+m,function(b){function c(){f.hide(),m.unbind("click"+g)}function d(){try{c()}catch(a){l.unbind("click"+g)}}if(b.stopPropagation(),b.preventDefault(),h.hasClass("dp-showing"))f.hide();else{a.each(e.instances,function(a,b){try{b.hasClass("dp-showing")&&!$sf.util.jCompat.isEl(b,h)&&b.sfDatePicker("hide")}catch(c){delete e.instances[a]}}),f.show();var g=".dp-blur"+f.get("uid"),l=a(k.ext.body()),m=a(k.intr.body());m.unbind("click"+g),m.bind("click"+g,c),k.isSingle()||(l.unbind("click"+g),l.bind("click"+g,d))}if(!j.attached){j.attached=!0,j.attachEvent("onClick",function(a){f.setDate(f._getCurrentDate()),f.hide(),i.onSelect(h,f._getCurrentDate())}),j.attachEvent("onChange",function(){a(".dhtmlxcalendar_month_label_year").removeClass("selected"),a(".dhtmlxcalendar_month_label_month").removeClass("selected")}),j.attachEvent("onSelectorHide",function(){a(".dhtmlxcalendar_month_label_year").removeClass("selected"),a(".dhtmlxcalendar_month_label_month").removeClass("selected")});j.attachEvent("onShowMonthSelector",function(b){a(b).addClass("selected"),a(b).siblings().removeClass("selected")}),j.attachEvent("onShowYearSelector",function(b){a(b).addClass("selected"),a(b).siblings().removeClass("selected")}),j.attachEvent("onYearSelected",function(a){f._appendArrow()})}}),h.bind("change"+m,function(a){i.onChange(h,h.val())}),o.bind("resize"+m,b),k.isSingle()||(n.bind("resize"+m,c),o.bind("scroll"+m,b)),d.bind("dp.allInitsFinished"+m,c),g.click(function(b){b.preventDefault(),b.stopPropagation(),a(this).attr("data-disabled")||h.trigger("click"+m)}),l.click(function(a){a.preventDefault();var b=j.getFormatedDate(j._dateFormat,new Date);f.setDate(b),i.onSelect(h,b),f.hide()})},rangedBindings:function(){function b(){var a=new Date(f._getBrowserFriendlyDate()),b=new Date,c=!1;a.getTime()>b.getTime()&&(c=!0),e.trigger("sfdp."+g,{date:d.getFormatedDate(d._dateFormat),disableToday:c})}var c=this.get("trigger"),d=this.get("cal"),e=this.get("input"),f=this,g=i.rangeGroup,h=(this.get("today"),a('[data-range-group="'+g+'"]')),j=h.filter(":eq(0)"),k=h.filter(":eq(1)");h.length>1&&(j.data("range-position","from"),k.data("range-position","to")),"to"===e.data("range-position")?(j.bind("sfdp."+g,function(a,b){d.clearSensitiveRange(),f._setMinRange(b.date),f._disableToday(b.disableToday)}),j.bind("sfdp.triggerOpen",function(a,b){if(!b){var c=this.get("cal"),b={};c.setDate(d.getFormatedDate(c._dateFormat,new Date(j.val()))),b.date=c.getFormatedDate(c._dateFormat)}d.clearSensitiveRange(),""!==j.val()&&f._setMinRange(b.date)}),c.click(function(){j.trigger("sfdp.triggerOpen"),f._appendArrow(),setTimeout(function(){f._appendArrow()},300)})):(e.bind("sfdp.updateRange",b),d.attachEvent("onClick",function(a){f.setDate(f._getCurrentDate()),f.hide(),b()}),j.bind("change",function(){f.setDate(a(this).val())}),c.click(function(){e.trigger("sfdp.triggerOpen",{date:d.getFormatedDate(d._dateFormat)})})),a(this).removeClass("me")},hide:function(){var a=this.get("trigger"),b=this.get("container"),c=this.get("input"),d=this.get("wrapper");a.css({zIndex:""}).attr("data-hidden",!0),d.css({zIndex:"",display:"none"}).attr("data-hidden",!0),c.removeClass("dp-showing"),a.removeClass("dp-trigger-active"),b.unbind("click.dp-protect"),b.hide()},hideAll:function(a){var b=this.get("trigger"),c=this.get("input"),d=this.get("wrapper");this.hide(),b.hide(),d.hide(),a!==!0&&c.hide()},showAll:function(){var a=this.get("trigger"),b=this.get("input"),c=this.get("wrapper");a.removeAttr("data-hidden").show(),c.removeAttr("data-hidden").show(),b.show(),this.positionTrigger()},set:function(b,c){a(this).data(b,c)},get:function(b,c){return a(this).data(b)},setDate:function(a){"date"==typeof a&&(a=("0"+(a.getUTCMonth()+1)).substr(-2)+"/"+("0"+a.getUTCDate()).substr(-2)+"/"+a.getUTCFullYear());var b=this.get("input"),c=b.val();b.val(a),this._updateCalDateFromInput(),c!==a&&i.onChange(b,a),b.trigger("sfdp.updateRange")},_appendArrow:function(){var a=this.get("container"),b=a.find(".dhtmlxcalendar_month_label_year").text();b+='<span class="arrow"></span>',a.find(".dhtmlxcalendar_month_label_year").html(b)},_disableToday:function(a){var b=this.get("today");a?b.hide():b.show()},_getDefaultDate:function(){var a=this.get("cal");return a.getFormatedDate(i.dateFormat,a.getDate())},_getCurrentDate:function(){var a=this.get("cal");return a.getFormatedDate(i.dateFormat)},_getPluginFriendlyDate:function(){var a=this.get("cal");return a.getFormatedDate(a._dateFormat,a.getDate())},_getBrowserFriendlyDate:function(){var a=this.get("cal");return a.getFormatedDate(a._dateFormat,a.getDate())},_setMinRange:function(a){var b=this.get("cal"),c=this._getDayBefore(this._getBrowserFriendlyDate());if(a){c=this._getDayBefore(a);var d=this.get("input").val(),e=new Date(this._getBrowserFriendlyDate()),f=new Date(a);(""===d||e.getTime()<f.getTime())&&(b.setDate(b.getFormatedDate(b._dateFormat,new Date(a))),this.setDate(b.getFormatedDate(i.dateFormat)))}"to"===this.get("input").data("range-position")&&"undefined"!=typeof this.max&&this.max.length>0?b.setSensitiveRange(this._getDayAfter(c),this.max):b.setInsensitiveRange(null,c)},_setRange:function(a,b){var c=this.get("cal"),d=null,e=null;a&&(d=c.getFormatedDate(c._dateFormat,a),this.min=c.getFormatedDate(c._dateFormat,a)),b&&(e=c.getFormatedDate(c._dateFormat,b),this.max=c.getFormatedDate(c._dateFormat,b));var f=new Date(this._getBrowserFriendlyDate());a&&f.getTime()<a.getTime()?(c.setDate(c.getFormatedDate(c._dateFormat,new Date(a))),this.setDate(c.getFormatedDate(i.dateFormat))):b&&f.getTime()>b.getTime()&&(c.setDate(c.getFormatedDate(c._dateFormat,new Date(b))),this.setDate(c.getFormatedDate(i.dateFormat))),c.setSensitiveRange(d,e)},_getDayBefore:function(a){var b=this.get("cal"),a=b._strToDate(a);a.setDate(a.getDate()-1);var c=b.getFormatedDate(b._dateFormat,a);return c},_getDayAfter:function(a){var b=this.get("cal"),a=b._strToDate(a);a.setDate(a.getDate()+1);var c=b.getFormatedDate(b._dateFormat,a);return c},_updateCalDateFromInput:function(){var a=this.get("input"),b=this.get("cal"),c=new Date;c.setHours(0,0,0,0);var d=""===a.val()?c:b._strToDate(a.val());if(d.setHours(0,0,0,0),d.getTime()<c.getTime()&&!i.beforeToday){var e=this._getCurrentDate();a.val(e),i.onChange(a,e)}b.setDate(b.getFormatedDate(b._dateFormat,d))}}),this.init()}))};return b.prototype.setYearsRange=function(){},b.prototype.lang="en",b.prototype.langData={en:{dateformat:"%Y-%m-%d",monthesFNames:["January","February","March","April","May","June","July","August","September","October","November","December"],monthesSNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],daysFNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],daysSNames:["Su","Mo","Tu","We","Th","Fr","Sa"],weekstart:1}},dhtmlxCalendarObject=b,a.fn.sfDatePicker}($sf.getjQuery())});