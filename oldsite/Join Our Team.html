<!DOCTYPE html>
<!-- saved from url=(0055)http://www.champions-sportsgrill.com/join-our-team.html -->
<html lang="en" dir="ltr" data-tcc-ignore=""><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><title>Join Our Team</title><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><link rel="stylesheet" type="text/css" href="./Join Our Team_files/site.css"><script> if (typeof ($sf) === "undefined") { $sf = { baseUrl: "https://p3pprd001.cloudstorage.secureserver.net/wsbv7-assets/WSB7_J_20250303_2130_DEP-03042_5487/v2", skin: "app", preload: 0, require: { jquery: "https://p3pprd001.cloudstorage.secureserver.net/wsbv7-assets/WSB7_J_20250303_2130_DEP-03042_5487/v2/libs/jquery/jq.js", paths: { "wsbcore": "common/wsb/core", "knockout": "libs/knockout/knockout" } } }; } </script><script id="duel" src="./Join Our Team_files/duel.js.download"></script><script charset="utf-8" async="" src="./Join Our Team_files/jq.js.download"></script><script> define('jquery', ['jq!starfield/jquery.mod'], function(m) { return m; }); define('appconfig', [], { documentDownloadBaseUrl: 'https://nebula.wsimg.com' }); </script><meta http-equiv="Content-Location" content="join-our-team.html"><meta name="generator" content="Starfield Technologies; Go Daddy Website Builder 7.0.5350"><meta name="description" content="Employment opportunities, job openeings"><meta property="og:type" content="website"><meta property="og:title" content="Join Our Team"><meta property="og:site_name" content="Champions Sports Grilll"><meta property="og:url" content="http://www.champions-sportsgrill.com/join-our-team.html"><meta property="og:description" content="Employment opportunities, job openeings"><meta property="og:image" content="https://nebula.wsimg.com/2ccfa39d17737cc00eaabb607354d5e2?AccessKeyId=1549AF287DE5730D6508&amp;disposition=0&amp;alloworigin=1"><script charset="utf-8" async="" src="./Join Our Team_files/customForm.published.js.download"></script><script charset="utf-8" async="" src="./Join Our Team_files/cookiemanager.js.download"></script><script charset="utf-8" async="" src="./Join Our Team_files/iebackground.js.download"></script><script charset="utf-8" async="" src="./Join Our Team_files/util.window.js.download"></script><script charset="utf-8" async="" src="./Join Our Team_files/regexhelper.js.download"></script><script charset="utf-8" async="" src="./Join Our Team_files/api.guid.js.download"></script><script charset="utf-8" async="" src="./Join Our Team_files/jquery.xDomainRequest.js.download"></script><script charset="utf-8" async="" src="./Join Our Team_files/tipper.js.download"></script><script charset="utf-8" async="" src="./Join Our Team_files/datepicker.js.download"></script><script charset="utf-8" async="" src="./Join Our Team_files/jquery.watermark.js.download"></script><link rel="stylesheet" href="chrome-extension://ihcjicgdanjaechkgeegckofjjedodee/app/content-style.css"><script charset="utf-8" async="" src="./Join Our Team_files/sf.tipper.js.download"></script><script charset="utf-8" async="" src="./Join Our Team_files/sf.datepicker.js.download"></script><script charset="utf-8" async="" src="./Join Our Team_files/sf.core.pkg.js.download"></script><link rel="stylesheet" type="text/css" _curl_movable="true" href="./Join Our Team_files/app.css"><script charset="utf-8" async="" src="./Join Our Team_files/sf.core.pkg.js.download"></script><link rel="stylesheet" type="text/css" _curl_movable="true" href="./Join Our Team_files/app(1).css"></head><body cz-shortcut-listen="true"><style data-inline-fonts="">/* vietnamese */
@font-face {
  font-family: 'Allura';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/allura/v22/9oRPNYsQpS4zjuA_hAgWDto.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Allura';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/allura/v22/9oRPNYsQpS4zjuA_hQgWDto.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Allura';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/allura/v22/9oRPNYsQpS4zjuA_iwgW.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic */
@font-face {
  font-family: 'Amatic SC';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/amaticsc/v27/TUZyzwprpvBS1izr_vOEDuSfQZQ.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* hebrew */
@font-face {
  font-family: 'Amatic SC';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/amaticsc/v27/TUZyzwprpvBS1izr_vOECOSfQZQ.woff2) format('woff2');
  unicode-range: U+0307-0308, U+0590-05FF, U+200C-2010, U+20AA, U+25CC, U+FB1D-FB4F;
}
/* vietnamese */
@font-face {
  font-family: 'Amatic SC';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/amaticsc/v27/TUZyzwprpvBS1izr_vOEBeSfQZQ.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Amatic SC';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/amaticsc/v27/TUZyzwprpvBS1izr_vOEBOSfQZQ.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Amatic SC';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/amaticsc/v27/TUZyzwprpvBS1izr_vOECuSf.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* vietnamese */
@font-face {
  font-family: 'Arizonia';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/arizonia/v22/neIIzCemt4A5qa7mv5WOFqwKUQ.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Arizonia';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/arizonia/v22/neIIzCemt4A5qa7mv5WPFqwKUQ.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Arizonia';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/arizonia/v22/neIIzCemt4A5qa7mv5WBFqw.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin */
@font-face {
  font-family: 'Averia Sans Libre';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/averiasanslibre/v20/ga6XaxZG_G5OvCf_rt7FH3B6BHLMEdVOEoI.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin */
@font-face {
  font-family: 'Cabin Sketch';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/cabinsketch/v22/QGYpz_kZZAGCONcK2A4bGOj8mNhN.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* vietnamese */
@font-face {
  font-family: 'Francois One';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/francoisone/v21/_Xmr-H4zszafZw3A-KPSZut9zgiRi_Y.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Francois One';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/francoisone/v21/_Xmr-H4zszafZw3A-KPSZut9zwiRi_Y.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Francois One';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/francoisone/v21/_Xmr-H4zszafZw3A-KPSZut9wQiR.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Fredericka the Great';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/frederickathegreat/v22/9Bt33CxNwt7aOctW2xjbCstzwVKsIBVV--StxbcVcg.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Fredericka the Great';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/frederickathegreat/v22/9Bt33CxNwt7aOctW2xjbCstzwVKsIBVV--Sjxbc.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin */
@font-face {
  font-family: 'Jacques Francois Shadow';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/jacquesfrancoisshadow/v26/KR1FBtOz8PKTMk-kqdkLVrvR0ECFrB6Pin-2_p8Suno.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin */
@font-face {
  font-family: 'Josefin Slab';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/josefinslab/v28/lW-swjwOK3Ps5GSJlNNkMalNpiZe_ldbOR4W71msR349Kg.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Kaushan Script';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/kaushanscript/v18/vm8vdRfvXFLG3OLnsO15WYS5DG72wNJHMw.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Kaushan Script';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/kaushanscript/v18/vm8vdRfvXFLG3OLnsO15WYS5DG74wNI.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Love Ya Like A Sister';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/loveyalikeasister/v22/R70EjzUBlOqPeouhFDfR80-0FhOqJubN-BeL-3xdgGE.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Love Ya Like A Sister';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/loveyalikeasister/v22/R70EjzUBlOqPeouhFDfR80-0FhOqJubN-BeL9Xxd.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Merriweather';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/merriweather/v32/u-4D0qyriQwlOrhSvowK_l5UcA6zuSYEqOzpPe3HOZJ5eX1WtLaQwmYiScCmDxhtNOKl8yDr3icaGV31GvU.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Merriweather';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/merriweather/v32/u-4D0qyriQwlOrhSvowK_l5UcA6zuSYEqOzpPe3HOZJ5eX1WtLaQwmYiScCmDxhtNOKl8yDr3icaEF31GvU.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Merriweather';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/merriweather/v32/u-4D0qyriQwlOrhSvowK_l5UcA6zuSYEqOzpPe3HOZJ5eX1WtLaQwmYiScCmDxhtNOKl8yDr3icaG131GvU.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Merriweather';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/merriweather/v32/u-4D0qyriQwlOrhSvowK_l5UcA6zuSYEqOzpPe3HOZJ5eX1WtLaQwmYiScCmDxhtNOKl8yDr3icaGl31GvU.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Merriweather';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/merriweather/v32/u-4D0qyriQwlOrhSvowK_l5UcA6zuSYEqOzpPe3HOZJ5eX1WtLaQwmYiScCmDxhtNOKl8yDr3icaFF31.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Offside';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/offside/v25/HI_KiYMWKa9QrAykc5joR6-d.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Offside';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/offside/v25/HI_KiYMWKa9QrAykc5boRw.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4taVIGxA.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4kaVIGxA.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4saVIGxA.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4jaVIGxA.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* hebrew */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4iaVIGxA.woff2) format('woff2');
  unicode-range: U+0307-0308, U+0590-05FF, U+200C-2010, U+20AA, U+25CC, U+FB1D-FB4F;
}
/* math */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B5caVIGxA.woff2) format('woff2');
  unicode-range: U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;
}
/* symbols */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B5OaVIGxA.woff2) format('woff2');
  unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
}
/* vietnamese */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4vaVIGxA.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4uaVIGxA.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4gaVI.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Oswald';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/oswald/v56/TK3_WkUHHAIjg75cFRf3bXL8LICs1_FvsUtiZTaR.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Oswald';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/oswald/v56/TK3_WkUHHAIjg75cFRf3bXL8LICs1_FvsUJiZTaR.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Oswald';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/oswald/v56/TK3_WkUHHAIjg75cFRf3bXL8LICs1_FvsUliZTaR.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Oswald';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/oswald/v56/TK3_WkUHHAIjg75cFRf3bXL8LICs1_FvsUhiZTaR.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Oswald';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/oswald/v56/TK3_WkUHHAIjg75cFRf3bXL8LICs1_FvsUZiZQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Over the Rainbow';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/overtherainbow/v22/11haGoXG1k_HKhMLUWz7Mc7vvW5ulvqs9eA2.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Over the Rainbow';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/overtherainbow/v22/11haGoXG1k_HKhMLUWz7Mc7vvW5ulvSs9Q.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Pacifico';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/pacifico/v22/FwZY7-Qmy14u9lezJ-6K6MmTpA.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Pacifico';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/pacifico/v22/FwZY7-Qmy14u9lezJ-6D6MmTpA.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Pacifico';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/pacifico/v22/FwZY7-Qmy14u9lezJ-6I6MmTpA.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Pacifico';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/pacifico/v22/FwZY7-Qmy14u9lezJ-6J6MmTpA.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Pacifico';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/pacifico/v22/FwZY7-Qmy14u9lezJ-6H6Mk.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Romanesco';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/romanesco/v21/w8gYH2ozQOY7_r_J7mSX1XYKmOo.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Romanesco';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/romanesco/v21/w8gYH2ozQOY7_r_J7mSX23YK.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Sacramento';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sacramento/v16/buEzpo6gcdjy0EiZMBUG4CMf_exL.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Sacramento';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sacramento/v16/buEzpo6gcdjy0EiZMBUG4C0f_Q.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Seaweed Script';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/seaweedscript/v16/bx6cNx6Tne2pxOATYE8C_Rsoe3WA8qY2VQ.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Seaweed Script';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/seaweedscript/v16/bx6cNx6Tne2pxOATYE8C_Rsoe3WO8qY.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Special Elite';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/specialelite/v19/XLYgIZbkc4JPUL5CVArUVL0ntn4OSEFt.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Special Elite';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/specialelite/v19/XLYgIZbkc4JPUL5CVArUVL0ntnAOSA.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* cyrillic-ext */
@font-face {
  font-family: 'Bitter';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8fbfOLXOXWh2.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Bitter';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8fbfOLzOXWh2.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Bitter';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8fbfOLfOXWh2.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Bitter';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8fbfOLbOXWh2.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Bitter';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8fbfOLjOXQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sourcesanspro/v22/6xK3dSBYKcSV-LCoeQqfX1RYOo3qNa7lqDY.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sourcesanspro/v22/6xK3dSBYKcSV-LCoeQqfX1RYOo3qPK7lqDY.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sourcesanspro/v22/6xK3dSBYKcSV-LCoeQqfX1RYOo3qNK7lqDY.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sourcesanspro/v22/6xK3dSBYKcSV-LCoeQqfX1RYOo3qO67lqDY.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sourcesanspro/v22/6xK3dSBYKcSV-LCoeQqfX1RYOo3qN67lqDY.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sourcesanspro/v22/6xK3dSBYKcSV-LCoeQqfX1RYOo3qNq7lqDY.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sourcesanspro/v22/6xK3dSBYKcSV-LCoeQqfX1RYOo3qOK7l.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
</style><style type="text/css"> #wsb-element-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9{top:-215px;left:46px;position:absolute;z-index:12}#wsb-element-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9{width:769px;height:98px}#wsb-element-00000000-0000-0000-0000-000401601928{top:-106px;left:858px;position:absolute;z-index:52}#wsb-element-00000000-0000-0000-0000-000401601928 .wsb-htmlsnippet-element{width:41px;height:40px;overflow:hidden;margin:auto}#wsb-element-00000000-0000-0000-0000-000394044459{top:48px;left:328px;position:absolute;z-index:47}#wsb-element-00000000-0000-0000-0000-000394044459 .wsb-image-inner{}#wsb-element-00000000-0000-0000-0000-000394044459 .wsb-image-inner div{width:98px;height:35px;position:relative;overflow:hidden}#wsb-element-00000000-0000-0000-0000-000394044459 img{position:absolute}#wsb-element-00000000-0000-0000-0000-000394044455{top:-246px;left:-5px;position:absolute;z-index:11}#wsb-element-00000000-0000-0000-0000-000394044455 .wsb-shape{width:914px;height:227px;-webkit-border-radius:10px;-moz-border-radius:10px;-o-border-radius:10px;border-radius:10px;padding:0px;background:#fff;-moz-opacity:0.97;-khtml-opacity:0.97;opacity:0.97;box-sizing:content-box;-moz-box-sizing:content-box}#wsb-element-00000000-0000-0000-0000-000394044440{top:-106px;left:46px;position:absolute;z-index:13}#wsb-element-00000000-0000-0000-0000-000394044440 .txt{width:822px;height:69px}#wsb-element-66b19782-f356-4e78-8f08-d988bd450fdb{top:1674px;left:271px;position:absolute;z-index:100}#wsb-element-66b19782-f356-4e78-8f08-d988bd450fdb>div.form-row{width:300px;height:65px;padding:0px}#wsb-element-00000000-0000-0000-0000-000431174186{top:1457px;left:271px;position:absolute;z-index:100}#wsb-element-00000000-0000-0000-0000-000431174186>div.form-row{width:300px;height:217px;padding:0px}#wsb-element-00000000-0000-0000-0000-000400224438{top:764px;left:271px;position:absolute;z-index:100}#wsb-element-00000000-0000-0000-0000-000400224438>div.form-row{width:300px;height:60px;padding:0px}#wsb-element-00000000-0000-0000-0000-000400061169{top:1833px;left:23px;position:absolute;z-index:64}#wsb-element-00000000-0000-0000-0000-000400061169 .txt{width:857px;height:149px}#wsb-element-00000000-0000-0000-0000-000399994949{top:1141px;left:271px;position:absolute;z-index:100}#wsb-element-00000000-0000-0000-0000-000399994949>div.form-row{width:300px;height:60px;padding:0px}#wsb-element-00000000-0000-0000-0000-000399963440{top:1008px;left:271px;position:absolute;z-index:100}#wsb-element-00000000-0000-0000-0000-000399963440>div.form-row{width:300px;height:60px;padding:0px}#wsb-element-00000000-0000-0000-0000-000399960130{top:1287px;left:271px;position:absolute;z-index:100}#wsb-element-00000000-0000-0000-0000-000399960130>div.form-row{width:300px;height:85px;padding:0px}#wsb-element-00000000-0000-0000-0000-000399956195{top:698px;left:271px;position:absolute;z-index:100}#wsb-element-00000000-0000-0000-0000-000399956195>div.form-row{width:300px;height:65px;padding:0px}#wsb-element-00000000-0000-0000-0000-000399956194{top:1201px;left:271px;position:absolute;z-index:100}#wsb-element-00000000-0000-0000-0000-000399956194>div.form-row{width:300px;height:85px;padding:0px}#wsb-element-00000000-0000-0000-0000-000399953513{top:1068px;left:271px;position:absolute;z-index:100}#wsb-element-00000000-0000-0000-0000-000399953513>div.form-row{width:300px;height:73px;padding:0px}#wsb-element-00000000-0000-0000-0000-000399953512{top:1372px;left:271px;position:absolute;z-index:100}#wsb-element-00000000-0000-0000-0000-000399953512>div.form-row{width:300px;height:85px;padding:0px}#wsb-element-00000000-0000-0000-0000-000399929044{top:949px;left:271px;position:absolute;z-index:100}#wsb-element-00000000-0000-0000-0000-000399929044>div.form-row{width:300px;height:60px;padding:0px}#wsb-element-00000000-0000-0000-0000-000399929043{top:889px;left:271px;position:absolute;z-index:100}#wsb-element-00000000-0000-0000-0000-000399929043>div.form-row{width:300px;height:60px;padding:0px}#wsb-element-00000000-0000-0000-0000-000399907579{top:625px;left:271px;position:absolute;z-index:100}#wsb-element-00000000-0000-0000-0000-000399907579>div.form-row{width:300px;height:10px;padding:0px}#wsb-element-00000000-0000-0000-0000-000399907578{top:824px;left:271px;position:absolute;z-index:100}#wsb-element-00000000-0000-0000-0000-000399907578>div.form-row{width:300px;height:65px;padding:0px}#wsb-element-00000000-0000-0000-0000-000399882348{top:404px;left:271px;position:absolute;z-index:100}#wsb-element-00000000-0000-0000-0000-000399882348>div.form-row{width:300px;height:90px;padding:0px}#wsb-element-00000000-0000-0000-0000-000399882347{top:339px;left:271px;position:absolute;z-index:100}#wsb-element-00000000-0000-0000-0000-000399882347>div.form-row{width:300px;height:65px;padding:0px}#wsb-element-00000000-0000-0000-0000-000399882346{top:494px;left:271px;position:absolute;z-index:100}#wsb-element-00000000-0000-0000-0000-000399882346>div.form-row{width:300px;height:65px;padding:0px}#wsb-element-00000000-0000-0000-0000-000399882345{top:560px;left:271px;position:absolute;z-index:100}#wsb-element-00000000-0000-0000-0000-000399882345>div.form-row{width:300px;height:65px;padding:0px}#wsb-element-00000000-0000-0000-0000-000399882344{top:1739px;left:271px;position:absolute;z-index:100}#wsb-element-00000000-0000-0000-0000-000399882344>div.form-row{width:300px;height:69px;padding:0px}#wsb-element-00000000-0000-0000-0000-000397738817{top:2110px;left:262.5px;position:absolute;z-index:50}#wsb-element-00000000-0000-0000-0000-000397738817 .wsb-image-inner{-webkit-border-radius:15px;-moz-border-radius:15px;-o-border-radius:15px;border-radius:15px;padding:0px}#wsb-element-00000000-0000-0000-0000-000397738817 .wsb-image-inner div{width:378px;height:310px;position:relative;overflow:hidden}#wsb-element-00000000-0000-0000-0000-000397738817 img{position:absolute;-webkit-border-radius:15px;-moz-border-radius:15px;-o-border-radius:15px;border-radius:15px}#wsb-element-00000000-0000-0000-0000-000394417348{top:2009px;left:372px;position:absolute;z-index:49}#wsb-element-00000000-0000-0000-0000-000394417348 .wsb-button{width:142px;height:49px}#wsb-element-00000000-0000-0000-0000-000394399776{top:28px;left:35px;position:absolute;z-index:48}#wsb-element-00000000-0000-0000-0000-000394399776 .txt{width:829px;height:376px} </style><div class="wsb-canvas body"><div class="wsb-canvas-page-container" style="min-height: 100%; padding-top: 254px; position: relative;"><div class="wsb-canvas-scrollable" style="filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=&#39;#bf0000&#39;, endColorstr=&#39;#ffffff&#39;,GradientType=1 ); background-image: url(https://nebula.wsimg.com/7104c9f53a311b825478fb0ee132c3be?AccessKeyId=531592D248B589D87A56&amp;alloworigin=1); background: url(https://nebula.wsimg.com/7104c9f53a311b825478fb0ee132c3be?AccessKeyId=531592D248B589D87A56&amp;alloworigin=1), -moz-linear-gradient(left, #bf0000 0%, #ffffff 100%);; background: url(https://nebula.wsimg.com/7104c9f53a311b825478fb0ee132c3be?AccessKeyId=531592D248B589D87A56&amp;alloworigin=1), -webkit-gradient(linear, left top, right top, color-stop(0%,#bf0000), color-stop(100%,#ffffff)); background: url(https://nebula.wsimg.com/7104c9f53a311b825478fb0ee132c3be?AccessKeyId=531592D248B589D87A56&amp;alloworigin=1), -webkit-linear-gradient(left, #bf0000 0%, #ffffff 100%); background: url(https://nebula.wsimg.com/7104c9f53a311b825478fb0ee132c3be?AccessKeyId=531592D248B589D87A56&amp;alloworigin=1), -o-linear-gradient(left, #bf0000 0%,#ffffff 100%); background: url(https://nebula.wsimg.com/7104c9f53a311b825478fb0ee132c3be?AccessKeyId=531592D248B589D87A56&amp;alloworigin=1), -ms-linear-gradient(left, #bf0000 0%,#ffffff 100%); background: url(https://nebula.wsimg.com/7104c9f53a311b825478fb0ee132c3be?AccessKeyId=531592D248B589D87A56&amp;alloworigin=1), linear-gradient(to right, #bf0000 0%,#ffffff 100%); background-position-x: left; background-position-y: top; background-position: left top; background-repeat: repeat; position: absolute; width: 100%; height: 100%;"></div><div id="wsb-canvas-template-page" class="wsb-canvas-page page" style="height: 2857px; margin: auto; width: 903px; background-color: #ff8d02; position: relative; "><div id="wsb-canvas-template-container" style="position: absolute;"> <div id="wsb-element-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9" class="wsb-element-navigation" data-type="element"> <div style="width: 769px; height: 98px;" class="wsb-nav nav_theme nav-text-center nav-horizontal nav-btn-right wsb-navigation-rendered-top-level-container" id="wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9"><style> #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container ul > li > a {color:#0000ff;} #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container ul > li:hover, #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container ul > li:hover > a, #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container ul > li.active:hover, #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container ul > li.active > a:hover, #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container ul > li.active .nav-subnav li:hover, #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container ul > li.active .nav-subnav li:hover > a {background-color: !important;color:#ff0000 !important;} #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container > ul.wsb-navigation-rendered-top-level-menu > li.active, #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container > ul.wsb-navigation-rendered-top-level-menu > li.active > a {background-image:none;background-color:#ffff56;color:#bf0000;} </style><ul class="wsb-navigation-rendered-top-level-menu "><li style="width: auto"><a href="http://www.champions-sportsgrill.com/home.html" target="" data-title="Home" data-pageid="00000000-0000-0000-0000-000000026933" data-url="home.html">Home</a></li><li style="width: auto"><a href="http://www.champions-sportsgrill.com/photo-gallery.html" target="" data-title="Photo Gallery" data-pageid="00000000-0000-0000-0000-000000054257" data-url="photo-gallery.html">Photo Gallery</a></li><li style="width: auto"><a href="http://www.champions-sportsgrill.com/menu.html" target="" data-title="Menu" data-pageid="00000000-0000-0000-0000-000008564320" data-url="menu.html">Menu</a></li><li style="width: auto"><a href="http://www.champions-sportsgrill.com/location---contact.html" target="" data-title="Location &amp; Contact" data-pageid="00000000-0000-0000-0000-000000054270" data-url="location---contact.html">Location &amp; Contact</a></li><li style="width: auto" class="active"><a href="http://www.champions-sportsgrill.com/join-our-team.html" target="" data-title="Join Our Team" data-pageid="00000000-0000-0000-0000-000394047957" data-url="join-our-team.html">Join Our Team</a></li><li style="width: auto"><a href="http://www.champions-sportsgrill.com/feedback---comments.html" target="" data-title="Feedback &amp; Comments" data-pageid="00000000-0000-0000-0000-000394420685" data-url="feedback---comments.html">Feedback &amp; Comments</a></li><li style="width: auto"><a href="http://www.champions-sportsgrill.com/specials---coupons.html" target="" data-title="Specials &amp; Coupons" data-pageid="00000000-0000-0000-0000-000394443923" data-url="specials---coupons.html">Specials &amp; Coupons</a></li><li style="width: auto"><a href="http://www.champions-sportsgrill.com/happenings.html" target="" data-title="Happenings" data-pageid="00000000-0000-0000-0000-000394444207" data-url="happenings.html">Happenings</a></li><li style="width: auto"><a href="http://www.champions-sportsgrill.com/private-event.html" target="" data-title="Private Event" data-pageid="00000000-0000-0000-0000-000394858045" data-url="private-event.html">Private Event</a></li><li style="width: auto"><a href="http://www.champions-sportsgrill.com/donations--fundraisers----sponsorships.html" target="" data-title="Donations, Fundraisers, &amp; Sponsorships" data-pageid="bc305eb3-d88f-4eee-8d25-9a89e574ea91" data-url="donations--fundraisers----sponsorships.html">Donations, Fundraisers, &amp; Sponsorships</a></li></ul></div> </div><div id="wsb-element-00000000-0000-0000-0000-000401601928" class="wsb-element-htmlsnippet" data-type="element">




        <div class="wsb-htmlsnippet-element"><style>.ig-b- { display: inline-block; }
.ig-b- img { visibility: hidden; }
.ig-b-:hover { background-position: 0 -60px; } .ig-b-:active { background-position: 0 -120px; }
.ig-b-32 { width: 32px; height: 32px; background: url(//badges.instagram.com/static/images/ig-badge-sprite-32.png) no-repeat 0 0; }
@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min--moz-device-pixel-ratio: 2), only screen and (-o-min-device-pixel-ratio: 2 / 1), only screen and (min-device-pixel-ratio: 2), only screen and (min-resolution: 192dpi), only screen and (min-resolution: 2dppx) {
.ig-b-32 { background-image: url(//badges.instagram.com/static/images/<EMAIL>); background-size: 60px 178px; } }</style>
<a href="http://instagram.com/champions_sg?ref=badge" class="ig-b- ig-b-32"><img src="./Join Our Team_files/ig-badge-32.png" alt="Instagram"></a></div>
</div><div id="wsb-element-00000000-0000-0000-0000-000394044455" class="wsb-element-shape" data-type="element"> <div class="wsb-shape shape_header customStyle shadow_drop_shadow"></div> </div><div id="wsb-element-00000000-0000-0000-0000-000394044440" class="wsb-element-text" data-type="element"> <div class="txt "><p style="text-align: center;"><span style="font-size:48px;"><span style="font-family:verdana,geneva,sans-serif;"><strong><span style="color:#cc0000;"><span style="line-height: 64px;">&nbsp;Champions Sports Grill&nbsp;</span></span></strong></span></span></p></div> </div><div id="wsb-element-66b19782-f356-4e78-8f08-d988bd450fdb" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-66b19782-f356-4e78-8f08-d988bd450fdb"><div data-label-container-groupid="13-desktop" class="form-label-above"><label class="form-label" for="elm-66b19782-f356-4e78-8f08-d988bd450fdb"> Applicant's name: </label></div><div data-field-container-groupid="13-desktop" class="form-field-above"><input type="text" id="elm-66b19782-f356-4e78-8f08-d988bd450fdb" data-groupid="13-desktop" name="elm-66b19782-f356-4e78-8f08-d988bd450fdb" data-label="Applicant&#39;s name:" data-formtype="input" class="form-value" data-content="" placeholder="Enter text here" tabindex="1326"></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000431174186" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000431174186"><div data-label-container-groupid="13-desktop" class="form-label-above"><label class="form-label" for="elm-00000000-0000-0000-0000-000431174186"> I certify that information contained in this application is true and complete. I understand that false information may be grounds for not hiring me or for immediate termination of employment at any point in the future if I am hired. I authorize the verification of any or all information listed above. <span class="form-req">*</span></label></div><div data-field-container-groupid="13-desktop" class="form-field-above"><div class="form-options-vertical"><label data-content=""><input type="radio" name="rd-00000000-0000-0000-0000-000431174186" data-groupid="13-desktop" data-label="I certify that information contained in this application is true and complete. I understand that false information may be grounds for not hiring me or for immediate termination of employment at any point in the future if I am hired. I authorize the verification of any or all information listed above." value="I agree" required="true" data-formtype="radio" tabindex="1325"> I agree </label></div><div class="form-req"></div></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000400224438" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000400224438"><div data-label-container-groupid="13-desktop" class="form-label-above"><label class="form-label" for="elm-00000000-0000-0000-0000-000400224438"> Are you 18 years of age or older: <span class="form-req">*</span></label></div><div data-field-container-groupid="13-desktop" class="form-field-above"><div class="form-options-horizontal"><label data-content=""><input type="radio" name="rd-00000000-0000-0000-0000-000400224438" data-groupid="13-desktop" data-label="Are you 18 years of age or older:" value="Yes" required="true" data-formtype="radio" tabindex="1307"> Yes </label></div><div class="form-options-horizontal"><label data-content=""><input type="radio" name="rd-00000000-0000-0000-0000-000400224438" data-groupid="13-desktop" data-label="Are you 18 years of age or older:" value="No" required="true" data-formtype="radio" tabindex="1308"> No </label></div><div class="form-req"></div></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000400061169" class="wsb-element-text" data-type="element"> <div class="txt "><p style="text-align: center;"><span style="font-size:22px;"><span style="color:#0000CD;"><span style="font-family:kaushan script;">Thank you for your interest to join our team and potentially work in a great &amp; fun establishment.<br></span></span></span></p><p style="text-align: center;"><span style="font-size:22px;"><span style="color:#0000CD;"><span style="font-family:kaushan script;"> One of management members will reply to you.</span></span></span></p><p><br></p><p><br></p><h3 style="text-align: center;">You can also click on the full <span style="font-size:20px;"><strong><span style="color:#FF0000;">Application Form</span></strong></span>&nbsp;below, print,<br></h3><h3 style="text-align: center;"> fill it out completely,&nbsp;<span style="line-height: 1.1; background-color: rgba(0, 0, 0, 0);">and drop it off&nbsp;in person.</span></h3></div> </div><div id="wsb-element-00000000-0000-0000-0000-000399994949" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000399994949"><div data-label-container-groupid="13-desktop" class="form-label-above"><label class="form-label" for="elm-00000000-0000-0000-0000-000399994949"> Previous restaurant/bar experience <span class="form-req">*</span></label></div><div data-field-container-groupid="13-desktop" class="form-field-above"><div class="form-options-horizontal"><label data-content=""><input type="radio" name="rd-00000000-0000-0000-0000-000399994949" data-groupid="13-desktop" data-label="Previous restaurant/bar experience" value="Yes" required="true" data-formtype="radio" tabindex="1320"> Yes </label></div><div class="form-options-horizontal"><label data-content=""><input type="radio" name="rd-00000000-0000-0000-0000-000399994949" data-groupid="13-desktop" data-label="Previous restaurant/bar experience" value="No" required="true" data-formtype="radio" tabindex="1321"> No </label></div><div class="form-req"></div></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000399963440" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000399963440"><div data-label-container-groupid="13-desktop" class="form-label-above"><label class="form-label" for="elm-00000000-0000-0000-0000-000399963440"> Type of employment desired <span class="form-req">*</span></label></div><div data-field-container-groupid="13-desktop" class="form-field-above"><div class="form-options-horizontal"><label data-content=""><input type="checkbox" name="cb-00000000-0000-0000-0000-000399963440" data-groupid="13-desktop" data-label="Type of employment desired" value="Full time" required="true" data-formtype="checkbox" tabindex="1316"> Full time </label></div><div class="form-options-horizontal"><label data-content=""><input type="checkbox" name="cb-00000000-0000-0000-0000-000399963440" data-groupid="13-desktop" data-label="Type of employment desired" value="Part time" required="true" data-formtype="checkbox" tabindex="1317"> Part time </label></div><div class="form-req"></div></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000399960130" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000399960130"><div data-label-container-groupid="13-desktop" class="form-label-above"><label class="form-label" for="elm-00000000-0000-0000-0000-000399960130"> Additional information or comments: </label></div><div data-field-container-groupid="13-desktop" class="form-field-above"><textarea data-groupid="13-desktop" id="elm-00000000-0000-0000-0000-000399960130" class="form-value" data-label="Additional information or comments:" data-formtype="paragraph" placeholder="Enter text here" data-content="" tabindex="1323"></textarea></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000399956195" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000399956195"><div data-label-container-groupid="13-desktop" class="form-label-above"><label class="form-label" for="elm-00000000-0000-0000-0000-000399956195"> Alternate Phone: </label></div><div data-field-container-groupid="13-desktop" class="form-field-above"><input type="text" id="elm-00000000-0000-0000-0000-000399956195" data-groupid="13-desktop" name="elm-00000000-0000-0000-0000-000399956195" data-label="Alternate Phone:" data-formtype="phone" class="form-value" data-content="" placeholder="Enter phone number" tabindex="1306"></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000399956194" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000399956194"><div data-label-container-groupid="13-desktop" class="form-label-above"><label class="form-label" for="elm-00000000-0000-0000-0000-000399956194"> If yes ... Where, When &amp; Position held: </label></div><div data-field-container-groupid="13-desktop" class="form-field-above"><textarea data-groupid="13-desktop" id="elm-00000000-0000-0000-0000-000399956194" class="form-value" data-label="If yes ... Where, When &amp; Position held:" data-formtype="paragraph" placeholder="Enter text here" data-content="" tabindex="1322"></textarea></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000399953513" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000399953513"><div data-label-container-groupid="13-desktop" class="form-label-above"><label class="form-label" for="elm-00000000-0000-0000-0000-000399953513"> Date you can start: <span class="form-req">*</span></label></div><div data-field-container-groupid="13-desktop" class="form-field-above"><span style="position: relative; display: inline-block;"><input type="text" class="form-value datefield sf-datepicker" data-groupid="13-desktop" data-date-elementid="00000000-0000-0000-0000-000399953513" data-subindex="0" data-formtype="date" data-datetime-type="date" data-before-today="false" data-content="" required="true" tabindex="1318" value="7/11/2025" data-placeholder=""><a href="http://www.champions-sportsgrill.com/join-our-team.html#" class="trigger sf-dp-trigger" style="position: absolute; top: 0px; left: 112px; display: block;"><span></span></a></span><input type="hidden" data-groupid="13-desktop" data-label="Date you can start:" data-aid="datetime-value-00000000-0000-0000-0000-000399953513" tabindex="1319"></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000399953512" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000399953512"><div data-label-container-groupid="13-desktop" class="form-label-above"><label class="form-label" for="elm-00000000-0000-0000-0000-000399953512"> Most recent employment history/position: <span class="form-req">*</span></label></div><div data-field-container-groupid="13-desktop" class="form-field-above"><textarea data-groupid="13-desktop" id="elm-00000000-0000-0000-0000-000399953512" class="form-value" data-label="Most recent employment history/position:" data-formtype="paragraph" placeholder="Enter text here" data-content="" required="true" tabindex="1324"></textarea></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000399929044" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000399929044"><div data-label-container-groupid="13-desktop" class="form-label-above"><label class="form-label" for="elm-00000000-0000-0000-0000-000399929044"> Times available to work: <span class="form-req">*</span></label></div><div data-field-container-groupid="13-desktop" class="form-field-above"><div class="form-options-horizontal"><label data-content=""><input type="radio" name="rd-00000000-0000-0000-0000-000399929044" data-groupid="13-desktop" data-label="Times available to work:" value="Days" required="true" data-formtype="radio" tabindex="1313"> Days </label></div><div class="form-options-horizontal"><label data-content=""><input type="radio" name="rd-00000000-0000-0000-0000-000399929044" data-groupid="13-desktop" data-label="Times available to work:" value="Nights" required="true" data-formtype="radio" tabindex="1314"> Nights </label></div><div class="form-options-horizontal"><label data-content=""><input type="radio" name="rd-00000000-0000-0000-0000-000399929044" data-groupid="13-desktop" data-label="Times available to work:" value="Any" required="true" data-formtype="radio" tabindex="1315"> Any </label></div><div class="form-req"></div></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000399929043" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000399929043"><div data-label-container-groupid="13-desktop" class="form-label-above"><label class="form-label" for="elm-00000000-0000-0000-0000-000399929043"> Days available to work: <span class="form-req">*</span></label></div><div data-field-container-groupid="13-desktop" class="form-field-above"><div class="form-options-horizontal"><label data-content=""><input type="radio" name="rd-00000000-0000-0000-0000-000399929043" data-groupid="13-desktop" data-label="Days available to work:" value="Weekdays" required="true" data-formtype="radio" tabindex="1310"> Weekdays </label></div><div class="form-options-horizontal"><label data-content=""><input type="radio" name="rd-00000000-0000-0000-0000-000399929043" data-groupid="13-desktop" data-label="Days available to work:" value="Weekends" required="true" data-formtype="radio" tabindex="1311"> Weekends </label></div><div class="form-options-horizontal"><label data-content=""><input type="radio" name="rd-00000000-0000-0000-0000-000399929043" data-groupid="13-desktop" data-label="Days available to work:" value="Any" required="true" data-formtype="radio" tabindex="1312"> Any </label></div><div class="form-req"></div></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000399907579" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000399907579"><div data-label-container-groupid="13-desktop" class="form-label-above"><label class="form-label" for="elm-00000000-0000-0000-0000-000399907579"> Today's date: <span class="form-req">*</span></label></div><div data-field-container-groupid="13-desktop" class="form-field-above"><span style="position: relative; display: inline-block;"><input type="text" class="form-value datefield sf-tipper-target sf-datepicker" data-groupid="13-desktop" data-date-elementid="00000000-0000-0000-0000-000399907579" data-subindex="0" data-formtype="date" data-datetime-type="date" data-before-today="false" data-content="Enter today&#39;s date" required="true" tabindex="1304" value="7/11/2025" data-placeholder=""><a href="http://www.champions-sportsgrill.com/join-our-team.html#" class="trigger sf-dp-trigger" style="position: absolute; top: 0px; left: 112px; display: block;"><span></span></a></span><input type="hidden" data-groupid="13-desktop" data-label="Today&#39;s date:" data-aid="datetime-value-00000000-0000-0000-0000-000399907579" tabindex="1305"></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000399907578" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000399907578"><div data-label-container-groupid="13-desktop" class="form-label-above"><label class="form-label" for="elm-00000000-0000-0000-0000-000399907578"> Position(s) applied for: <span class="form-req">*</span></label></div><div data-field-container-groupid="13-desktop" class="form-field-above"><input type="text" id="elm-00000000-0000-0000-0000-000399907578" data-groupid="13-desktop" name="elm-00000000-0000-0000-0000-000399907578" data-label="Position(s) applied for:" data-formtype="input" class="form-value sf-tipper-target" data-content="You can name more than one position" placeholder="Enter text here" required="true" tabindex="1309"></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000399882348" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000399882348"><div data-label-container-groupid="13-desktop" class="form-label-above"><label class="form-label" for="elm-00000000-0000-0000-0000-000399882348"> Email: </label></div><div data-field-container-groupid="13-desktop" class="form-field-above"><input type="email" id="elm-00000000-0000-0000-0000-000399882348" data-groupid="13-desktop" name="elm-00000000-0000-0000-0000-000399882348" data-label="Email:" data-formtype="email" data-gemsubmit="true" class="form-value" data-content="" placeholder="Enter email address" tabindex="1301"><label class="form-label opt-in-label"><input type="checkbox" class="opt-in-checkbox"><span class="opt-in">Check here to receive email updates</span></label></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000399882347" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000399882347"><div data-label-container-groupid="13-desktop" class="form-label-above"><label class="form-label" for="elm-00000000-0000-0000-0000-000399882347"> Applicant's name: <span class="form-req">*</span></label></div><div data-field-container-groupid="13-desktop" class="form-field-above"><input type="text" id="elm-00000000-0000-0000-0000-000399882347" data-groupid="13-desktop" name="elm-00000000-0000-0000-0000-000399882347" data-label="Applicant&#39;s name:" data-formtype="input" class="form-value" data-content="" required="true" tabindex="1300"></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000399882346" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000399882346"><div data-label-container-groupid="13-desktop" class="form-label-above"><label class="form-label" for="elm-00000000-0000-0000-0000-000399882346"> Address </label></div><div data-field-container-groupid="13-desktop" class="form-field-above"><input type="text" id="elm-00000000-0000-0000-0000-000399882346" data-groupid="13-desktop" name="elm-00000000-0000-0000-0000-000399882346" data-label="Address" data-formtype="address" class="form-value" data-content="" placeholder="Enter address" tabindex="1302"></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000399882345" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000399882345"><div data-label-container-groupid="13-desktop" class="form-label-above"><label class="form-label" for="elm-00000000-0000-0000-0000-000399882345"> Phone: <span class="form-req">*</span></label></div><div data-field-container-groupid="13-desktop" class="form-field-above"><input type="text" id="elm-00000000-0000-0000-0000-000399882345" data-groupid="13-desktop" name="elm-00000000-0000-0000-0000-000399882345" data-label="Phone:" data-formtype="phone" class="form-value" data-content="" placeholder="Enter phone number" required="true" tabindex="1303"></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000399882344" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000399882344"><div><input type="button" value="Submit" data-groupid="13-desktop" data-aid="submit-00000000-0000-0000-0000-000399882344-desktop" class="form-submit form-button-disabled" data-content="" disabled="disabled" tabindex="1327"></div><div id="formMsgBox-desktop-00000000-0000-0000-0000-000399882344" class="form-message" style="display: none;"> Thank you for contacting us! If needed, you will hear back within 48-72 hours. </div><script type="text/javascript"> require(['designer/app/builder/ui/canvas/elements/customform/customForm.published'], function (customForm) { customForm.initializeSubmitButton({"groupId":"13-desktop","groupIdInt":13,"elementId":"00000000-0000-0000-0000-000399882344","domainName":"champions-sportsgrill.com","resellerId":1,"subject":"champions-sportsgrill.com Join Our Team: Form Submission","showMessage":true,"gemSubmit":false,"postRedirectUrl":null,"renderMode":"desktop","isPreview":false,"mailerUrl":"https://sitesupport-v7.websitetonight.com/api/CustomFormMailer/Submit","labelOrientation":"0","labelCssClass":"form-label-above","fieldCssClass":"form-field-above","messageBoxId":"formMsgBox-desktop-00000000-0000-0000-0000-000399882344","fieldFormatByType":{"input":"Text {num}:","radio":"Multiple Choice {num}:","checkbox":"Checkbox {num}:","dropdown":"Drop Down {num}:","file":"Submitted File {num}:","date":"Date/Time {num}:","address":"Address {num}:","phone":"Phone {num}:","email":"Email Address {num}:"},"requiredValidationMessage":"This is a required field.","lengthValidationMessage":"Field is limited to 100 characters.","longLengthValidationMessage":"Field is limited to 4000 characters.","emailValidationMessage":"Invalid email address.","dateValidationMessage":"Invalid date/time value.","errorTitle":"Try Again","sendErrorMessage":"Unknown error occurred. Please try again.","tooManyRequestsErrorTitle":"Whoa, slow down","tooManyRequestsErrorMessage":"We're working feverishly to process your request. Please wait a few seconds and try again.","websiteId":"00000000-0000-0000-0000-000392878707","orionId":"9d42db59-352e-11e4-af6e-f04da2075117","gemSubmitUrl":"https://apps.api.godaddy.com/v1/apps/madmimi/v1/subscriber","googleMapsClientId":"gme-godaddycom","googleMapsPublishedChannel":"v7-published","mapboxApiKey":"pk.eyJ1IjoiZ29kYWRkeSIsImEiOiJjaWc5b20wcjcwczAydGFsdGxvamdvYnV0In0.JK9HuO6nAzc8BnMv6W7NBQ","isMapboxApiEnabled":true,"googleMapsApiBaseUrl":"js!//maps.googleapis.com/maps/api/js?v=3.27&libraries=places,geometry","mapboxApiBaseUrl":"https://api.mapbox.com/geocoding/v5/mapbox.places/{0}.json?access_token="}); }); </script></div> </div><div id="wsb-element-00000000-0000-0000-0000-000397738817" class="wsb-element-image" data-type="element"> <div class="wsb-image-inner "><div class="customStyle"><img src="./Join Our Team_files/2ccfa39d17737cc00eaabb607354d5e2" style="vertical-align:middle;width:378px;height:310px;"></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000394417348" class="wsb-element-button" data-type="element"> <div><a id="wsb-button-00000000-0000-0000-0000-000394417348" class="wsb-button button_red " href="https://nebula.wsimg.com/a4a7462408577bb177e3df89dcfa4332?AccessKeyId=1549AF287DE5730D6508&amp;disposition=0&amp;alloworigin=1" target="_blank"><span class="button-content wsb-button-content" style="white-space:nowrap">Application<br>Form</span></a></div> </div><div id="wsb-element-00000000-0000-0000-0000-000394399776" class="wsb-element-text" data-type="element"> <div class="txt "><h1 style="text-align: center;"><span style="font-size:36px;"><span style="color:#B22222;"><strong><em>Employment Opportunities ...</em></strong></span></span></h1><p style="text-align: center;"><br></p><h2 style="text-align: center;">Do you have what it takes to be a "<span style="font-size:26px;"><span style="color:#FF0000;"><strong>Champions</strong></span></span>" Team member&nbsp;?</h2><p style="text-align: center;"><br></p><p style="text-align: center;"><span style="font-size:18px;">We are&nbsp;looking for fun, friendly&nbsp;&amp; reliable people to join our team .<br></span></p><p style="text-align: center;"><span style="font-size:18px;">If Interested, please inquire with one of our managers on your next visit.</span><br></p><p style="text-align: center;"><br></p><p style="text-align: center;"> You can also send&nbsp;your resume and/or application to: &nbsp;<span style="font-size:18px;"><strong><EMAIL> </strong></span>&nbsp;or<br></p><p style="text-align: center;">Mail to: <em>Champions Sports Grill, 2212 Sibley Rd., Brownstown, MI 48193</em></p><p style="text-align: center;"><br></p><h2 style="text-align: center;"><span style="font-size:28px;"><span style="font-family:kaushan script;"><strong><span style="color:#0000CD;">Please fill out the form below and click submit:</span></strong></span></span><br></h2><h2><em><br></em></h2><p><br></p><p><br></p><p><br></p></div> </div> </div></div><div id="wsb-canvas-template-footer" class="wsb-canvas-page-footer footer" style="margin: auto; min-height:100px; height: 100px; width: 903px; position: relative;"><div id="wsb-canvas-template-footer-container" class="footer-container" style="position: absolute"> <div id="wsb-element-00000000-0000-0000-0000-000394044459" class="wsb-element-image"> <div class="wsb-image-inner "><div class="img"><a href="https://www.godaddy.com/websites/website-builder?cvosrc=assets.wsb_badge.wsb_badge" rel=""><img src="./Join Our Team_files/fab625f9c7d3d65638893f3418a0e7d9" style="vertical-align:middle;width:98px;height:35px;"></a></div></div> </div> </div></div><div class="view-as-mobile" style="padding:10px;position:relative;text-align:center;display:none;"><a href="http://www.champions-sportsgrill.com/join-our-team.html#" onclick="return false;">View on Mobile</a></div></div></div><script type="text/javascript"> require(['jquery', 'common/cookiemanager/cookiemanager', 'designer/iebackground/iebackground'], function ($, cookieManager, bg) { if (cookieManager.getCookie("WSB.ForceDesktop")) { $('.view-as-mobile', '.wsb-canvas-page-container').show().find('a').bind('click', function () { cookieManager.eraseCookie("WSB.ForceDesktop"); window.location.reload(true); }); } bg.fixBackground(); }); </script><script> "undefined" === typeof _trfq || (window._trfq = []); "undefined" === typeof _trfd && (window._trfd = []), _trfd.push({ "ap": "WSBv7" }); </script><script src="./Join Our Team_files/scc-c2.min.js.download" async=""></script> <div class="sflayer sflayer-flyout" data-sflayer="flyout" data-sflayerdeep="flyout" id="sflayer-flyout"><div class="sf-qt" data-style="qt"><span class="sf-qt-content"></span><span class="sf-qt-arrow-wrap"><span class="sf-qt-arrow"></span></span></div><div class="sf-qt" data-style="qt"><span class="sf-qt-content"></span><span class="sf-qt-arrow-wrap"><span class="sf-qt-arrow"></span></span></div><span id="dp-style40098" class="sf-dp-wrapper dp-wrapper" style="height: 32px; display: block; position: absolute; top: 1357.2px; left: 579.9px;"><div class="dhtmlxcalendar_container dhtmlxcalendar_skin_gd dhtmlxcalendar_time_hidden" style="left: 0px; top: 0px; display: none; position: absolute;"><div class="dhtmlxcalendar_month_cont"><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_month_hdr"><div class="dhtmlxcalendar_month_arrow dhtmlxcalendar_month_arrow_left" onmouseover="this.className=&quot;dhtmlxcalendar_month_arrow dhtmlxcalendar_month_arrow_left_hover&quot;;" onmouseout="this.className=&quot;dhtmlxcalendar_month_arrow dhtmlxcalendar_month_arrow_left&quot;;"></div><span class="dhtmlxcalendar_month_label_month">July</span><span class="dhtmlxcalendar_month_label_year">2025</span><div class="dhtmlxcalendar_month_arrow dhtmlxcalendar_month_arrow_right" onmouseover="this.className=&quot;dhtmlxcalendar_month_arrow dhtmlxcalendar_month_arrow_right_hover&quot;;" onmouseout="this.className=&quot;dhtmlxcalendar_month_arrow dhtmlxcalendar_month_arrow_right&quot;;"></div></li></ul></div><div class="dhtmlxcalendar_days_cont"><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_day_weekday_cell_first">Su</li><li class="dhtmlxcalendar_cell">Mo</li><li class="dhtmlxcalendar_cell">Tu</li><li class="dhtmlxcalendar_cell">We</li><li class="dhtmlxcalendar_cell">Th</li><li class="dhtmlxcalendar_cell">Fr</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_day_weekday_cell">Sa</li></ul></div><div class="dhtmlxcalendar_dates_cont"><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_weekend_dis">29</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_dis">30</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_dis">1</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_dis">2</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_dis">3</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_dis">4</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_weekend_dis">5</li></ul><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_weekend_dis">6</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_dis">7</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_dis">8</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_dis">9</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_dis">10</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_date_holiday">11</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_weekend">12</li></ul><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_weekend">13</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">14</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">15</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">16</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">17</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">18</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_weekend">19</li></ul><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_weekend">20</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">21</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">22</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">23</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">24</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">25</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_weekend">26</li></ul><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_weekend">27</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">28</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">29</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">30</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">31</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell">1</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_weekend">2</li></ul><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_weekend">3</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell">4</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell">5</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell">6</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell">7</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell">8</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_weekend">9</li></ul></div><div class="dhtmlxcalendar_time_cont"><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_time_hdr"><div class="dhtmlxcalendar_time_label"></div><span class="dhtmlxcalendar_label_hours">00</span><span class="dhtmlxcalendar_label_colon">:</span><span class="dhtmlxcalendar_label_minutes">00</span></li></ul></div><a class="today" href="http://www.champions-sportsgrill.com/join-our-team.html#"></a></div></span><span id="dp-style47042" class="sf-dp-wrapper dp-wrapper" style="height: 32px; display: block; position: absolute; top: 914.2px; left: 579.9px;"><div class="dhtmlxcalendar_container dhtmlxcalendar_skin_gd dhtmlxcalendar_time_hidden" style="left: 0px; top: 0px; display: none; position: absolute;"><div class="dhtmlxcalendar_month_cont"><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_month_hdr"><div class="dhtmlxcalendar_month_arrow dhtmlxcalendar_month_arrow_left" onmouseover="this.className=&quot;dhtmlxcalendar_month_arrow dhtmlxcalendar_month_arrow_left_hover&quot;;" onmouseout="this.className=&quot;dhtmlxcalendar_month_arrow dhtmlxcalendar_month_arrow_left&quot;;"></div><span class="dhtmlxcalendar_month_label_month">July</span><span class="dhtmlxcalendar_month_label_year">2025</span><div class="dhtmlxcalendar_month_arrow dhtmlxcalendar_month_arrow_right" onmouseover="this.className=&quot;dhtmlxcalendar_month_arrow dhtmlxcalendar_month_arrow_right_hover&quot;;" onmouseout="this.className=&quot;dhtmlxcalendar_month_arrow dhtmlxcalendar_month_arrow_right&quot;;"></div></li></ul></div><div class="dhtmlxcalendar_days_cont"><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_day_weekday_cell_first">Su</li><li class="dhtmlxcalendar_cell">Mo</li><li class="dhtmlxcalendar_cell">Tu</li><li class="dhtmlxcalendar_cell">We</li><li class="dhtmlxcalendar_cell">Th</li><li class="dhtmlxcalendar_cell">Fr</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_day_weekday_cell">Sa</li></ul></div><div class="dhtmlxcalendar_dates_cont"><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_weekend_dis">29</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_dis">30</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_dis">1</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_dis">2</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_dis">3</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_dis">4</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_weekend_dis">5</li></ul><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_weekend_dis">6</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_dis">7</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_dis">8</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_dis">9</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_dis">10</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_date_holiday">11</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_weekend">12</li></ul><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_weekend">13</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">14</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">15</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">16</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">17</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">18</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_weekend">19</li></ul><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_weekend">20</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">21</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">22</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">23</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">24</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">25</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_weekend">26</li></ul><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_weekend">27</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">28</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">29</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">30</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">31</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell">1</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_weekend">2</li></ul><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_weekend">3</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell">4</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell">5</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell">6</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell">7</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell">8</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_weekend">9</li></ul></div><div class="dhtmlxcalendar_time_cont"><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_time_hdr"><div class="dhtmlxcalendar_time_label"></div><span class="dhtmlxcalendar_label_hours">00</span><span class="dhtmlxcalendar_label_colon">:</span><span class="dhtmlxcalendar_label_minutes">00</span></li></ul></div><a class="today" href="http://www.champions-sportsgrill.com/join-our-team.html#"></a></div></span></div></body></html>