<!DOCTYPE html>
<!-- saved from url=(0055)http://www.champions-sportsgrill.com/private-event.html -->
<html lang="en" dir="ltr" data-tcc-ignore=""><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><title>Private Event</title><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><link rel="stylesheet" type="text/css" href="./Private Event_files/site.css"><script> if (typeof ($sf) === "undefined") { $sf = { baseUrl: "https://p3pprd001.cloudstorage.secureserver.net/wsbv7-assets/WSB7_J_20250303_2130_DEP-03042_5487/v2", skin: "app", preload: 0, require: { jquery: "https://p3pprd001.cloudstorage.secureserver.net/wsbv7-assets/WSB7_J_20250303_2130_DEP-03042_5487/v2/libs/jquery/jq.js", paths: { "wsbcore": "common/wsb/core", "knockout": "libs/knockout/knockout" } } }; } </script><script id="duel" src="./Private Event_files/duel.js.download"></script><script charset="utf-8" async="" src="./Private Event_files/jq.js.download"></script><script> define('jquery', ['jq!starfield/jquery.mod'], function(m) { return m; }); define('appconfig', [], { documentDownloadBaseUrl: 'https://nebula.wsimg.com' }); </script><meta http-equiv="Content-Location" content="private-event.html"><meta name="generator" content="Starfield Technologies; Go Daddy Website Builder 7.0.5350"><meta property="og:type" content="website"><meta property="og:title" content="Private Event"><meta property="og:site_name" content="Champions Sports Grilll"><meta property="og:url" content="http://www.champions-sportsgrill.com/private-event.html"><meta property="og:image" content="https://nebula.wsimg.com/040c179fde2ede1a16c6d547cb667115?AccessKeyId=1549AF287DE5730D6508&amp;disposition=0&amp;alloworigin=1"><script charset="utf-8" async="" src="./Private Event_files/customForm.published.js.download"></script><script charset="utf-8" async="" src="./Private Event_files/media.gallery.js.download"></script><script charset="utf-8" async="" src="./Private Event_files/cookiemanager.js.download"></script><script charset="utf-8" async="" src="./Private Event_files/iebackground.js.download"></script><script charset="utf-8" async="" src="./Private Event_files/regexhelper.js.download"></script><script charset="utf-8" async="" src="./Private Event_files/api.guid.js.download"></script><script charset="utf-8" async="" src="./Private Event_files/jquery.xDomainRequest.js.download"></script><script charset="utf-8" async="" src="./Private Event_files/tipper.js.download"></script><script charset="utf-8" async="" src="./Private Event_files/datepicker.js.download"></script><script charset="utf-8" async="" src="./Private Event_files/jquery.watermark.js.download"></script><script charset="utf-8" async="" src="./Private Event_files/util.instances.js.download"></script><script charset="utf-8" async="" src="./Private Event_files/util.model.js.download"></script><script charset="utf-8" async="" src="./Private Event_files/documentHelper.js.download"></script><script charset="utf-8" async="" src="./Private Event_files/util.window.js.download"></script><script charset="utf-8" async="" src="./Private Event_files/sf.tipper.js.download"></script><script charset="utf-8" async="" src="./Private Event_files/sf.datepicker.js.download"></script><script charset="utf-8" async="" src="./Private Event_files/sf.core.pkg.js.download"></script><link rel="stylesheet" type="text/css" _curl_movable="true" href="./Private Event_files/app.css"><script charset="utf-8" async="" src="./Private Event_files/sf.core.pkg.js.download"></script><link rel="stylesheet" type="text/css" _curl_movable="true" href="./Private Event_files/app(1).css"><link rel="stylesheet" href="chrome-extension://ihcjicgdanjaechkgeegckofjjedodee/app/content-style.css"></head><body cz-shortcut-listen="true"><style data-inline-fonts="">/* vietnamese */
@font-face {
  font-family: 'Allura';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/allura/v22/9oRPNYsQpS4zjuA_hAgWDto.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Allura';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/allura/v22/9oRPNYsQpS4zjuA_hQgWDto.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Allura';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/allura/v22/9oRPNYsQpS4zjuA_iwgW.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic */
@font-face {
  font-family: 'Amatic SC';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/amaticsc/v27/TUZyzwprpvBS1izr_vOEDuSfQZQ.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* hebrew */
@font-face {
  font-family: 'Amatic SC';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/amaticsc/v27/TUZyzwprpvBS1izr_vOECOSfQZQ.woff2) format('woff2');
  unicode-range: U+0307-0308, U+0590-05FF, U+200C-2010, U+20AA, U+25CC, U+FB1D-FB4F;
}
/* vietnamese */
@font-face {
  font-family: 'Amatic SC';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/amaticsc/v27/TUZyzwprpvBS1izr_vOEBeSfQZQ.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Amatic SC';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/amaticsc/v27/TUZyzwprpvBS1izr_vOEBOSfQZQ.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Amatic SC';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/amaticsc/v27/TUZyzwprpvBS1izr_vOECuSf.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* vietnamese */
@font-face {
  font-family: 'Arizonia';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/arizonia/v22/neIIzCemt4A5qa7mv5WOFqwKUQ.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Arizonia';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/arizonia/v22/neIIzCemt4A5qa7mv5WPFqwKUQ.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Arizonia';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/arizonia/v22/neIIzCemt4A5qa7mv5WBFqw.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin */
@font-face {
  font-family: 'Averia Sans Libre';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/averiasanslibre/v20/ga6XaxZG_G5OvCf_rt7FH3B6BHLMEdVOEoI.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin */
@font-face {
  font-family: 'Cabin Sketch';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/cabinsketch/v22/QGYpz_kZZAGCONcK2A4bGOj8mNhN.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* vietnamese */
@font-face {
  font-family: 'Francois One';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/francoisone/v21/_Xmr-H4zszafZw3A-KPSZut9zgiRi_Y.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Francois One';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/francoisone/v21/_Xmr-H4zszafZw3A-KPSZut9zwiRi_Y.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Francois One';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/francoisone/v21/_Xmr-H4zszafZw3A-KPSZut9wQiR.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Fredericka the Great';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/frederickathegreat/v22/9Bt33CxNwt7aOctW2xjbCstzwVKsIBVV--StxbcVcg.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Fredericka the Great';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/frederickathegreat/v22/9Bt33CxNwt7aOctW2xjbCstzwVKsIBVV--Sjxbc.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin */
@font-face {
  font-family: 'Jacques Francois Shadow';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/jacquesfrancoisshadow/v26/KR1FBtOz8PKTMk-kqdkLVrvR0ECFrB6Pin-2_p8Suno.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin */
@font-face {
  font-family: 'Josefin Slab';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/josefinslab/v28/lW-swjwOK3Ps5GSJlNNkMalNpiZe_ldbOR4W71msR349Kg.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Kaushan Script';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/kaushanscript/v18/vm8vdRfvXFLG3OLnsO15WYS5DG72wNJHMw.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Kaushan Script';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/kaushanscript/v18/vm8vdRfvXFLG3OLnsO15WYS5DG74wNI.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Love Ya Like A Sister';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/loveyalikeasister/v22/R70EjzUBlOqPeouhFDfR80-0FhOqJubN-BeL-3xdgGE.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Love Ya Like A Sister';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/loveyalikeasister/v22/R70EjzUBlOqPeouhFDfR80-0FhOqJubN-BeL9Xxd.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Merriweather';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/merriweather/v32/u-4D0qyriQwlOrhSvowK_l5UcA6zuSYEqOzpPe3HOZJ5eX1WtLaQwmYiScCmDxhtNOKl8yDr3icaGV31GvU.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Merriweather';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/merriweather/v32/u-4D0qyriQwlOrhSvowK_l5UcA6zuSYEqOzpPe3HOZJ5eX1WtLaQwmYiScCmDxhtNOKl8yDr3icaEF31GvU.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Merriweather';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/merriweather/v32/u-4D0qyriQwlOrhSvowK_l5UcA6zuSYEqOzpPe3HOZJ5eX1WtLaQwmYiScCmDxhtNOKl8yDr3icaG131GvU.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Merriweather';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/merriweather/v32/u-4D0qyriQwlOrhSvowK_l5UcA6zuSYEqOzpPe3HOZJ5eX1WtLaQwmYiScCmDxhtNOKl8yDr3icaGl31GvU.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Merriweather';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/merriweather/v32/u-4D0qyriQwlOrhSvowK_l5UcA6zuSYEqOzpPe3HOZJ5eX1WtLaQwmYiScCmDxhtNOKl8yDr3icaFF31.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Offside';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/offside/v25/HI_KiYMWKa9QrAykc5joR6-d.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Offside';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/offside/v25/HI_KiYMWKa9QrAykc5boRw.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4taVIGxA.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4kaVIGxA.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4saVIGxA.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4jaVIGxA.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* hebrew */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4iaVIGxA.woff2) format('woff2');
  unicode-range: U+0307-0308, U+0590-05FF, U+200C-2010, U+20AA, U+25CC, U+FB1D-FB4F;
}
/* math */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B5caVIGxA.woff2) format('woff2');
  unicode-range: U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;
}
/* symbols */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B5OaVIGxA.woff2) format('woff2');
  unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
}
/* vietnamese */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4vaVIGxA.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4uaVIGxA.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  src: url(https://img1.wsimg.com/gfonts/s/opensans/v43/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4gaVI.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Oswald';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/oswald/v56/TK3_WkUHHAIjg75cFRf3bXL8LICs1_FvsUtiZTaR.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Oswald';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/oswald/v56/TK3_WkUHHAIjg75cFRf3bXL8LICs1_FvsUJiZTaR.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Oswald';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/oswald/v56/TK3_WkUHHAIjg75cFRf3bXL8LICs1_FvsUliZTaR.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Oswald';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/oswald/v56/TK3_WkUHHAIjg75cFRf3bXL8LICs1_FvsUhiZTaR.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Oswald';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/oswald/v56/TK3_WkUHHAIjg75cFRf3bXL8LICs1_FvsUZiZQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Over the Rainbow';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/overtherainbow/v22/11haGoXG1k_HKhMLUWz7Mc7vvW5ulvqs9eA2.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Over the Rainbow';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/overtherainbow/v22/11haGoXG1k_HKhMLUWz7Mc7vvW5ulvSs9Q.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Pacifico';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/pacifico/v22/FwZY7-Qmy14u9lezJ-6K6MmTpA.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Pacifico';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/pacifico/v22/FwZY7-Qmy14u9lezJ-6D6MmTpA.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Pacifico';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/pacifico/v22/FwZY7-Qmy14u9lezJ-6I6MmTpA.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Pacifico';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/pacifico/v22/FwZY7-Qmy14u9lezJ-6J6MmTpA.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Pacifico';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/pacifico/v22/FwZY7-Qmy14u9lezJ-6H6Mk.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Romanesco';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/romanesco/v21/w8gYH2ozQOY7_r_J7mSX1XYKmOo.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Romanesco';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/romanesco/v21/w8gYH2ozQOY7_r_J7mSX23YK.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Sacramento';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sacramento/v16/buEzpo6gcdjy0EiZMBUG4CMf_exL.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Sacramento';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sacramento/v16/buEzpo6gcdjy0EiZMBUG4C0f_Q.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Seaweed Script';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/seaweedscript/v16/bx6cNx6Tne2pxOATYE8C_Rsoe3WA8qY2VQ.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Seaweed Script';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/seaweedscript/v16/bx6cNx6Tne2pxOATYE8C_Rsoe3WO8qY.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Special Elite';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/specialelite/v19/XLYgIZbkc4JPUL5CVArUVL0ntn4OSEFt.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Special Elite';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/specialelite/v19/XLYgIZbkc4JPUL5CVArUVL0ntnAOSA.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* cyrillic-ext */
@font-face {
  font-family: 'Bitter';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8fbfOLXOXWh2.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Bitter';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8fbfOLzOXWh2.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* vietnamese */
@font-face {
  font-family: 'Bitter';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8fbfOLfOXWh2.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Bitter';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8fbfOLbOXWh2.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Bitter';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/bitter/v39/raxhHiqOu8IVPmnRc6SY1KXhnF_Y8fbfOLjOXQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sourcesanspro/v22/6xK3dSBYKcSV-LCoeQqfX1RYOo3qNa7lqDY.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sourcesanspro/v22/6xK3dSBYKcSV-LCoeQqfX1RYOo3qPK7lqDY.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sourcesanspro/v22/6xK3dSBYKcSV-LCoeQqfX1RYOo3qNK7lqDY.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sourcesanspro/v22/6xK3dSBYKcSV-LCoeQqfX1RYOo3qO67lqDY.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sourcesanspro/v22/6xK3dSBYKcSV-LCoeQqfX1RYOo3qN67lqDY.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sourcesanspro/v22/6xK3dSBYKcSV-LCoeQqfX1RYOo3qNq7lqDY.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: url(https://img1.wsimg.com/gfonts/s/sourcesanspro/v22/6xK3dSBYKcSV-LCoeQqfX1RYOo3qOK7l.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
</style><style type="text/css"> #wsb-element-00000000-0000-0000-0000-000394044455{top:-246px;left:-5px;position:absolute;z-index:11}#wsb-element-00000000-0000-0000-0000-000394044455 .wsb-shape{width:914px;height:227px;-webkit-border-radius:10px;-moz-border-radius:10px;-o-border-radius:10px;border-radius:10px;padding:0px;background:#fff;-moz-opacity:0.97;-khtml-opacity:0.97;opacity:0.97;box-sizing:content-box;-moz-box-sizing:content-box}#wsb-element-00000000-0000-0000-0000-000394044440{top:-106px;left:46px;position:absolute;z-index:13}#wsb-element-00000000-0000-0000-0000-000394044440 .txt{width:822px;height:69px}#wsb-element-b9b305e9-ec74-45d2-a097-1b7dfda5e690{top:3007px;left:363px;position:absolute;z-index:90}#wsb-element-b9b305e9-ec74-45d2-a097-1b7dfda5e690 .wsb-button{width:165px;height:40px}#wsb-element-74a3af61-8d15-45c8-9590-91ccbf1b6eee{top:1955px;left:124px;position:absolute;z-index:92}#wsb-element-74a3af61-8d15-45c8-9590-91ccbf1b6eee .wsb-image-inner{-webkit-border-radius:38px;-moz-border-radius:38px;-o-border-radius:38px;border-radius:38px;padding:0px}#wsb-element-74a3af61-8d15-45c8-9590-91ccbf1b6eee .wsb-image-inner div{width:702px;height:878px;position:relative;overflow:hidden}#wsb-element-74a3af61-8d15-45c8-9590-91ccbf1b6eee img{position:absolute;-webkit-border-radius:38px;-moz-border-radius:38px;-o-border-radius:38px;border-radius:38px}#wsb-element-00000000-0000-0000-0000-000602046936{top:2956px;left:242px;position:absolute;z-index:84}#wsb-element-00000000-0000-0000-0000-000602046936 .txt{width:423px;height:31px}#wsb-element-00000000-0000-0000-0000-000545232617{top:2890px;left:125px;position:absolute;z-index:63}#wsb-element-00000000-0000-0000-0000-000545232617 .txt{width:667px;height:69px}#wsb-element-00000000-0000-0000-0000-000545227561{top:1880px;left:173px;position:absolute;z-index:62}#wsb-element-00000000-0000-0000-0000-000545227561 .txt{width:574px;height:53px}#wsb-element-00000000-0000-0000-0000-000545223511{top:1785px;left:82px;position:absolute;z-index:61}#wsb-element-00000000-0000-0000-0000-000545223511 .txt{width:753px;height:64px}#wsb-element-00000000-0000-0000-0000-000400440250{top:2865px;left:171px;position:absolute;z-index:56}#wsb-element-00000000-0000-0000-0000-000400440250 .wsb-line-element{width:587px;height:25px}#wsb-element-00000000-0000-0000-0000-000400440249{top:3208px;left:277px;position:absolute;z-index:57}#wsb-element-00000000-0000-0000-0000-000400440249 .wsb-image-inner{-webkit-border-radius:34px;-moz-border-radius:34px;-o-border-radius:34px;border-radius:34px;padding:0px;-moz-opacity:0.88;-khtml-opacity:0.88;opacity:0.88}#wsb-element-00000000-0000-0000-0000-000400440249 .wsb-image-inner div{width:388px;height:296px;position:relative;overflow:hidden}#wsb-element-00000000-0000-0000-0000-000400440249 img{position:absolute;-webkit-border-radius:34px;-moz-border-radius:34px;-o-border-radius:34px;border-radius:34px}#wsb-element-00000000-0000-0000-0000-000400426391{top:848px;left:302px;position:absolute;z-index:99}#wsb-element-00000000-0000-0000-0000-000400426391>div.form-row{width:300px;height:172px;padding:0px}#wsb-element-00000000-0000-0000-0000-000400426390{top:654px;left:302px;position:absolute;z-index:99}#wsb-element-00000000-0000-0000-0000-000400426390>div.form-row{width:300px;height:186px;padding:0px}#wsb-element-00000000-0000-0000-0000-000400417778{top:425px;left:302px;position:absolute;z-index:99}#wsb-element-00000000-0000-0000-0000-000400417778>div.form-row{width:300px;height:10px;padding:0px}#wsb-element-00000000-0000-0000-0000-000400417777{top:360px;left:302px;position:absolute;z-index:99}#wsb-element-00000000-0000-0000-0000-000400417777>div.form-row{width:300px;height:65px;padding:0px}#wsb-element-00000000-0000-0000-0000-000400417776{top:581px;left:302px;position:absolute;z-index:99}#wsb-element-00000000-0000-0000-0000-000400417776>div.form-row{width:300px;height:10px;padding:0px}#wsb-element-00000000-0000-0000-0000-000400417775{top:1168px;left:302px;position:absolute;z-index:99}#wsb-element-00000000-0000-0000-0000-000400417775>div.form-row{width:300px;height:69px;padding:0px}#wsb-element-00000000-0000-0000-0000-000400417774{top:515px;left:302px;position:absolute;z-index:99}#wsb-element-00000000-0000-0000-0000-000400417774>div.form-row{width:300px;height:65px;padding:0px}#wsb-element-1d53a788-ef68-482e-86c6-e35dd58bfa2b{top:1703px;left:31px;position:absolute;z-index:94}#wsb-element-1d53a788-ef68-482e-86c6-e35dd58bfa2b .txt{width:824px;height:67px}#wsb-element-00000000-0000-0000-0000-000634621630{top:1257px;left:141px;position:absolute;z-index:86}#wsb-element-00000000-0000-0000-0000-000634621630>div{width:654px;height:436px}#wsb-element-00000000-0000-0000-0000-000400438841{top:3070px;left:56px;position:absolute;z-index:55}#wsb-element-00000000-0000-0000-0000-000400438841 .txt{width:790px;height:118px}#wsb-element-00000000-0000-0000-0000-000400432657{top:286px;left:147px;position:absolute;z-index:54}#wsb-element-00000000-0000-0000-0000-000400432657 .wsb-line-element{width:587px;height:25px}#wsb-element-00000000-0000-0000-0000-000400431350{top:997px;left:302px;position:absolute;z-index:99}#wsb-element-00000000-0000-0000-0000-000400431350>div.form-row{width:300px;height:85px;padding:0px}#wsb-element-00000000-0000-0000-0000-000400431349{top:1083px;left:302px;position:absolute;z-index:99}#wsb-element-00000000-0000-0000-0000-000400431349>div.form-row{width:300px;height:85px;padding:0px}#wsb-element-00000000-0000-0000-0000-000397709222{top:58px;left:51px;position:absolute;z-index:48}#wsb-element-00000000-0000-0000-0000-000397709222 .txt{width:796px;height:212px}#wsb-element-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9{top:-215px;left:46px;position:absolute;z-index:12}#wsb-element-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9{width:769px;height:98px}#wsb-element-00000000-0000-0000-0000-000401601928{top:-106px;left:858px;position:absolute;z-index:52}#wsb-element-00000000-0000-0000-0000-000401601928 .wsb-htmlsnippet-element{width:41px;height:40px;overflow:hidden;margin:auto}#wsb-element-00000000-0000-0000-0000-000394044459{top:48px;left:328px;position:absolute;z-index:47}#wsb-element-00000000-0000-0000-0000-000394044459 .wsb-image-inner{}#wsb-element-00000000-0000-0000-0000-000394044459 .wsb-image-inner div{width:98px;height:35px;position:relative;overflow:hidden}#wsb-element-00000000-0000-0000-0000-000394044459 img{position:absolute} </style><div class="wsb-canvas body"><div class="wsb-canvas-page-container" style="min-height: 100%; padding-top: 254px; position: relative;"><div class="wsb-canvas-scrollable" style="filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=&#39;#bf0000&#39;, endColorstr=&#39;#ffffff&#39;,GradientType=1 ); background-image: url(https://nebula.wsimg.com/7104c9f53a311b825478fb0ee132c3be?AccessKeyId=531592D248B589D87A56&amp;alloworigin=1); background: url(https://nebula.wsimg.com/7104c9f53a311b825478fb0ee132c3be?AccessKeyId=531592D248B589D87A56&amp;alloworigin=1), -moz-linear-gradient(left, #bf0000 0%, #ffffff 100%);; background: url(https://nebula.wsimg.com/7104c9f53a311b825478fb0ee132c3be?AccessKeyId=531592D248B589D87A56&amp;alloworigin=1), -webkit-gradient(linear, left top, right top, color-stop(0%,#bf0000), color-stop(100%,#ffffff)); background: url(https://nebula.wsimg.com/7104c9f53a311b825478fb0ee132c3be?AccessKeyId=531592D248B589D87A56&amp;alloworigin=1), -webkit-linear-gradient(left, #bf0000 0%, #ffffff 100%); background: url(https://nebula.wsimg.com/7104c9f53a311b825478fb0ee132c3be?AccessKeyId=531592D248B589D87A56&amp;alloworigin=1), -o-linear-gradient(left, #bf0000 0%,#ffffff 100%); background: url(https://nebula.wsimg.com/7104c9f53a311b825478fb0ee132c3be?AccessKeyId=531592D248B589D87A56&amp;alloworigin=1), -ms-linear-gradient(left, #bf0000 0%,#ffffff 100%); background: url(https://nebula.wsimg.com/7104c9f53a311b825478fb0ee132c3be?AccessKeyId=531592D248B589D87A56&amp;alloworigin=1), linear-gradient(to right, #bf0000 0%,#ffffff 100%); background-position-x: left; background-position-y: top; background-position: left top; background-repeat: repeat; position: absolute; width: 100%; height: 100%;"></div><div id="wsb-canvas-template-page" class="wsb-canvas-page page" style="height: 3504px; margin: auto; width: 903px; background-color: #ff8d02; position: relative; "><div id="wsb-canvas-template-container" style="position: absolute;"> <div id="wsb-element-00000000-0000-0000-0000-000394044455" class="wsb-element-shape" data-type="element"> <div class="wsb-shape shape_header customStyle shadow_drop_shadow"></div> </div><div id="wsb-element-00000000-0000-0000-0000-000394044440" class="wsb-element-text" data-type="element"> <div class="txt "><p style="text-align: center;"><span style="font-size:48px;"><span style="font-family:verdana,geneva,sans-serif;"><strong><span style="color:#cc0000;"><span style="line-height: 64px;">&nbsp;Champions Sports Grill&nbsp;</span></span></strong></span></span></p></div> </div><div id="wsb-element-b9b305e9-ec74-45d2-a097-1b7dfda5e690" class="wsb-element-button" data-type="element"> <div><a id="wsb-button-b9b305e9-ec74-45d2-a097-1b7dfda5e690" class="wsb-button button_red " href="https://nebula.wsimg.com/d366f572029b7b8d83b4d3dbd51af6aa?AccessKeyId=1549AF287DE5730D6508&amp;disposition=0&amp;alloworigin=1"><span class="button-content wsb-button-content" style="white-space:nowrap">Click Here</span></a></div> </div><div id="wsb-element-74a3af61-8d15-45c8-9590-91ccbf1b6eee" class="wsb-element-image" data-type="element"> <div class="wsb-image-inner "><div class="customStyle"><img src="./Private Event_files/7a0386da70fed57e7617b405aad5256e" style="vertical-align:middle;width:702px;height:878px;"></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000602046936" class="wsb-element-text" data-type="element"> <div class="txt "><p style="text-align: center;"><span style="font-size:22px;"><strong><span style="color:#000080;"></span><span style="color:#008000;"><em>Click button below to print BANQUET MENU</em></span></strong></span></p></div> </div><div id="wsb-element-00000000-0000-0000-0000-000545232617" class="wsb-element-text" data-type="element"> <div class="txt "><p style="text-align: center;"><em><span style="font-size:14px;">Item selection &amp; prices subject to change.</span></em></p><p style="text-align: center;"><span style="font-size:14px;"><em>Prices&nbsp;do not include room fee, beverages,&nbsp;taxes, and/or gratuities.</em><br></span></p><p style="text-align: center;"><span style="font-size:14px;"><em>Minimum number of guests &amp; items ordered required for private room reservation.</em></span></p></div> </div><div id="wsb-element-00000000-0000-0000-0000-000545227561" class="wsb-element-text" data-type="element"> <div class="txt "><h1 style="text-align: center;"><span style="font-size:48px;"><span style="color:#FF0000;"><em>Banquet&nbsp; Menu Options</em></span></span></h1></div> </div><div id="wsb-element-00000000-0000-0000-0000-000545223511" class="wsb-element-text" data-type="element"> <div class="txt "><p style="text-align: center;"><span style="font-size:22px;"><span style="font-family:lucida sans unicode,lucida grande,sans-serif;"><span style="color:#0000CD;">Below are some of the more&nbsp;popular&nbsp;menu options that we offer.<br></span></span></span></p><p style="text-align: center;"><span style="font-size:22px;"><span style="font-family:lucida sans unicode,lucida grande,sans-serif;"><span style="color:#0000CD;"> &nbsp;We can cater your event with&nbsp;other items&nbsp;upon request.&nbsp;</span></span></span></p></div> </div><div id="wsb-element-00000000-0000-0000-0000-000400440250" class="wsb-element-line" data-type="element"> <div class="wsb-line-element" style="width: 587px; height: 25px; width: 587px;border-top: 5px dashed #ff0000;opacity: 1;filter: alpha(opacity=1);"></div> </div><div id="wsb-element-00000000-0000-0000-0000-000400440249" class="wsb-element-image" data-type="element"> <div class="wsb-image-inner "><div class="customStyle"><img src="./Private Event_files/4783028a686debb7c203f521aeed911c" style="vertical-align:middle;width:388px;height:296px;"></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000400426391" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000400426391"><div data-label-container-groupid="6-desktop" class="form-label-above"><label class="form-label" for="elm-00000000-0000-0000-0000-000400426391"> Expected number of guests for your event: <span class="form-req">*</span></label></div><div data-field-container-groupid="6-desktop" class="form-field-above"><div class="form-options-vertical"><label data-content=""><input type="checkbox" name="cb-00000000-0000-0000-0000-000400426391" data-groupid="6-desktop" data-label="Expected number of guests for your event:" value="20-30" required="true" data-formtype="checkbox" tabindex="620"> 20-30 </label></div><div class="form-options-vertical"><label data-content=""><input type="checkbox" name="cb-00000000-0000-0000-0000-000400426391" data-groupid="6-desktop" data-label="Expected number of guests for your event:" value="30-40" required="true" data-formtype="checkbox" tabindex="621"> 30-40 </label></div><div class="form-options-vertical"><label data-content=""><input type="checkbox" name="cb-00000000-0000-0000-0000-000400426391" data-groupid="6-desktop" data-label="Expected number of guests for your event:" value="40-50" required="true" data-formtype="checkbox" tabindex="622"> 40-50 </label></div><div class="form-options-vertical"><label data-content=""><input type="checkbox" name="cb-00000000-0000-0000-0000-000400426391" data-groupid="6-desktop" data-label="Expected number of guests for your event:" value="50-60" required="true" data-formtype="checkbox" tabindex="623"> 50-60 </label></div><div class="form-options-vertical"><label data-content=""><input type="checkbox" name="cb-00000000-0000-0000-0000-000400426391" data-groupid="6-desktop" data-label="Expected number of guests for your event:" value="61 or more" required="true" data-formtype="checkbox" tabindex="624"> 61 or more </label></div><div class="form-options-vertical"><label data-content=""><input type="checkbox" name="cb-00000000-0000-0000-0000-000400426391" data-groupid="6-desktop" data-label="Expected number of guests for your event:" value="Other" required="true" data-formtype="checkbox" tabindex="625"> Other </label></div><div class="form-req"></div></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000400426390" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000400426390"><div data-label-container-groupid="6-desktop" class="form-label-above"><label class="form-label" for="elm-00000000-0000-0000-0000-000400426390"> Type of event: <span class="form-req">*</span></label></div><div data-field-container-groupid="6-desktop" class="form-field-above"><div class="form-options-horizontal"><label data-content=""><input type="radio" name="rd-00000000-0000-0000-0000-000400426390" data-groupid="6-desktop" data-label="Type of event:" value="Birthday" required="true" data-formtype="radio" tabindex="606"> Birthday </label></div><div class="form-options-horizontal"><label data-content=""><input type="radio" name="rd-00000000-0000-0000-0000-000400426390" data-groupid="6-desktop" data-label="Type of event:" value="Benefit/Fundraiser" required="true" data-formtype="radio" tabindex="607"> Benefit/Fundraiser </label></div><div class="form-options-horizontal"><label data-content=""><input type="radio" name="rd-00000000-0000-0000-0000-000400426390" data-groupid="6-desktop" data-label="Type of event:" value="Bridal shower" required="true" data-formtype="radio" tabindex="608"> Bridal shower </label></div><div class="form-options-horizontal"><label data-content=""><input type="radio" name="rd-00000000-0000-0000-0000-000400426390" data-groupid="6-desktop" data-label="Type of event:" value="Baby shower" required="true" data-formtype="radio" tabindex="609"> Baby shower </label></div><div class="form-options-horizontal"><label data-content=""><input type="radio" name="rd-00000000-0000-0000-0000-000400426390" data-groupid="6-desktop" data-label="Type of event:" value="Bachelorette party" required="true" data-formtype="radio" tabindex="610"> Bachelorette party </label></div><div class="form-options-horizontal"><label data-content=""><input type="radio" name="rd-00000000-0000-0000-0000-000400426390" data-groupid="6-desktop" data-label="Type of event:" value="Bachelor party" required="true" data-formtype="radio" tabindex="611"> Bachelor party </label></div><div class="form-options-horizontal"><label data-content=""><input type="radio" name="rd-00000000-0000-0000-0000-000400426390" data-groupid="6-desktop" data-label="Type of event:" value="Graduation" required="true" data-formtype="radio" tabindex="612"> Graduation </label></div><div class="form-options-horizontal"><label data-content=""><input type="radio" name="rd-00000000-0000-0000-0000-000400426390" data-groupid="6-desktop" data-label="Type of event:" value="Retirement" required="true" data-formtype="radio" tabindex="613"> Retirement </label></div><div class="form-options-horizontal"><label data-content=""><input type="radio" name="rd-00000000-0000-0000-0000-000400426390" data-groupid="6-desktop" data-label="Type of event:" value="Anniversary" required="true" data-formtype="radio" tabindex="614"> Anniversary </label></div><div class="form-options-horizontal"><label data-content=""><input type="radio" name="rd-00000000-0000-0000-0000-000400426390" data-groupid="6-desktop" data-label="Type of event:" value="Work meeting" required="true" data-formtype="radio" tabindex="615"> Work meeting </label></div><div class="form-options-horizontal"><label data-content=""><input type="radio" name="rd-00000000-0000-0000-0000-000400426390" data-groupid="6-desktop" data-label="Type of event:" value="Holiday gathering" required="true" data-formtype="radio" tabindex="616"> Holiday gathering </label></div><div class="form-options-horizontal"><label data-content=""><input type="radio" name="rd-00000000-0000-0000-0000-000400426390" data-groupid="6-desktop" data-label="Type of event:" value="School reunion" required="true" data-formtype="radio" tabindex="617"> School reunion </label></div><div class="form-options-horizontal"><label data-content=""><input type="radio" name="rd-00000000-0000-0000-0000-000400426390" data-groupid="6-desktop" data-label="Type of event:" value="Family reunion" required="true" data-formtype="radio" tabindex="618"> Family reunion </label></div><div class="form-options-horizontal"><label data-content=""><input type="radio" name="rd-00000000-0000-0000-0000-000400426390" data-groupid="6-desktop" data-label="Type of event:" value="Other" required="true" data-formtype="radio" tabindex="619"> Other </label></div><div class="form-req"></div></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000400417778" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000400417778"><div data-label-container-groupid="6-desktop" class="form-label-above"><label class="form-label" for="elm-00000000-0000-0000-0000-000400417778"> Email: <span class="form-req">*</span></label></div><div data-field-container-groupid="6-desktop" class="form-field-above"><input type="email" id="elm-00000000-0000-0000-0000-000400417778" data-groupid="6-desktop" name="elm-00000000-0000-0000-0000-000400417778" data-label="Email:" data-formtype="email" data-gemsubmit="true" class="form-value" data-content="" placeholder="Enter email address" required="true" tabindex="601"><label class="form-label opt-in-label"><input type="checkbox" class="opt-in-checkbox"><span class="opt-in">Check here to receive email updates</span></label></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000400417777" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000400417777"><div data-label-container-groupid="6-desktop" class="form-label-above"><label class="form-label" for="elm-00000000-0000-0000-0000-000400417777"> Name: <span class="form-req">*</span></label></div><div data-field-container-groupid="6-desktop" class="form-field-above"><input type="text" id="elm-00000000-0000-0000-0000-000400417777" data-groupid="6-desktop" name="elm-00000000-0000-0000-0000-000400417777" data-label="Name:" data-formtype="input" class="form-value" data-content="" required="true" data-namefield="true" tabindex="600"></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000400417776" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000400417776"><div data-label-container-groupid="6-desktop" class="form-label-above"><label class="form-label" for="elm-00000000-0000-0000-0000-000400417776"> Date and time of your event <span class="form-req">*</span></label></div><div data-field-container-groupid="6-desktop" class="form-field-above"><span style="position: relative; display: inline-block;"><input type="text" class="form-value datefield sf-datepicker" data-groupid="6-desktop" data-date-elementid="00000000-0000-0000-0000-000400417776" data-subindex="0" data-formtype="date" data-datetime-type="date" data-before-today="false" data-content="" required="true" tabindex="603" value="7/11/2025" data-placeholder=""><a href="http://www.champions-sportsgrill.com/private-event.html#" class="trigger sf-dp-trigger" style="position: absolute; top: 0px; left: 112px; display: block;"><span></span></a></span><select class="form-value timefield indent" data-groupid="6-desktop" data-formtype="date" data-datetime-type="time" data-date-elementid="00000000-0000-0000-0000-000400417776" data-subindex="1" data-content="" required="true" tabindex="604"><option value="12:00 AM">12:00 AM </option><option value="12:30 AM">12:30 AM </option><option value="1:00 AM">1:00 AM </option><option value="1:30 AM">1:30 AM </option><option value="2:00 AM">2:00 AM </option><option value="2:30 AM">2:30 AM </option><option value="3:00 AM">3:00 AM </option><option value="3:30 AM">3:30 AM </option><option value="4:00 AM">4:00 AM </option><option value="4:30 AM">4:30 AM </option><option value="5:00 AM">5:00 AM </option><option value="5:30 AM">5:30 AM </option><option value="6:00 AM">6:00 AM </option><option value="6:30 AM">6:30 AM </option><option value="7:00 AM">7:00 AM </option><option value="7:30 AM">7:30 AM </option><option value="8:00 AM">8:00 AM </option><option value="8:30 AM">8:30 AM </option><option value="9:00 AM">9:00 AM </option><option value="9:30 AM">9:30 AM </option><option value="10:00 AM">10:00 AM </option><option value="10:30 AM">10:30 AM </option><option value="11:00 AM">11:00 AM </option><option value="11:30 AM">11:30 AM </option><option value="12:00 PM" selected="">12:00 PM </option><option value="12:30 PM">12:30 PM </option><option value="1:00 PM">1:00 PM </option><option value="1:30 PM">1:30 PM </option><option value="2:00 PM">2:00 PM </option><option value="2:30 PM">2:30 PM </option><option value="3:00 PM">3:00 PM </option><option value="3:30 PM">3:30 PM </option><option value="4:00 PM">4:00 PM </option><option value="4:30 PM">4:30 PM </option><option value="5:00 PM">5:00 PM </option><option value="5:30 PM">5:30 PM </option><option value="6:00 PM">6:00 PM </option><option value="6:30 PM">6:30 PM </option><option value="7:00 PM">7:00 PM </option><option value="7:30 PM">7:30 PM </option><option value="8:00 PM">8:00 PM </option><option value="8:30 PM">8:30 PM </option><option value="9:00 PM">9:00 PM </option><option value="9:30 PM">9:30 PM </option><option value="10:00 PM">10:00 PM </option><option value="10:30 PM">10:30 PM </option><option value="11:00 PM">11:00 PM </option><option value="11:30 PM">11:30 PM </option></select><input type="hidden" data-groupid="6-desktop" data-label="Date and time of your event" data-aid="datetime-value-00000000-0000-0000-0000-000400417776" tabindex="605"></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000400417775" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000400417775"><div><input type="button" value="Submit" data-groupid="6-desktop" data-aid="submit-00000000-0000-0000-0000-000400417775-desktop" class="form-submit form-button-disabled" data-content="" disabled="disabled" tabindex="628"></div><div id="formMsgBox-desktop-00000000-0000-0000-0000-000400417775" class="form-message" style="display: none;"> Thank you for contacting us! If needed, you will hear back within 48-72 hours. </div><script type="text/javascript"> require(['designer/app/builder/ui/canvas/elements/customform/customForm.published'], function (customForm) { customForm.initializeSubmitButton({"groupId":"6-desktop","groupIdInt":6,"elementId":"00000000-0000-0000-0000-000400417775","domainName":"champions-sportsgrill.com","resellerId":1,"subject":"champions-sportsgrill.com Private Event: Form Submission","showMessage":true,"gemSubmit":false,"postRedirectUrl":null,"renderMode":"desktop","isPreview":false,"mailerUrl":"https://sitesupport-v7.websitetonight.com/api/CustomFormMailer/Submit","labelOrientation":"0","labelCssClass":"form-label-above","fieldCssClass":"form-field-above","messageBoxId":"formMsgBox-desktop-00000000-0000-0000-0000-000400417775","fieldFormatByType":{"input":"Text {num}:","radio":"Multiple Choice {num}:","checkbox":"Checkbox {num}:","dropdown":"Drop Down {num}:","file":"Submitted File {num}:","date":"Date/Time {num}:","address":"Address {num}:","phone":"Phone {num}:","email":"Email Address {num}:"},"requiredValidationMessage":"This is a required field.","lengthValidationMessage":"Field is limited to 100 characters.","longLengthValidationMessage":"Field is limited to 4000 characters.","emailValidationMessage":"Invalid email address.","dateValidationMessage":"Invalid date/time value.","errorTitle":"Try Again","sendErrorMessage":"Unknown error occurred. Please try again.","tooManyRequestsErrorTitle":"Whoa, slow down","tooManyRequestsErrorMessage":"We're working feverishly to process your request. Please wait a few seconds and try again.","websiteId":"00000000-0000-0000-0000-000392878707","orionId":"9d42db59-352e-11e4-af6e-f04da2075117","gemSubmitUrl":"https://apps.api.godaddy.com/v1/apps/madmimi/v1/subscriber","googleMapsClientId":"gme-godaddycom","googleMapsPublishedChannel":"v7-published","mapboxApiKey":"pk.eyJ1IjoiZ29kYWRkeSIsImEiOiJjaWc5b20wcjcwczAydGFsdGxvamdvYnV0In0.JK9HuO6nAzc8BnMv6W7NBQ","isMapboxApiEnabled":true,"googleMapsApiBaseUrl":"js!//maps.googleapis.com/maps/api/js?v=3.27&libraries=places,geometry","mapboxApiBaseUrl":"https://api.mapbox.com/geocoding/v5/mapbox.places/{0}.json?access_token="}); }); </script></div> </div><div id="wsb-element-00000000-0000-0000-0000-000400417774" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000400417774"><div data-label-container-groupid="6-desktop" class="form-label-above"><label class="form-label" for="elm-00000000-0000-0000-0000-000400417774"> Phone: <span class="form-req">*</span></label></div><div data-field-container-groupid="6-desktop" class="form-field-above"><input type="text" id="elm-00000000-0000-0000-0000-000400417774" data-groupid="6-desktop" name="elm-00000000-0000-0000-0000-000400417774" data-label="Phone:" data-formtype="phone" class="form-value" data-content="" placeholder="Enter phone number" required="true" tabindex="602"></div></div> </div><div id="wsb-element-1d53a788-ef68-482e-86c6-e35dd58bfa2b" class="wsb-element-text" data-type="element"> <div class="txt "><p style="text-align: center;">Banquet room tables can be setup to whatever fits your needs best. Guest also have the option to bring in their own linens (or rent from us for&nbsp;an added cost&nbsp;based on availability), as well as their own decoration allowed per the contract.&nbsp;</p><p style="text-align: center;">All banquet room events will require a room fee to reserve the date. Final cost will also include &nbsp;gratuity and sales tax added.</p></div> </div><div id="wsb-element-00000000-0000-0000-0000-000634621630" class="wsb-element-gallery" data-type="element"> <div class="false "><div id="desktop-00000000-0000-0000-0000-000634621630" class="wsb-media-gallery" style="height: 436px; width: 654px;"><div class="wsb-media-gallery-arrows-left-arrow" style="top: 196px;"></div><ul class="wsb-media-gallery-slider bordered" style="margin-left: 22px; width: 580px; height: 406.4px;"><li class="" style="width: 580px; left: 0px; z-index: 87; display: none;"><img src="./Private Event_files/040c179fde2ede1a16c6d547cb667115" class="autosize" style="width: 580px; height: auto; left: 0px; top: -14.5px;"><div class="wsb-media-gallery-caption" style="width: 580px;">Vegetable Platter</div></li><li class="" style="width: 580px; left: 0px; z-index: 87; display: list-item;"><img src="./Private Event_files/37786564b937fe9e0ba1e168e560dd11" class="autosize" style="width: 580px; height: auto; left: 0px; top: -14.5px;"><div class="wsb-media-gallery-caption" style="width: 580px;">Fruit &amp; Cheese Platter</div></li><li class="loading" style="width: 580px; left: 580px;"></li><li class="" style="width: 580px; left: 580px;"><img src="./Private Event_files/30be24f37a67b3c8c6ba43147f4edf2b" class="autosize" style="width: 580px; height: auto; left: 0px; top: -14.5px;"><div class="wsb-media-gallery-caption" style="width: 580px;">Buffet Setup</div></li><li class="loading" style="width: 580px; left: 580px;"></li><li class="loading" style="width: 580px; left: 580px;"></li><li class="loading" style="width: 580px; left: 580px;"></li><li class="loading" style="width: 580px; left: 580px;"></li><li class="loading" style="width: 580px; left: 580px;"></li><li class="loading" style="width: 580px; left: 580px;"></li><li class="loading" style="width: 580px; left: 580px;"></li><li class="loading" style="width: 580px; left: 580px;"></li><li class="" style="width: 580px; left: 580px;"><img src="./Private Event_files/2ff9dd3db2894a3ee1150dbe3dd6f7d6" class="autosize" style="width: 580px; height: auto; left: 0px; top: -14.5px;"></li><li class="" style="width: 580px; left: 580px;"><img src="./Private Event_files/05d461ba7298de1a6d3bb4b7f4ab1a0d" class="autosize" style="width: 580px; height: auto; left: 0px; top: -14.5px;"></li></ul><div class="wsb-media-gallery-arrows-right-arrow" style="top: 196px;"></div></div></div><script type="text/javascript"> require(['designer/app/builder/ui/controls/media/gallery/media.gallery'], function (gallery) { var $element = $('#desktop-00000000-0000-0000-0000-000634621630.wsb-media-gallery'); var model = { ID: '00000000-0000-0000-0000-000634621630', mode: 'desktop', preview: false, Layer: 86, Width: '654px', Height: '436px', GalleryAssets: [{"id":"040c179fde2ede1a16c6d547cb667115:1549AF287DE5730D6508","src":"https://nebula.wsimg.com/040c179fde2ede1a16c6d547cb667115?AccessKeyId=1549AF287DE5730D6508&disposition=0&alloworigin=1","caption":"Vegetable Platter","link":"","type":"image"},{"id":"37786564b937fe9e0ba1e168e560dd11:1549AF287DE5730D6508","src":"https://nebula.wsimg.com/37786564b937fe9e0ba1e168e560dd11?AccessKeyId=1549AF287DE5730D6508&disposition=0&alloworigin=1","caption":"Fruit & Cheese Platter","link":"","type":"image"},{"id":"996f42ef82b2f2255b60bde6b07d2718:1549AF287DE5730D6508","src":"https://nebula.wsimg.com/996f42ef82b2f2255b60bde6b07d2718?AccessKeyId=1549AF287DE5730D6508&disposition=0&alloworigin=1","caption":"Hummus Dip & Pita Chips Platter","link":"","type":"image"},{"id":"30be24f37a67b3c8c6ba43147f4edf2b:1549AF287DE5730D6508","src":"https://nebula.wsimg.com/30be24f37a67b3c8c6ba43147f4edf2b?AccessKeyId=1549AF287DE5730D6508&disposition=0&alloworigin=1","caption":"Buffet Setup","link":"","type":"image"},{"id":"392c8f5358ef35cf91a8af0b91ad1091:1549AF287DE5730D6508","src":"https://nebula.wsimg.com/392c8f5358ef35cf91a8af0b91ad1091?AccessKeyId=1549AF287DE5730D6508&disposition=0&alloworigin=1","caption":"Buffet Setup","link":"","type":"image"},{"id":"70bbd3b0a242efdffc8fcc4e08c1adb4:1549AF287DE5730D6508","src":"https://nebula.wsimg.com/70bbd3b0a242efdffc8fcc4e08c1adb4?AccessKeyId=1549AF287DE5730D6508&disposition=0&alloworigin=1","caption":"Vegetable & Cheese Platter","link":"","type":"image"},{"id":"4b2017e920e1b353187fd92eae085ef5:1549AF287DE5730D6508","src":"https://nebula.wsimg.com/4b2017e920e1b353187fd92eae085ef5?AccessKeyId=1549AF287DE5730D6508&disposition=0&alloworigin=1","caption":"Traditional Wings","link":"","type":"image"},{"id":"1a179b3517f3396de8dc627a454d163d:1549AF287DE5730D6508","src":"https://nebula.wsimg.com/1a179b3517f3396de8dc627a454d163d?AccessKeyId=1549AF287DE5730D6508&disposition=0&alloworigin=1","caption":"Buffet Setup","link":"","type":"image"},{"id":"a6f079f80361d317a5c2f7a1f30e78d0:1549AF287DE5730D6508","src":"https://nebula.wsimg.com/a6f079f80361d317a5c2f7a1f30e78d0?AccessKeyId=1549AF287DE5730D6508&disposition=0&alloworigin=1","caption":"","link":"","type":"image"},{"id":"855973c5266ab4376eeeaeb3b06b9451:1549AF287DE5730D6508","src":"https://nebula.wsimg.com/855973c5266ab4376eeeaeb3b06b9451?AccessKeyId=1549AF287DE5730D6508&disposition=0&alloworigin=1","caption":"","link":"","type":"image"},{"id":"b8d1196a1b1105beea383fd0c2a8edae:1549AF287DE5730D6508","src":"https://nebula.wsimg.com/b8d1196a1b1105beea383fd0c2a8edae?AccessKeyId=1549AF287DE5730D6508&disposition=0&alloworigin=1","caption":"","link":"","type":"image"},{"id":"62f8b80677dc1fa5be0d5639ce7d165b:1549AF287DE5730D6508","src":"https://nebula.wsimg.com/62f8b80677dc1fa5be0d5639ce7d165b?AccessKeyId=1549AF287DE5730D6508&disposition=0&alloworigin=1","caption":"","link":"","type":"image"},{"id":"2ff9dd3db2894a3ee1150dbe3dd6f7d6:1549AF287DE5730D6508","src":"https://nebula.wsimg.com/2ff9dd3db2894a3ee1150dbe3dd6f7d6?AccessKeyId=1549AF287DE5730D6508&disposition=0&alloworigin=1","caption":"","link":"","type":"image"},{"id":"05d461ba7298de1a6d3bb4b7f4ab1a0d:1549AF287DE5730D6508","src":"https://nebula.wsimg.com/05d461ba7298de1a6d3bb4b7f4ab1a0d?AccessKeyId=1549AF287DE5730D6508&disposition=0&alloworigin=1","caption":"","link":"","type":"image"}], GalleryAutoStart: true, GalleryCaption: true, GalleryAutoSize: true, GallerySpeed: 4, GalleryTheme: 0, GalleryTransition: 'Fade' }; gallery.render($element, model); }); </script> </div><div id="wsb-element-00000000-0000-0000-0000-000400438841" class="wsb-element-text" data-type="element"> <div class="txt "><h1 style="text-align: center;"><span style="color:#0000CD;"><span style="font-size:28px;"><span style="font-family:kaushan script;">Thank you for your interest in holding your event at Champions.</span></span><br></span></h1><h1 style="text-align: center;"><span style="color:#0000CD;"><span style="font-size:28px;"><span style="font-family:kaushan script;"> We will contact you and make all arrangements for your event </span></span><br></span></h1><h1 style="text-align: center;"><span style="color:#0000CD;"><span style="font-size:28px;"><span style="font-family:kaushan script;">to make it one that you will always remember !!!</span></span></span></h1><p><br></p></div> </div><div id="wsb-element-00000000-0000-0000-0000-000400432657" class="wsb-element-line" data-type="element"> <div class="wsb-line-element" style="width: 587px; height: 25px; width: 587px;border-top: 5px solid #ff0000;opacity: 1;filter: alpha(opacity=1);"></div> </div><div id="wsb-element-00000000-0000-0000-0000-000400431350" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000400431350"><div data-label-container-groupid="6-desktop" class="form-label-above"><label class="form-label" for="elm-00000000-0000-0000-0000-000400431350"> Additional items/requests for your event: </label></div><div data-field-container-groupid="6-desktop" class="form-field-above"><textarea data-groupid="6-desktop" id="elm-00000000-0000-0000-0000-000400431350" class="form-value" data-label="Additional items/requests for your event:" data-formtype="paragraph" placeholder="Enter text here" data-content="" tabindex="626"></textarea></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000400431349" class="wsb-element-customform" data-type="element"> <div class="form customform form-row allow-select" data-aid="elm-container-00000000-0000-0000-0000-000400431349"><div data-label-container-groupid="6-desktop" class="form-label-above"><label class="form-label" for="elm-00000000-0000-0000-0000-000400431349"> Other comments/information: </label></div><div data-field-container-groupid="6-desktop" class="form-field-above"><textarea data-groupid="6-desktop" id="elm-00000000-0000-0000-0000-000400431349" class="form-value" data-label="Other comments/information:" data-formtype="paragraph" placeholder="Enter text here" data-content="" tabindex="627"></textarea></div></div> </div><div id="wsb-element-00000000-0000-0000-0000-000397709222" class="wsb-element-text" data-type="element"> <div class="txt "><h1 style="text-align: center;"><span style="font-size:28px;"><span style="color:#FF0000;">Birthdays, Meetings, Showers, Holiday Gatherings,&nbsp;&nbsp;<br></span></span></h1><h1 style="text-align: center;"><span style="font-size:28px;"><span style="color:#FF0000;">or any other&nbsp;celebration !!!</span></span></h1><p style="text-align: center;"><br></p><p style="text-align: center;"><em><span style="font-size:20px;">We have a private room available for all your events, with a full private bar and dining. </span></em><br></p><p style="text-align: center;"><em><span style="font-size:20px;">Contact us for availability and let us handle all the details for you.&nbsp;</span></em></p><p style="text-align: center;"><br></p><h2 style="text-align: center;"><span style="color:#0000CD;"><span style="font-family:kaushan script;">Please fill out the form below &amp;&nbsp;submit,&nbsp;<span style="line-height: 1.1; background-color: rgba(0, 0, 0, 0);">to inquire about holding your event at&nbsp;</span><span style="line-height: 1.1; background-color: rgba(0, 0, 0, 0);">Champions, and we will be sure to get back with you.</span></span></span></h2></div> </div><div id="wsb-element-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9" class="wsb-element-navigation" data-type="element"> <div style="width: 769px; height: 98px;" class="wsb-nav nav_theme nav-text-center nav-horizontal nav-btn-right wsb-navigation-rendered-top-level-container" id="wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9"><style> #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container ul > li > a {color:#0000ff;} #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container ul > li:hover, #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container ul > li:hover > a, #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container ul > li.active:hover, #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container ul > li.active > a:hover, #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container ul > li.active .nav-subnav li:hover, #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container ul > li.active .nav-subnav li:hover > a {background-color: !important;color:#ff0000 !important;} #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container > ul.wsb-navigation-rendered-top-level-menu > li.active, #wsb-nav-a8ff5ddf-8a5b-4531-b481-a7c06fec02a9.wsb-navigation-rendered-top-level-container > ul.wsb-navigation-rendered-top-level-menu > li.active > a {background-image:none;background-color:#ffff56;color:#bf0000;} </style><ul class="wsb-navigation-rendered-top-level-menu "><li style="width: auto"><a href="http://www.champions-sportsgrill.com/home.html" target="" data-title="Home" data-pageid="00000000-0000-0000-0000-000000026933" data-url="home.html">Home</a></li><li style="width: auto"><a href="http://www.champions-sportsgrill.com/photo-gallery.html" target="" data-title="Photo Gallery" data-pageid="00000000-0000-0000-0000-000000054257" data-url="photo-gallery.html">Photo Gallery</a></li><li style="width: auto"><a href="http://www.champions-sportsgrill.com/menu.html" target="" data-title="Menu" data-pageid="00000000-0000-0000-0000-000008564320" data-url="menu.html">Menu</a></li><li style="width: auto"><a href="http://www.champions-sportsgrill.com/location---contact.html" target="" data-title="Location &amp; Contact" data-pageid="00000000-0000-0000-0000-000000054270" data-url="location---contact.html">Location &amp; Contact</a></li><li style="width: auto"><a href="http://www.champions-sportsgrill.com/join-our-team.html" target="" data-title="Join Our Team" data-pageid="00000000-0000-0000-0000-000394047957" data-url="join-our-team.html">Join Our Team</a></li><li style="width: auto"><a href="http://www.champions-sportsgrill.com/feedback---comments.html" target="" data-title="Feedback &amp; Comments" data-pageid="00000000-0000-0000-0000-000394420685" data-url="feedback---comments.html">Feedback &amp; Comments</a></li><li style="width: auto"><a href="http://www.champions-sportsgrill.com/specials---coupons.html" target="" data-title="Specials &amp; Coupons" data-pageid="00000000-0000-0000-0000-000394443923" data-url="specials---coupons.html">Specials &amp; Coupons</a></li><li style="width: auto"><a href="http://www.champions-sportsgrill.com/happenings.html" target="" data-title="Happenings" data-pageid="00000000-0000-0000-0000-000394444207" data-url="happenings.html">Happenings</a></li><li style="width: auto" class="active"><a href="http://www.champions-sportsgrill.com/private-event.html" target="" data-title="Private Event" data-pageid="00000000-0000-0000-0000-000394858045" data-url="private-event.html">Private Event</a></li><li style="width: auto"><a href="http://www.champions-sportsgrill.com/donations--fundraisers----sponsorships.html" target="" data-title="Donations, Fundraisers, &amp; Sponsorships" data-pageid="bc305eb3-d88f-4eee-8d25-9a89e574ea91" data-url="donations--fundraisers----sponsorships.html">Donations, Fundraisers, &amp; Sponsorships</a></li></ul></div> </div><div id="wsb-element-00000000-0000-0000-0000-000401601928" class="wsb-element-htmlsnippet" data-type="element">




        <div class="wsb-htmlsnippet-element"><style>.ig-b- { display: inline-block; }
.ig-b- img { visibility: hidden; }
.ig-b-:hover { background-position: 0 -60px; } .ig-b-:active { background-position: 0 -120px; }
.ig-b-32 { width: 32px; height: 32px; background: url(//badges.instagram.com/static/images/ig-badge-sprite-32.png) no-repeat 0 0; }
@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min--moz-device-pixel-ratio: 2), only screen and (-o-min-device-pixel-ratio: 2 / 1), only screen and (min-device-pixel-ratio: 2), only screen and (min-resolution: 192dpi), only screen and (min-resolution: 2dppx) {
.ig-b-32 { background-image: url(//badges.instagram.com/static/images/<EMAIL>); background-size: 60px 178px; } }</style>
<a href="http://instagram.com/champions_sg?ref=badge" class="ig-b- ig-b-32"><img src="./Private Event_files/ig-badge-32.png" alt="Instagram"></a></div>
</div> </div></div><div id="wsb-canvas-template-footer" class="wsb-canvas-page-footer footer" style="margin: auto; min-height:100px; height: 100px; width: 903px; position: relative;"><div id="wsb-canvas-template-footer-container" class="footer-container" style="position: absolute"> <div id="wsb-element-00000000-0000-0000-0000-000394044459" class="wsb-element-image"> <div class="wsb-image-inner "><div class="img"><a href="https://www.godaddy.com/websites/website-builder?cvosrc=assets.wsb_badge.wsb_badge" rel=""><img src="./Private Event_files/fab625f9c7d3d65638893f3418a0e7d9" style="vertical-align:middle;width:98px;height:35px;"></a></div></div> </div> </div></div><div class="view-as-mobile" style="padding:10px;position:relative;text-align:center;display:none;"><a href="http://www.champions-sportsgrill.com/private-event.html#" onclick="return false;">View on Mobile</a></div></div></div><script type="text/javascript"> require(['jquery', 'common/cookiemanager/cookiemanager', 'designer/iebackground/iebackground'], function ($, cookieManager, bg) { if (cookieManager.getCookie("WSB.ForceDesktop")) { $('.view-as-mobile', '.wsb-canvas-page-container').show().find('a').bind('click', function () { cookieManager.eraseCookie("WSB.ForceDesktop"); window.location.reload(true); }); } bg.fixBackground(); }); </script><script> "undefined" === typeof _trfq || (window._trfq = []); "undefined" === typeof _trfd && (window._trfd = []), _trfd.push({ "ap": "WSBv7" }); </script><script src="./Private Event_files/scc-c2.min.js.download" async=""></script> <div class="sflayer sflayer-flyout" data-sflayer="flyout" data-sflayerdeep="flyout" id="sflayer-flyout"><span id="dp-style63537" class="sf-dp-wrapper dp-wrapper" style="height: 32px; display: block; position: absolute; top: 870.2px; left: 610.9px;"><div class="dhtmlxcalendar_container dhtmlxcalendar_skin_gd dhtmlxcalendar_time_hidden" style="left: 0px; top: 0px; display: none; position: absolute;"><div class="dhtmlxcalendar_month_cont"><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_month_hdr"><div class="dhtmlxcalendar_month_arrow dhtmlxcalendar_month_arrow_left" onmouseover="this.className=&quot;dhtmlxcalendar_month_arrow dhtmlxcalendar_month_arrow_left_hover&quot;;" onmouseout="this.className=&quot;dhtmlxcalendar_month_arrow dhtmlxcalendar_month_arrow_left&quot;;"></div><span class="dhtmlxcalendar_month_label_month">July</span><span class="dhtmlxcalendar_month_label_year">2025</span><div class="dhtmlxcalendar_month_arrow dhtmlxcalendar_month_arrow_right" onmouseover="this.className=&quot;dhtmlxcalendar_month_arrow dhtmlxcalendar_month_arrow_right_hover&quot;;" onmouseout="this.className=&quot;dhtmlxcalendar_month_arrow dhtmlxcalendar_month_arrow_right&quot;;"></div></li></ul></div><div class="dhtmlxcalendar_days_cont"><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_day_weekday_cell_first">Su</li><li class="dhtmlxcalendar_cell">Mo</li><li class="dhtmlxcalendar_cell">Tu</li><li class="dhtmlxcalendar_cell">We</li><li class="dhtmlxcalendar_cell">Th</li><li class="dhtmlxcalendar_cell">Fr</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_day_weekday_cell">Sa</li></ul></div><div class="dhtmlxcalendar_dates_cont"><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_weekend_dis">29</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_dis">30</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_dis">1</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_dis">2</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_dis">3</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_dis">4</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_weekend_dis">5</li></ul><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_weekend_dis">6</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_dis">7</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_dis">8</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_dis">9</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_dis">10</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_date_holiday">11</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_weekend">12</li></ul><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_weekend">13</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">14</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">15</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">16</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">17</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">18</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_weekend">19</li></ul><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_weekend">20</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">21</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">22</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">23</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">24</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">25</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_weekend">26</li></ul><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month_weekend">27</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">28</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">29</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">30</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_month">31</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell">1</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_weekend">2</li></ul><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_weekend">3</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell">4</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell">5</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell">6</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell">7</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell">8</li><li class="dhtmlxcalendar_cell dhtmlxcalendar_cell_weekend">9</li></ul></div><div class="dhtmlxcalendar_time_cont"><ul class="dhtmlxcalendar_line"><li class="dhtmlxcalendar_cell dhtmlxcalendar_time_hdr"><div class="dhtmlxcalendar_time_label"></div><span class="dhtmlxcalendar_label_hours">00</span><span class="dhtmlxcalendar_label_colon">:</span><span class="dhtmlxcalendar_label_minutes">00</span></li></ul></div><a class="today" href="http://www.champions-sportsgrill.com/private-event.html#"></a></div></span></div></body></html>