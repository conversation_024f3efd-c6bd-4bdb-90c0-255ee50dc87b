# Champions Sports Bar & Grill - Git Ignore File

# PHP
*.log
*.tmp
*.temp
*.cache

# Database
*.db
*.sqlite
*.sqlite3

# Configuration files with sensitive data
config/database.php.local
.env
.env.local
.env.production

# Backup files
*.backup
*.bak
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Node modules (if using any build tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Composer
vendor/
composer.lock

# Logs
logs/
*.log
error.log
access.log

# Uploads directory (keep structure but ignore uploaded files)
uploads/*
!uploads/.gitkeep
assets/images/uploads/*
!assets/images/uploads/.gitkeep

# Cache directories
cache/
tmp/
temp/

# Session files
sessions/

# Development files
test-config.php
phpinfo.php
debug.php

# Database dumps
*.sql.gz
*.sql.bz2
database-backup-*.sql

# Admin data directory (contains SQLite database)
admin/data/

# Sensitive configuration
admin/config/database.php.production
config.production.php

# Error logs
php_errors.log
error_log

# Apache/Nginx logs
access_log
error_log

# SSL certificates
*.pem
*.key
*.crt
*.csr

# Deployment scripts
deploy.sh
deploy.php

# Local development
.htaccess.local
robots.txt.local

# Backup directories
backups/
backup-*/

# Temporary files
*.tmp
*.temp
*.swp

# Package files
*.zip
*.tar.gz
*.rar

# Documentation builds
docs/_build/

# Coverage reports
coverage/
*.coverage

# Testing
phpunit.xml.local
tests/_output/

# Local environment
.local/
local/
