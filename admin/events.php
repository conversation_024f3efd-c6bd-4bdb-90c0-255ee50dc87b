<?php
/**
 * Champions Sports Bar & Grill - Events Management
 */

require_once 'includes/auth.php';
require_once 'config/database.php';

// Check if user is logged in
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$db = Database::getInstance();
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // CSRF protection
        if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
            throw new Exception('Invalid CSRF token');
        }

        $action = $_POST['action'] ?? '';
        
        if ($action === 'add') {
            $title = trim($_POST['title'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $eventDate = trim($_POST['event_date'] ?? '');
            $eventTime = trim($_POST['event_time'] ?? '');
            $category = trim($_POST['category'] ?? '');
            $isRecurring = isset($_POST['is_recurring']) ? 1 : 0;
            $recurringType = trim($_POST['recurring_type'] ?? '');
            $isFeatured = isset($_POST['is_featured']) ? 1 : 0;
            $isActive = isset($_POST['is_active']) ? 1 : 0;
            
            if (empty($title)) {
                throw new Exception('Event title is required');
            }
            
            if (empty($eventDate)) {
                throw new Exception('Event date is required');
            }
            
            if (empty($category)) {
                throw new Exception('Event category is required');
            }
            
            // Handle file upload
            $imageUrl = null;
            if (isset($_FILES['image_file']) && $_FILES['image_file']['error'] === UPLOAD_ERR_OK) {
                $uploadDir = '../assets/images/events/';
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0755, true);
                }
                
                $fileInfo = pathinfo($_FILES['image_file']['name']);
                $extension = strtolower($fileInfo['extension']);
                $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                
                if (!in_array($extension, $allowedExtensions)) {
                    throw new Exception('Invalid file type. Only JPEG, PNG, GIF, and WebP files are allowed.');
                }
                
                if ($_FILES['image_file']['size'] > 5 * 1024 * 1024) {
                    throw new Exception('File size must be less than 5MB.');
                }
                
                $filename = time() . '_' . uniqid() . '.' . $extension;
                $uploadPath = $uploadDir . $filename;
                
                if (move_uploaded_file($_FILES['image_file']['tmp_name'], $uploadPath)) {
                    $imageUrl = 'assets/images/events/' . $filename;
                } else {
                    throw new Exception('Failed to upload image file.');
                }
            }
            
            $db->query(
                "INSERT INTO events (title, description, event_date, event_time, category, image_url, is_recurring, recurring_type, is_featured, is_active, created_at, updated_at) VALUES (:title, :description, :event_date, :event_time, :category, :image_url, :is_recurring, :recurring_type, :is_featured, :is_active, NOW(), NOW())",
                [
                    'title' => $title,
                    'description' => $description,
                    'event_date' => $eventDate,
                    'event_time' => $eventTime,
                    'category' => $category,
                    'image_url' => $imageUrl,
                    'is_recurring' => $isRecurring,
                    'recurring_type' => $recurringType,
                    'is_featured' => $isFeatured,
                    'is_active' => $isActive
                ]
            );
            
            $message = 'Event added successfully!';
            $messageType = 'success';
            
        } elseif ($action === 'edit') {
            $id = (int)($_POST['id'] ?? 0);
            $title = trim($_POST['title'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $eventDate = trim($_POST['event_date'] ?? '');
            $eventTime = trim($_POST['event_time'] ?? '');
            $category = trim($_POST['category'] ?? '');
            $isRecurring = isset($_POST['is_recurring']) ? 1 : 0;
            $recurringType = trim($_POST['recurring_type'] ?? '');
            $isFeatured = isset($_POST['is_featured']) ? 1 : 0;
            $isActive = isset($_POST['is_active']) ? 1 : 0;
            
            if (empty($title)) {
                throw new Exception('Event title is required');
            }
            
            if (empty($eventDate)) {
                throw new Exception('Event date is required');
            }
            
            if (empty($category)) {
                throw new Exception('Event category is required');
            }
            
            // Handle file upload for edit
            $imageUrl = $_POST['current_image'] ?? null;
            if (isset($_FILES['image_file']) && $_FILES['image_file']['error'] === UPLOAD_ERR_OK) {
                $uploadDir = '../assets/images/events/';
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0755, true);
                }
                
                $fileInfo = pathinfo($_FILES['image_file']['name']);
                $extension = strtolower($fileInfo['extension']);
                $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                
                if (!in_array($extension, $allowedExtensions)) {
                    throw new Exception('Invalid file type. Only JPEG, PNG, GIF, and WebP files are allowed.');
                }
                
                if ($_FILES['image_file']['size'] > 5 * 1024 * 1024) {
                    throw new Exception('File size must be less than 5MB.');
                }
                
                $filename = time() . '_' . uniqid() . '.' . $extension;
                $uploadPath = $uploadDir . $filename;
                
                if (move_uploaded_file($_FILES['image_file']['tmp_name'], $uploadPath)) {
                    // Delete old image if it exists
                    if ($imageUrl && file_exists('../' . $imageUrl)) {
                        unlink('../' . $imageUrl);
                    }
                    $imageUrl = 'assets/images/events/' . $filename;
                } else {
                    throw new Exception('Failed to upload image file.');
                }
            }
            
            $db->query(
                "UPDATE events SET title = :title, description = :description, event_date = :event_date, event_time = :event_time, category = :category, image_url = :image_url, is_recurring = :is_recurring, recurring_type = :recurring_type, is_featured = :is_featured, is_active = :is_active, updated_at = NOW() WHERE id = :id",
                [
                    'id' => $id,
                    'title' => $title,
                    'description' => $description,
                    'event_date' => $eventDate,
                    'event_time' => $eventTime,
                    'category' => $category,
                    'image_url' => $imageUrl,
                    'is_recurring' => $isRecurring,
                    'recurring_type' => $recurringType,
                    'is_featured' => $isFeatured,
                    'is_active' => $isActive
                ]
            );
            
            $message = 'Event updated successfully!';
            $messageType = 'success';
            
        } elseif ($action === 'delete') {
            $id = (int)($_POST['id'] ?? 0);

            // Get image URL before deleting
            $event = $db->fetch("SELECT image_url FROM events WHERE id = :id", ['id' => $id]);

            $db->query("DELETE FROM events WHERE id = :id", ['id' => $id]);

            // Delete image file if it exists
            if ($event && $event['image_url'] && file_exists('../' . $event['image_url'])) {
                unlink('../' . $event['image_url']);
            }

            $message = 'Event deleted successfully!';
            $messageType = 'success';
        }
        
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Handle AJAX requests
if (isset($_GET['ajax'])) {
    header('Content-Type: application/json');
    
    if ($_GET['ajax'] === 'toggle_status') {
        $id = (int)($_GET['id'] ?? 0);
        $event = $db->fetch("SELECT is_active FROM events WHERE id = :id", ['id' => $id]);

        if ($event) {
            $newStatus = $event['is_active'] ? 0 : 1;
            $db->query("UPDATE events SET is_active = :status WHERE id = :id", [
                'status' => $newStatus,
                'id' => $id
            ]);
            echo json_encode(['success' => true, 'status' => $newStatus]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Event not found']);
        }
        exit;
    }

    if ($_GET['ajax'] === 'toggle_featured') {
        $id = (int)($_GET['id'] ?? 0);
        $event = $db->fetch("SELECT is_featured FROM events WHERE id = :id", ['id' => $id]);

        if ($event) {
            $newStatus = $event['is_featured'] ? 0 : 1;
            $db->query("UPDATE events SET is_featured = :status WHERE id = :id", [
                'status' => $newStatus,
                'id' => $id
            ]);
            echo json_encode(['success' => true, 'status' => $newStatus]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Event not found']);
        }
        exit;
    }

    if ($_GET['ajax'] === 'get_event') {
        $id = (int)($_GET['id'] ?? 0);
        $event = $db->fetch("SELECT * FROM events WHERE id = :id", ['id' => $id]);

        if ($event) {
            echo json_encode(['success' => true, 'event' => $event]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Event not found']);
        }
        exit;
    }
}

// Generate CSRF token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Get all events
$events = $db->fetchAll("SELECT * FROM events ORDER BY event_date DESC, created_at DESC");

// Get event statistics
$totalEvents = $db->fetch("SELECT COUNT(*) as count FROM events")['count'];
$activeEvents = $db->fetch("SELECT COUNT(*) as count FROM events WHERE is_active = 1")['count'];
$featuredEvents = $db->fetch("SELECT COUNT(*) as count FROM events WHERE is_featured = 1")['count'];
$upcomingEvents = $db->fetch("SELECT COUNT(*) as count FROM events WHERE event_date >= CURDATE() AND is_active = 1")['count'];

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Events Management</h1>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addEventModal">
                    <i class="fas fa-plus me-2"></i>Add Event
                </button>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo $totalEvents; ?></h4>
                                    <p class="mb-0">Total Events</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calendar-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo $activeEvents; ?></h4>
                                    <p class="mb-0">Active Events</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo $featuredEvents; ?></h4>
                                    <p class="mb-0">Featured Events</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-star fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo $upcomingEvents; ?></h4>
                                    <p class="mb-0">Upcoming Events</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Events Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">All Events</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Image</th>
                                    <th>Title</th>
                                    <th>Date & Time</th>
                                    <th>Category</th>
                                    <th>Recurring</th>
                                    <th>Featured</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($events)): ?>
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-calendar-alt fa-3x mb-3"></i>
                                                <p>No events found. <a href="#" data-bs-toggle="modal" data-bs-target="#addEventModal">Add your first event</a>.</p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($events as $event): ?>
                                        <tr>
                                            <td>
                                                <?php if ($event['image_url']): ?>
                                                    <img src="../<?php echo htmlspecialchars($event['image_url']); ?>"
                                                         alt="<?php echo htmlspecialchars($event['title']); ?>"
                                                         class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="bg-light d-flex align-items-center justify-content-center"
                                                         style="width: 60px; height: 60px; border-radius: 4px;">
                                                        <i class="fas fa-image text-muted"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($event['title']); ?></strong>
                                                    <?php if ($event['description']): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars(substr($event['description'], 0, 50)) . (strlen($event['description']) > 50 ? '...' : ''); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?php echo date('M j, Y', strtotime($event['event_date'])); ?></strong>
                                                    <?php if ($event['event_time']): ?>
                                                        <br><small class="text-muted"><?php echo date('g:i A', strtotime($event['event_time'])); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary"><?php echo htmlspecialchars($event['category']); ?></span>
                                            </td>
                                            <td>
                                                <?php if ($event['is_recurring']): ?>
                                                    <span class="badge bg-info"><?php echo htmlspecialchars($event['recurring_type']); ?></span>
                                                <?php else: ?>
                                                    <span class="text-muted">No</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <button type="button"
                                                        class="btn btn-sm <?php echo $event['is_featured'] ? 'btn-warning' : 'btn-outline-warning'; ?>"
                                                        onclick="toggleFeatured(<?php echo $event['id']; ?>)"
                                                        title="Toggle Featured">
                                                    <i class="fas fa-star"></i>
                                                </button>
                                            </td>
                                            <td>
                                                <button type="button"
                                                        class="btn btn-sm <?php echo $event['is_active'] ? 'btn-success' : 'btn-outline-success'; ?>"
                                                        onclick="toggleStatus(<?php echo $event['id']; ?>)"
                                                        title="Toggle Status">
                                                    <i class="fas <?php echo $event['is_active'] ? 'fa-check' : 'fa-times'; ?>"></i>
                                                </button>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"
                                                            onclick="editEvent(<?php echo $event['id']; ?>)"
                                                            title="Edit Event">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                                            onclick="deleteEvent(<?php echo $event['id']; ?>)"
                                                            title="Delete Event">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Event Modal -->
<div class="modal fade" id="addEventModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <input type="hidden" name="action" value="add">

                <div class="modal-header">
                    <h5 class="modal-title">Add New Event</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>

                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="add_title" class="form-label">Event Title</label>
                                <input type="text" class="form-control" id="add_title" name="title" required>
                                <div class="invalid-feedback">Please provide an event title.</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="add_category" class="form-label">Category</label>
                                <select class="form-select" id="add_category" name="category" required>
                                    <option value="">Select category</option>
                                    <option value="sports">Sports</option>
                                    <option value="live-music">Live Music</option>
                                    <option value="trivia">Trivia Night</option>
                                    <option value="karaoke">Karaoke</option>
                                    <option value="special">Special Event</option>
                                    <option value="holiday">Holiday</option>
                                    <option value="promotion">Promotion</option>
                                    <option value="private">Private Event</option>
                                </select>
                                <div class="invalid-feedback">Please select a category.</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="add_description" class="form-label">Description</label>
                        <textarea class="form-control" id="add_description" name="description" rows="3"
                                  placeholder="Describe the event..."></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_event_date" class="form-label">Event Date</label>
                                <input type="date" class="form-control" id="add_event_date" name="event_date" required>
                                <div class="invalid-feedback">Please provide an event date.</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_event_time" class="form-label">Event Time (optional)</label>
                                <input type="time" class="form-control" id="add_event_time" name="event_time">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="add_image_file" class="form-label">Event Image (optional)</label>
                        <input type="file" class="form-control" id="add_image_file" name="image_file"
                               accept="image/jpeg,image/png,image/gif,image/webp">
                        <div class="form-text">
                            Supported formats: JPEG, PNG, GIF, WebP. Maximum size: 5MB.
                            Recommended dimensions: 1200x600 pixels.
                        </div>
                    </div>

                    <div class="mb-3" id="add_image_preview_container" style="display: none;">
                        <label class="form-label">Image Preview</label>
                        <div class="border rounded p-2">
                            <img id="add_image_preview" src="" alt="Preview" class="img-fluid" style="max-height: 200px;">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="add_is_recurring" name="is_recurring">
                                <label class="form-check-label" for="add_is_recurring">Recurring Event</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_recurring_type" class="form-label">Recurring Type</label>
                                <select class="form-select" id="add_recurring_type" name="recurring_type" disabled>
                                    <option value="">Select type</option>
                                    <option value="weekly">Weekly</option>
                                    <option value="monthly">Monthly</option>
                                    <option value="yearly">Yearly</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="add_is_featured" name="is_featured">
                                <label class="form-check-label" for="add_is_featured">Featured Event</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="add_is_active" name="is_active" checked>
                                <label class="form-check-label" for="add_is_active">Active</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Event</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Event Modal -->
<div class="modal fade" id="editEventModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="id" id="edit_event_id">
                <input type="hidden" name="current_image" id="edit_current_image">

                <div class="modal-header">
                    <h5 class="modal-title">Edit Event</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>

                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="edit_title" class="form-label">Event Title</label>
                                <input type="text" class="form-control" id="edit_title" name="title" required>
                                <div class="invalid-feedback">Please provide an event title.</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_category" class="form-label">Category</label>
                                <select class="form-select" id="edit_category" name="category" required>
                                    <option value="">Select category</option>
                                    <option value="sports">Sports</option>
                                    <option value="live-music">Live Music</option>
                                    <option value="trivia">Trivia Night</option>
                                    <option value="karaoke">Karaoke</option>
                                    <option value="special">Special Event</option>
                                    <option value="holiday">Holiday</option>
                                    <option value="promotion">Promotion</option>
                                    <option value="private">Private Event</option>
                                </select>
                                <div class="invalid-feedback">Please select a category.</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"
                                  placeholder="Describe the event..."></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_event_date" class="form-label">Event Date</label>
                                <input type="date" class="form-control" id="edit_event_date" name="event_date" required>
                                <div class="invalid-feedback">Please provide an event date.</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_event_time" class="form-label">Event Time (optional)</label>
                                <input type="time" class="form-control" id="edit_event_time" name="event_time">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_image_file" class="form-label">Event Image (optional)</label>
                        <input type="file" class="form-control" id="edit_image_file" name="image_file"
                               accept="image/jpeg,image/png,image/gif,image/webp">
                        <div class="form-text">
                            Supported formats: JPEG, PNG, GIF, WebP. Maximum size: 5MB.
                            Leave empty to keep current image.
                        </div>
                    </div>

                    <div class="mb-3" id="edit_current_image_container" style="display: none;">
                        <label class="form-label">Current Image</label>
                        <div class="border rounded p-2">
                            <img id="edit_current_image_preview" src="" alt="Current Image" class="img-fluid" style="max-height: 150px;">
                        </div>
                    </div>

                    <div class="mb-3" id="edit_image_preview_container" style="display: none;">
                        <label class="form-label">New Image Preview</label>
                        <div class="border rounded p-2">
                            <img id="edit_image_preview" src="" alt="Preview" class="img-fluid" style="max-height: 200px;">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="edit_is_recurring" name="is_recurring">
                                <label class="form-check-label" for="edit_is_recurring">Recurring Event</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_recurring_type" class="form-label">Recurring Type</label>
                                <select class="form-select" id="edit_recurring_type" name="recurring_type">
                                    <option value="">Select type</option>
                                    <option value="weekly">Weekly</option>
                                    <option value="monthly">Monthly</option>
                                    <option value="yearly">Yearly</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="edit_is_featured" name="is_featured">
                                <label class="form-check-label" for="edit_is_featured">Featured Event</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                                <label class="form-check-label" for="edit_is_active">Active</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Event</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Handle file preview for Add Event modal
document.getElementById('add_image_file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const previewContainer = document.getElementById('add_image_preview_container');
    const preview = document.getElementById('add_image_preview');

    if (file) {
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            alert('Please select a valid image file (JPEG, PNG, GIF, or WebP).');
            e.target.value = '';
            previewContainer.style.display = 'none';
            return;
        }

        if (file.size > 5 * 1024 * 1024) {
            alert('File size must be less than 5MB.');
            e.target.value = '';
            previewContainer.style.display = 'none';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            preview.src = e.target.result;
            previewContainer.style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        previewContainer.style.display = 'none';
    }
});

// Handle file preview for Edit Event modal
document.getElementById('edit_image_file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const previewContainer = document.getElementById('edit_image_preview_container');
    const preview = document.getElementById('edit_image_preview');

    if (file) {
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            alert('Please select a valid image file (JPEG, PNG, GIF, or WebP).');
            e.target.value = '';
            previewContainer.style.display = 'none';
            return;
        }

        if (file.size > 5 * 1024 * 1024) {
            alert('File size must be less than 5MB.');
            e.target.value = '';
            previewContainer.style.display = 'none';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            preview.src = e.target.result;
            previewContainer.style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        previewContainer.style.display = 'none';
    }
});

// Handle recurring event checkbox
document.getElementById('add_is_recurring').addEventListener('change', function() {
    const recurringType = document.getElementById('add_recurring_type');
    recurringType.disabled = !this.checked;
    if (!this.checked) {
        recurringType.value = '';
    }
});

document.getElementById('edit_is_recurring').addEventListener('change', function() {
    const recurringType = document.getElementById('edit_recurring_type');
    recurringType.disabled = !this.checked;
    if (!this.checked) {
        recurringType.value = '';
    }
});

function toggleStatus(id) {
    fetch(`?ajax=toggle_status&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the status.');
        });
}

function toggleFeatured(id) {
    fetch(`?ajax=toggle_featured&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the featured status.');
        });
}

function editEvent(id) {
    fetch(`?ajax=get_event&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const event = data.event;

                // Populate form fields
                document.getElementById('edit_event_id').value = event.id;
                document.getElementById('edit_title').value = event.title;
                document.getElementById('edit_description').value = event.description || '';
                document.getElementById('edit_event_date').value = event.event_date;
                document.getElementById('edit_event_time').value = event.event_time || '';
                document.getElementById('edit_category').value = event.category;
                document.getElementById('edit_current_image').value = event.image_url || '';
                document.getElementById('edit_is_recurring').checked = event.is_recurring == 1;
                document.getElementById('edit_recurring_type').value = event.recurring_type || '';
                document.getElementById('edit_recurring_type').disabled = event.is_recurring != 1;
                document.getElementById('edit_is_featured').checked = event.is_featured == 1;
                document.getElementById('edit_is_active').checked = event.is_active == 1;

                // Show current image if exists
                const currentImageContainer = document.getElementById('edit_current_image_container');
                const currentImagePreview = document.getElementById('edit_current_image_preview');
                if (event.image_url) {
                    currentImagePreview.src = '../' + event.image_url;
                    currentImageContainer.style.display = 'block';
                } else {
                    currentImageContainer.style.display = 'none';
                }

                // Hide new image preview
                document.getElementById('edit_image_preview_container').style.display = 'none';
                document.getElementById('edit_image_file').value = '';

                // Show modal
                new bootstrap.Modal(document.getElementById('editEventModal')).show();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while loading the event data.');
        });
}

function deleteEvent(id) {
    if (confirm('Are you sure you want to delete this event? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php include 'includes/footer.php'; ?>
