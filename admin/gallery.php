<?php
/**
 * Champions Sports Bar & Grill - Gallery Management
 */

require_once 'includes/auth.php';
require_once 'config/database.php';

// Check if user is logged in
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$db = getDB();
$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate CSRF token
        if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
            throw new Exception('Invalid CSRF token');
        }

        $action = $_POST['action'] ?? '';
        
        if ($action === 'add') {
            $title = trim($_POST['title'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $category = trim($_POST['category'] ?? '');
            $isActive = isset($_POST['is_active']) ? 1 : 0;
            $sortOrder = (int)($_POST['sort_order'] ?? 0);
            
            if (empty($title)) {
                throw new Exception('Image title is required');
            }
            
            if (empty($category)) {
                throw new Exception('Category is required');
            }
            
            $db->query(
                "INSERT INTO gallery_images (title, description, category, is_active, sort_order, created_at, updated_at) VALUES (:title, :description, :category, :is_active, :sort_order, NOW(), NOW())",
                [
                    'title' => $title,
                    'description' => $description,
                    'category' => $category,
                    'is_active' => $isActive,
                    'sort_order' => $sortOrder
                ]
            );
            
            $message = 'Gallery image added successfully!';
            $messageType = 'success';
            
        } elseif ($action === 'edit') {
            $id = (int)($_POST['id'] ?? 0);
            $title = trim($_POST['title'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $category = trim($_POST['category'] ?? '');
            $isActive = isset($_POST['is_active']) ? 1 : 0;
            $sortOrder = (int)($_POST['sort_order'] ?? 0);
            
            if (empty($title)) {
                throw new Exception('Image title is required');
            }
            
            if (empty($category)) {
                throw new Exception('Category is required');
            }
            
            // Check if image exists
            $existing = $db->fetch("SELECT id FROM gallery_images WHERE id = :id", ['id' => $id]);
            if (!$existing) {
                throw new Exception('Gallery image not found');
            }
            
            $db->query(
                "UPDATE gallery_images SET title = :title, description = :description, category = :category, is_active = :is_active, sort_order = :sort_order, updated_at = NOW() WHERE id = :id",
                [
                    'title' => $title,
                    'description' => $description,
                    'category' => $category,
                    'is_active' => $isActive,
                    'sort_order' => $sortOrder,
                    'id' => $id
                ]
            );
            
            $message = 'Gallery image updated successfully!';
            $messageType = 'success';
            
        } elseif ($action === 'delete') {
            $id = (int)($_POST['id'] ?? 0);
            
            // Check if image exists
            $existing = $db->fetch("SELECT image_url FROM gallery_images WHERE id = :id", ['id' => $id]);
            if (!$existing) {
                throw new Exception('Gallery image not found');
            }
            
            // Delete image file if it exists
            if ($existing['image_url'] && file_exists('../' . $existing['image_url'])) {
                unlink('../' . $existing['image_url']);
                
                // Also delete thumbnail if it exists
                $thumbnailPath = str_replace('/gallery/', '/gallery/thumbs/', $existing['image_url']);
                if (file_exists('../' . $thumbnailPath)) {
                    unlink('../' . $thumbnailPath);
                }
            }
            
            $db->query("DELETE FROM gallery_images WHERE id = :id", ['id' => $id]);
            
            $message = 'Gallery image deleted successfully!';
            $messageType = 'success';
        }
        
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Handle AJAX requests for quick actions
if (isset($_GET['ajax'])) {
    header('Content-Type: application/json');
    
    try {
        if ($_GET['ajax'] === 'toggle_status' && isset($_GET['id'])) {
            $id = (int)$_GET['id'];
            $image = $db->fetch("SELECT is_active FROM gallery_images WHERE id = :id", ['id' => $id]);
            
            if ($image) {
                $newStatus = $image['is_active'] ? 0 : 1;
                $db->query("UPDATE gallery_images SET is_active = :status, updated_at = NOW() WHERE id = :id", 
                          ['status' => $newStatus, 'id' => $id]);
                echo json_encode(['success' => true, 'new_status' => $newStatus]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Gallery image not found']);
            }
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

// Get filter parameters
$categoryFilter = isset($_GET['category']) ? trim($_GET['category']) : '';

// Build query
$whereConditions = [];
$params = [];

if (!empty($categoryFilter)) {
    $whereConditions[] = "category = :category";
    $params['category'] = $categoryFilter;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Get gallery images
$galleryImages = $db->fetchAll("
    SELECT * FROM gallery_images 
    {$whereClause}
    ORDER BY sort_order ASC, created_at DESC
", $params);

// Get all categories for filter dropdown
$categories = $db->fetchAll("SELECT DISTINCT category FROM gallery_images WHERE category != '' ORDER BY category ASC");

// Generate CSRF token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

$pageTitle = 'Gallery Management';
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Gallery Management</h1>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addImageModal">
                    <i class="fas fa-plus me-2"></i>Add Image
                </button>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="category" class="form-label">Filter by Category</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">All Categories</option>
                                <?php foreach ($categories as $cat): ?>
                                    <option value="<?php echo htmlspecialchars($cat['category']); ?>" 
                                            <?php echo $categoryFilter === $cat['category'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars(ucfirst($cat['category'])); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-outline-primary">Filter</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <?php if (empty($galleryImages)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-images fa-3x text-muted mb-3"></i>
                            <h5>No gallery images found</h5>
                            <p class="text-muted">
                                <?php if (!empty($categoryFilter)): ?>
                                    No images found in the "<?php echo htmlspecialchars($categoryFilter); ?>" category. 
                                    <a href="?">View all images</a>.
                                <?php else: ?>
                                    Start by adding your first gallery image.
                                <?php endif; ?>
                            </p>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addImageModal">
                                <i class="fas fa-plus me-2"></i>Add First Image
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach ($galleryImages as $image): ?>
                                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                                    <div class="card h-100">
                                        <?php if ($image['image_url']): ?>
                                            <img src="/<?php echo htmlspecialchars($image['image_url']); ?>" 
                                                 alt="<?php echo htmlspecialchars($image['title']); ?>" 
                                                 class="card-img-top" style="height: 200px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                                 style="height: 200px;">
                                                <i class="fas fa-image fa-3x text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="card-body">
                                            <h6 class="card-title"><?php echo htmlspecialchars($image['title']); ?></h6>
                                            <p class="card-text">
                                                <small class="text-muted">
                                                    Category: <?php echo htmlspecialchars(ucfirst($image['category'])); ?>
                                                </small>
                                            </p>
                                            <?php if ($image['description']): ?>
                                                <p class="card-text">
                                                    <small><?php echo htmlspecialchars(substr($image['description'], 0, 100)); ?><?php echo strlen($image['description']) > 100 ? '...' : ''; ?></small>
                                                </p>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="card-footer bg-transparent">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" 
                                                           <?php echo $image['is_active'] ? 'checked' : ''; ?>
                                                           onchange="toggleStatus(<?php echo $image['id']; ?>)">
                                                    <label class="form-check-label">Active</label>
                                                </div>
                                                
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" class="btn btn-outline-info" 
                                                            onclick="uploadImage(<?php echo $image['id']; ?>, '<?php echo htmlspecialchars($image['title']); ?>')"
                                                            title="Upload Image">
                                                        <i class="fas fa-camera"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-primary" 
                                                            onclick="editImage(<?php echo htmlspecialchars(json_encode($image)); ?>)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-danger" 
                                                            onclick="deleteImage(<?php echo $image['id']; ?>, '<?php echo htmlspecialchars($image['title']); ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Image Modal -->
<div class="modal fade" id="addImageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <input type="hidden" name="action" value="add">

                <div class="modal-header">
                    <h5 class="modal-title">Add New Gallery Image</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>

                <div class="modal-body">
                    <div class="mb-3">
                        <label for="add_title" class="form-label">Image Title</label>
                        <input type="text" class="form-control" id="add_title" name="title" required>
                        <div class="invalid-feedback">Please provide an image title.</div>
                    </div>

                    <div class="mb-3">
                        <label for="add_category" class="form-label">Category</label>
                        <select class="form-select" id="add_category" name="category" required>
                            <option value="">Select a category</option>
                            <option value="food">Food</option>
                            <option value="drinks">Drinks</option>
                            <option value="interior">Interior</option>
                            <option value="patio">Patio</option>
                            <option value="events">Events</option>
                            <option value="sports">Sports</option>
                            <option value="staff">Staff</option>
                            <option value="atmosphere">Atmosphere</option>
                        </select>
                        <div class="invalid-feedback">Please select a category.</div>
                    </div>

                    <div class="mb-3">
                        <label for="add_description" class="form-label">Description (optional)</label>
                        <textarea class="form-control" id="add_description" name="description" rows="3"
                                  placeholder="Describe the image..."></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_sort_order" class="form-label">Sort Order</label>
                                <input type="number" class="form-control" id="add_sort_order" name="sort_order" value="0" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="add_is_active" name="is_active" checked>
                                    <label class="form-check-label" for="add_is_active">Active</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Image</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Image Modal -->
<div class="modal fade" id="editImageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="id" id="edit_id">

                <div class="modal-header">
                    <h5 class="modal-title">Edit Gallery Image</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>

                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_title" class="form-label">Image Title</label>
                        <input type="text" class="form-control" id="edit_title" name="title" required>
                        <div class="invalid-feedback">Please provide an image title.</div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_category" class="form-label">Category</label>
                        <select class="form-select" id="edit_category" name="category" required>
                            <option value="">Select a category</option>
                            <option value="food">Food</option>
                            <option value="drinks">Drinks</option>
                            <option value="interior">Interior</option>
                            <option value="patio">Patio</option>
                            <option value="events">Events</option>
                            <option value="sports">Sports</option>
                            <option value="staff">Staff</option>
                            <option value="atmosphere">Atmosphere</option>
                        </select>
                        <div class="invalid-feedback">Please select a category.</div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description (optional)</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_sort_order" class="form-label">Sort Order</label>
                                <input type="number" class="form-control" id="edit_sort_order" name="sort_order" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                                    <label class="form-check-label" for="edit_is_active">Active</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Image</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteImageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="id" id="delete_id">

                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>

                <div class="modal-body">
                    <p>Are you sure you want to delete the image "<span id="delete_name"></span>"?</p>
                    <p class="text-danger"><small>This action cannot be undone and will also delete the image file.</small></p>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete Image</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Image Upload Modal -->
<div class="modal fade" id="uploadImageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Upload Image for <span id="upload_image_name"></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>

            <div class="modal-body">
                <form id="imageUploadForm" enctype="multipart/form-data">
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                    <input type="hidden" name="upload_type" value="gallery">
                    <input type="hidden" name="item_id" id="upload_image_id">

                    <div class="mb-3">
                        <label for="image_file" class="form-label">Select Image</label>
                        <input type="file" class="form-control" id="image_file" name="image"
                               accept="image/jpeg,image/png,image/gif,image/webp" required>
                        <div class="form-text">
                            Supported formats: JPEG, PNG, GIF, WebP. Maximum size: 5MB.
                            Recommended dimensions: 1200x800 pixels.
                        </div>
                    </div>

                    <div id="image_preview" class="mb-3" style="display: none;">
                        <label class="form-label">Preview</label>
                        <div class="border rounded p-2">
                            <img id="preview_image" src="" alt="Preview" class="img-fluid" style="max-height: 200px;">
                        </div>
                    </div>

                    <div id="upload_progress" class="mb-3" style="display: none;">
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>

                    <div id="upload_message" class="alert" style="display: none;"></div>
                </form>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitImageUpload()">Upload Image</button>
            </div>
        </div>
    </div>
</div>

<script>
function editImage(image) {
    document.getElementById('edit_id').value = image.id;
    document.getElementById('edit_title').value = image.title;
    document.getElementById('edit_category').value = image.category;
    document.getElementById('edit_description').value = image.description || '';
    document.getElementById('edit_sort_order').value = image.sort_order;
    document.getElementById('edit_is_active').checked = image.is_active == 1;

    new bootstrap.Modal(document.getElementById('editImageModal')).show();
}

function deleteImage(id, name) {
    document.getElementById('delete_id').value = id;
    document.getElementById('delete_name').textContent = name;

    new bootstrap.Modal(document.getElementById('deleteImageModal')).show();
}

function uploadImage(id, name) {
    document.getElementById('upload_image_id').value = id;
    document.getElementById('upload_image_name').textContent = name;
    document.getElementById('image_file').value = '';
    document.getElementById('image_preview').style.display = 'none';
    document.getElementById('upload_progress').style.display = 'none';
    document.getElementById('upload_message').style.display = 'none';

    new bootstrap.Modal(document.getElementById('uploadImageModal')).show();
}

function toggleStatus(id) {
    fetch(`?ajax=toggle_status&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                alert('Error: ' + data.message);
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the status');
            location.reload();
        });
}

function submitImageUpload() {
    const form = document.getElementById('imageUploadForm');
    const formData = new FormData(form);
    const progressBar = document.querySelector('#upload_progress .progress-bar');
    const messageDiv = document.getElementById('upload_message');

    // Show progress bar
    document.getElementById('upload_progress').style.display = 'block';
    progressBar.style.width = '0%';

    // Hide previous messages
    messageDiv.style.display = 'none';

    fetch('upload-image.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        progressBar.style.width = '100%';

        if (data.success) {
            messageDiv.className = 'alert alert-success';
            messageDiv.textContent = data.message;
            messageDiv.style.display = 'block';

            // Reload page after short delay
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            messageDiv.className = 'alert alert-danger';
            messageDiv.textContent = data.message;
            messageDiv.style.display = 'block';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        progressBar.style.width = '100%';
        messageDiv.className = 'alert alert-danger';
        messageDiv.textContent = 'An error occurred while uploading the image';
        messageDiv.style.display = 'block';
    });
}

// Image preview functionality
document.addEventListener('DOMContentLoaded', function() {
    const imageInput = document.getElementById('image_file');
    const previewDiv = document.getElementById('image_preview');
    const previewImg = document.getElementById('preview_image');

    if (imageInput) {
        imageInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    previewDiv.style.display = 'block';
                };
                reader.readAsDataURL(file);
            } else {
                previewDiv.style.display = 'none';
            }
        });
    }
});

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?php include 'includes/footer.php'; ?>
