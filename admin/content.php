<?php
/**
 * Champions Sports Bar & Grill - Content Management
 */

$pageTitle = 'Content Management';
require_once 'includes/header.php';

$auth->requirePermission('manage_content');

$db = getDB();
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (!verifyCSRFToken($csrfToken)) {
        $message = 'Invalid security token.';
        $messageType = 'danger';
    } else {
        switch ($action) {
            case 'update_content':
                $sectionKey = sanitize($_POST['section_key']);
                $title = sanitize($_POST['title']);
                $content = $_POST['content']; // Don't sanitize HTML content
                $imageUrl = sanitize($_POST['image_url']);
                $isActive = isset($_POST['is_active']) ? 1 : 0;
                
                // Check if section exists
                $existing = $db->fetch(
                    "SELECT id FROM content_sections WHERE section_key = :key",
                    ['key' => $sectionKey]
                );
                
                if ($existing) {
                    // Update existing section
                    $db->update(
                        'content_sections',
                        [
                            'title' => $title,
                            'content' => $content,
                            'image_url' => $imageUrl,
                            'is_active' => $isActive,
                            'updated_by' => $currentUser['id']
                        ],
                        'section_key = :key',
                        ['key' => $sectionKey]
                    );
                } else {
                    // Insert new section
                    $db->insert('content_sections', [
                        'section_key' => $sectionKey,
                        'title' => $title,
                        'content' => $content,
                        'image_url' => $imageUrl,
                        'is_active' => $isActive,
                        'updated_by' => $currentUser['id']
                    ]);
                }
                
                logActivity($currentUser['id'], 'content_update', 'content_sections', null, null, [
                    'section_key' => $sectionKey,
                    'title' => $title
                ]);
                
                $message = 'Content updated successfully!';
                $messageType = 'success';
                break;
                
            case 'update_settings':
                foreach ($_POST['settings'] as $key => $value) {
                    $key = sanitize($key);
                    $value = sanitize($value);
                    
                    $db->query(
                        "INSERT INTO site_settings (setting_key, setting_value, updated_by) 
                         VALUES (:key, :value, :user_id)
                         ON DUPLICATE KEY UPDATE 
                         setting_value = :value, updated_by = :user_id",
                        [
                            'key' => $key,
                            'value' => $value,
                            'user_id' => $currentUser['id']
                        ]
                    );
                }
                
                logActivity($currentUser['id'], 'settings_update');
                
                $message = 'Settings updated successfully!';
                $messageType = 'success';
                break;
        }
    }
}

// Get current content sections
$contentSections = $db->fetchAll(
    "SELECT * FROM content_sections ORDER BY sort_order ASC, section_key ASC"
);

// Get site settings
$settings = [];
$settingsData = $db->fetchAll("SELECT setting_key, setting_value FROM site_settings");
foreach ($settingsData as $setting) {
    $settings[$setting['setting_key']] = $setting['setting_value'];
}

// Define content sections structure
$sectionDefinitions = [
    'hero_main' => [
        'name' => 'Main Hero Section',
        'description' => 'Homepage hero banner content',
        'has_image' => true
    ],
    'patio_highlight' => [
        'name' => 'Outdoor Patio Highlight',
        'description' => 'Outdoor patio section content',
        'has_image' => true
    ],
    'about_us' => [
        'name' => 'About Us',
        'description' => 'About Champions Sports Bar content',
        'has_image' => false
    ],
    'features_intro' => [
        'name' => 'Features Introduction',
        'description' => 'Introduction text for features section',
        'has_image' => false
    ]
];
?>

<?php if ($message): ?>
    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
        <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row mb-4">
    <div class="col-12">
        <h1 class="h2 mb-0">
            <i class="fas fa-edit me-2"></i>
            Content Management
        </h1>
        <p class="text-muted">Manage homepage content, hero banners, and site settings</p>
    </div>
</div>

<!-- Navigation Tabs -->
<ul class="nav nav-tabs mb-4" id="contentTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="content-tab" data-bs-toggle="tab" data-bs-target="#content" type="button" role="tab">
            <i class="fas fa-file-alt me-1"></i> Content Sections
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab">
            <i class="fas fa-cog me-1"></i> Site Settings
        </button>
    </li>
</ul>

<div class="tab-content" id="contentTabsContent">
    <!-- Content Sections Tab -->
    <div class="tab-pane fade show active" id="content" role="tabpanel">
        <div class="row">
            <?php foreach ($sectionDefinitions as $sectionKey => $definition): ?>
                <?php
                // Find existing content for this section
                $existingContent = null;
                foreach ($contentSections as $section) {
                    if ($section['section_key'] === $sectionKey) {
                        $existingContent = $section;
                        break;
                    }
                }
                ?>
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <?php echo htmlspecialchars($definition['name']); ?>
                            </h5>
                            <small class="text-muted"><?php echo htmlspecialchars($definition['description']); ?></small>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="action" value="update_content">
                                <input type="hidden" name="section_key" value="<?php echo $sectionKey; ?>">
                                
                                <div class="mb-3">
                                    <label for="title_<?php echo $sectionKey; ?>" class="form-label">Title</label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="title_<?php echo $sectionKey; ?>" 
                                           name="title" 
                                           value="<?php echo htmlspecialchars($existingContent['title'] ?? ''); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="content_<?php echo $sectionKey; ?>" class="form-label">Content</label>
                                    <textarea class="form-control summernote" 
                                              id="content_<?php echo $sectionKey; ?>" 
                                              name="content" 
                                              rows="6"><?php echo $existingContent['content'] ?? ''; ?></textarea>
                                </div>
                                
                                <?php if ($definition['has_image']): ?>
                                <div class="mb-3">
                                    <label for="image_url_<?php echo $sectionKey; ?>" class="form-label">Image URL</label>
                                    <input type="url" 
                                           class="form-control" 
                                           id="image_url_<?php echo $sectionKey; ?>" 
                                           name="image_url" 
                                           value="<?php echo htmlspecialchars($existingContent['image_url'] ?? ''); ?>"
                                           placeholder="https://example.com/image.jpg">
                                    <div class="form-text">Enter the full URL to the image</div>
                                </div>
                                <?php endif; ?>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" 
                                           class="form-check-input" 
                                           id="is_active_<?php echo $sectionKey; ?>" 
                                           name="is_active" 
                                           <?php echo ($existingContent['is_active'] ?? 1) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_active_<?php echo $sectionKey; ?>">
                                        Active (show on website)
                                    </label>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i> Save Content
                                </button>
                                
                                <?php if ($existingContent): ?>
                                <small class="text-muted d-block mt-2">
                                    Last updated: <?php echo date('M j, Y g:i A', strtotime($existingContent['updated_at'])); ?>
                                </small>
                                <?php endif; ?>
                            </form>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
    
    <!-- Site Settings Tab -->
    <div class="tab-pane fade" id="settings" role="tabpanel">
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Site Settings</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="action" value="update_settings">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="site_name" class="form-label">Site Name</label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="site_name" 
                                           name="settings[site_name]" 
                                           value="<?php echo htmlspecialchars($settings['site_name'] ?? ''); ?>">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="site_tagline" class="form-label">Site Tagline</label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="site_tagline" 
                                           name="settings[site_tagline]" 
                                           value="<?php echo htmlspecialchars($settings['site_tagline'] ?? ''); ?>">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="contact_phone" class="form-label">Phone Number</label>
                                    <input type="tel" 
                                           class="form-control" 
                                           id="contact_phone" 
                                           name="settings[contact_phone]" 
                                           value="<?php echo htmlspecialchars($settings['contact_phone'] ?? ''); ?>">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="contact_email" class="form-label">Contact Email</label>
                                    <input type="email" 
                                           class="form-control" 
                                           id="contact_email" 
                                           name="settings[contact_email]" 
                                           value="<?php echo htmlspecialchars($settings['contact_email'] ?? ''); ?>">
                                </div>
                            </div>
                            
                            <h6 class="mt-4 mb-3">Address</h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="address_street" class="form-label">Street Address</label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="address_street" 
                                           name="settings[address_street]" 
                                           value="<?php echo htmlspecialchars($settings['address_street'] ?? ''); ?>">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="address_city" class="form-label">City</label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="address_city" 
                                           name="settings[address_city]" 
                                           value="<?php echo htmlspecialchars($settings['address_city'] ?? ''); ?>">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="address_state" class="form-label">State</label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="address_state" 
                                           name="settings[address_state]" 
                                           value="<?php echo htmlspecialchars($settings['address_state'] ?? ''); ?>">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="address_zip" class="form-label">ZIP Code</label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="address_zip" 
                                           name="settings[address_zip]" 
                                           value="<?php echo htmlspecialchars($settings['address_zip'] ?? ''); ?>">
                                </div>
                            </div>
                            
                            <h6 class="mt-4 mb-3">Social Media</h6>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="social_facebook" class="form-label">Facebook URL</label>
                                    <input type="url" 
                                           class="form-control" 
                                           id="social_facebook" 
                                           name="settings[social_facebook]" 
                                           value="<?php echo htmlspecialchars($settings['social_facebook'] ?? ''); ?>">
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="social_instagram" class="form-label">Instagram URL</label>
                                    <input type="url" 
                                           class="form-control" 
                                           id="social_instagram" 
                                           name="settings[social_instagram]" 
                                           value="<?php echo htmlspecialchars($settings['social_instagram'] ?? ''); ?>">
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="social_twitter" class="form-label">Twitter URL</label>
                                    <input type="url" 
                                           class="form-control" 
                                           id="social_twitter" 
                                           name="settings[social_twitter]" 
                                           value="<?php echo htmlspecialchars($settings['social_twitter'] ?? ''); ?>">
                                </div>
                            </div>
                            
                            <h6 class="mt-4 mb-3">Integration Settings</h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="google_maps_api_key" class="form-label">Google Maps API Key</label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="google_maps_api_key" 
                                           name="settings[google_maps_api_key]" 
                                           value="<?php echo htmlspecialchars($settings['google_maps_api_key'] ?? ''); ?>">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="google_analytics_id" class="form-label">Google Analytics ID</label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="google_analytics_id" 
                                           name="settings[google_analytics_id]" 
                                           value="<?php echo htmlspecialchars($settings['google_analytics_id'] ?? ''); ?>">
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Save Settings
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">Quick Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="../index.php" target="_blank" class="btn btn-outline-primary">
                                <i class="fas fa-external-link-alt me-1"></i> Preview Homepage
                            </a>
                            <a href="hero-banners.php" class="btn btn-outline-secondary">
                                <i class="fas fa-image me-1"></i> Manage Hero Banners
                            </a>
                            <a href="gallery.php" class="btn btn-outline-info">
                                <i class="fas fa-images me-1"></i> Manage Gallery
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">Content Tips</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled small">
                            <li class="mb-2">
                                <i class="fas fa-lightbulb text-warning me-1"></i>
                                Use the rich text editor to format your content
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-image text-info me-1"></i>
                                Upload images to the gallery first, then use their URLs
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-mobile-alt text-success me-1"></i>
                                Keep content concise for mobile users
                            </li>
                            <li class="mb-0">
                                <i class="fas fa-search text-primary me-1"></i>
                                Include relevant keywords for SEO
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
