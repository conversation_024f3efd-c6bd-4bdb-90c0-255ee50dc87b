<?php
/**
 * Champions Sports Bar & Grill - Job Management
 */

require_once 'includes/auth.php';
require_once 'config/database.php';

// Check if user is logged in
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$db = Database::getInstance();
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // CSRF protection
        if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
            throw new Exception('Invalid CSRF token');
        }

        $action = $_POST['action'] ?? '';
        
        if ($action === 'add') {
            $title = trim($_POST['title'] ?? '');
            $department = trim($_POST['department'] ?? '');
            $location = trim($_POST['location'] ?? '');
            $employmentType = trim($_POST['employment_type'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $requirements = trim($_POST['requirements'] ?? '');
            $benefits = trim($_POST['benefits'] ?? '');
            $salaryMin = !empty($_POST['salary_min']) ? (float)$_POST['salary_min'] : null;
            $salaryMax = !empty($_POST['salary_max']) ? (float)$_POST['salary_max'] : null;
            $salaryType = trim($_POST['salary_type'] ?? '');
            $isActive = isset($_POST['is_active']) ? 1 : 0;
            
            if (empty($title)) {
                throw new Exception('Job title is required');
            }
            
            if (empty($department)) {
                throw new Exception('Department is required');
            }
            
            if (empty($description)) {
                throw new Exception('Job description is required');
            }
            
            $db->query(
                "INSERT INTO job_postings (title, description, requirements, employment_type, salary_min, salary_max, is_active) VALUES (:title, :description, :requirements, :employment_type, :salary_min, :salary_max, :is_active)",
                [
                    'title' => $title,
                    'description' => $description,
                    'requirements' => $requirements,
                    'employment_type' => $employmentType,
                    'salary_min' => $salaryMin,
                    'salary_max' => $salaryMax,
                    'is_active' => $isActive
                ]
            );
            
            $message = 'Job posting added successfully!';
            $messageType = 'success';
            
        } elseif ($action === 'edit') {
            $id = (int)($_POST['id'] ?? 0);
            $title = trim($_POST['title'] ?? '');
            $department = trim($_POST['department'] ?? '');
            $location = trim($_POST['location'] ?? '');
            $employmentType = trim($_POST['employment_type'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $requirements = trim($_POST['requirements'] ?? '');
            $benefits = trim($_POST['benefits'] ?? '');
            $salaryMin = !empty($_POST['salary_min']) ? (float)$_POST['salary_min'] : null;
            $salaryMax = !empty($_POST['salary_max']) ? (float)$_POST['salary_max'] : null;
            $salaryType = trim($_POST['salary_type'] ?? '');
            $isActive = isset($_POST['is_active']) ? 1 : 0;
            
            if (empty($title)) {
                throw new Exception('Job title is required');
            }
            
            if (empty($department)) {
                throw new Exception('Department is required');
            }
            
            if (empty($description)) {
                throw new Exception('Job description is required');
            }
            
            $db->query(
                "UPDATE job_postings SET title = :title, employment_type = :employment_type, description = :description, requirements = :requirements, salary_min = :salary_min, salary_max = :salary_max, is_active = :is_active, updated_at = NOW() WHERE id = :id",
                [
                    'id' => $id,
                    'title' => $title,
                    'employment_type' => $employmentType,
                    'description' => $description,
                    'requirements' => $requirements,
                    'salary_min' => $salaryMin,
                    'salary_max' => $salaryMax,
                    'is_active' => $isActive
                ]
            );
            
            $message = 'Job posting updated successfully!';
            $messageType = 'success';
            
        } elseif ($action === 'delete') {
            $id = (int)($_POST['id'] ?? 0);
            
            $db->query("DELETE FROM job_postings WHERE id = :id", ['id' => $id]);
            
            $message = 'Job posting deleted successfully!';
            $messageType = 'success';
        }
        
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Handle AJAX requests
if (isset($_GET['ajax'])) {
    header('Content-Type: application/json');
    
    if ($_GET['ajax'] === 'toggle_status') {
        $id = (int)($_GET['id'] ?? 0);
        $job = $db->fetch("SELECT is_active FROM job_postings WHERE id = :id", ['id' => $id]);
        
        if ($job) {
            $newStatus = $job['is_active'] ? 0 : 1;
            $db->query("UPDATE job_postings SET is_active = :status WHERE id = :id", [
                'status' => $newStatus,
                'id' => $id
            ]);
            echo json_encode(['success' => true, 'status' => $newStatus]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Job posting not found']);
        }
        exit;
    }
    
    // Remove toggle_featured functionality since is_featured column doesn't exist
    
    if ($_GET['ajax'] === 'get_job') {
        $id = (int)($_GET['id'] ?? 0);
        $job = $db->fetch("SELECT * FROM job_postings WHERE id = :id", ['id' => $id]);
        
        if ($job) {
            echo json_encode(['success' => true, 'job' => $job]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Job posting not found']);
        }
        exit;
    }
}

// Generate CSRF token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Get all job postings
$jobs = $db->fetchAll("SELECT * FROM job_postings ORDER BY created_at DESC");

// Get job statistics
$totalJobs = $db->fetch("SELECT COUNT(*) as count FROM job_postings")['count'];
$activeJobs = $db->fetch("SELECT COUNT(*) as count FROM job_postings WHERE is_active = 1")['count'];
$featuredJobs = 0; // No featured column in this table structure
$totalApplications = $db->fetch("SELECT COUNT(*) as count FROM job_applications")['count'];

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Job Management</h1>
                <div>
                    <a href="job-applications.php" class="btn btn-outline-primary me-2">
                        <i class="fas fa-file-alt me-1"></i>View Applications
                    </a>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addJobModal">
                        <i class="fas fa-plus me-2"></i>Add Job Posting
                    </button>
                </div>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo $totalJobs; ?></h4>
                                    <p class="mb-0">Total Jobs</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-briefcase fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo $activeJobs; ?></h4>
                                    <p class="mb-0">Active Jobs</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo $featuredJobs; ?></h4>
                                    <p class="mb-0">Featured Jobs</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-star fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?php echo $totalApplications; ?></h4>
                                    <p class="mb-0">Applications</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-file-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Jobs Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Job Postings</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Department</th>
                                    <th>Type</th>
                                    <th>Salary</th>
                                    <th>Applications</th>
                                    <th>Featured</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($jobs)): ?>
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-briefcase fa-3x mb-3"></i>
                                                <p>No job postings found. <a href="#" data-bs-toggle="modal" data-bs-target="#addJobModal">Add your first job posting</a>.</p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($jobs as $job): ?>
                                        <?php
                                        // Get application count for this job
                                        try {
                                            $applicationCount = $db->fetch("SELECT COUNT(*) as count FROM job_applications WHERE job_id = :job_id", ['job_id' => $job['id']])['count'];
                                        } catch (Exception $e) {
                                            $applicationCount = 0; // Default to 0 if query fails
                                        }
                                        ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($job['title']); ?></strong>
                                                    <br><small class="text-muted"><i class="fas fa-briefcase"></i> Job Posting</small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">General</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?php echo htmlspecialchars($job['employment_type']); ?></span>
                                            </td>
                                            <td>
                                                <?php if ($job['salary_min'] || $job['salary_max']): ?>
                                                    <div>
                                                        <?php if ($job['salary_min'] && $job['salary_max']): ?>
                                                            $<?php echo number_format($job['salary_min']); ?> - $<?php echo number_format($job['salary_max']); ?>
                                                        <?php elseif ($job['salary_min']): ?>
                                                            From $<?php echo number_format($job['salary_min']); ?>
                                                        <?php elseif ($job['salary_max']): ?>
                                                            Up to $<?php echo number_format($job['salary_max']); ?>
                                                        <?php endif; ?>
                                                        <?php if ($job['salary_type']): ?>
                                                            <br><small class="text-muted"><?php echo htmlspecialchars($job['salary_type']); ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="text-muted">Not specified</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <a href="job-applications.php?job_id=<?php echo $job['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                    <?php echo $applicationCount; ?> Applications
                                                </a>
                                            </td>
                                            <!-- Featured functionality removed -->
                                            <td>
                                                <button type="button"
                                                        class="btn btn-sm <?php echo $job['is_active'] ? 'btn-success' : 'btn-outline-success'; ?>"
                                                        onclick="toggleStatus(<?php echo $job['id']; ?>)"
                                                        title="Toggle Status">
                                                    <i class="fas <?php echo $job['is_active'] ? 'fa-check' : 'fa-times'; ?>"></i>
                                                </button>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"
                                                            onclick="editJob(<?php echo $job['id']; ?>)"
                                                            title="Edit Job">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                                            onclick="deleteJob(<?php echo $job['id']; ?>)"
                                                            title="Delete Job">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Job Modal -->
<div class="modal fade" id="addJobModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <input type="hidden" name="action" value="add">

                <div class="modal-header">
                    <h5 class="modal-title">Add New Job Posting</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>

                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="add_title" class="form-label">Job Title</label>
                                <input type="text" class="form-control" id="add_title" name="title" required>
                                <div class="invalid-feedback">Please provide a job title.</div>
                            </div>
                        </div>
                        <!-- Department field removed since column doesn't exist -->
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_employment_type" class="form-label">Employment Type</label>
                                <select class="form-select" id="add_employment_type" name="employment_type">
                                    <option value="">Select type</option>
                                    <option value="full-time">Full-time</option>
                                    <option value="part-time">Part-time</option>
                                    <option value="contract">Contract</option>
                                    <option value="temporary">Temporary</option>
                                    <option value="seasonal">Seasonal</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_location" class="form-label">Location</label>
                                <input type="text" class="form-control" id="add_location" name="location"
                                       placeholder="e.g., Downtown Location, Main Street">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="add_description" class="form-label">Job Description</label>
                        <textarea class="form-control" id="add_description" name="description" rows="4" required
                                  placeholder="Describe the job responsibilities, duties, and requirements..."></textarea>
                        <div class="invalid-feedback">Please provide a job description.</div>
                    </div>

                    <div class="mb-3">
                        <label for="add_requirements" class="form-label">Requirements (optional)</label>
                        <textarea class="form-control" id="add_requirements" name="requirements" rows="3"
                                  placeholder="List qualifications, skills, experience requirements..."></textarea>
                    </div>

                    <!-- Benefits field removed since column doesn't exist -->

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="add_salary_min" class="form-label">Minimum Salary</label>
                                <input type="number" class="form-control" id="add_salary_min" name="salary_min"
                                       step="0.01" min="0" placeholder="0.00">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="add_salary_max" class="form-label">Maximum Salary</label>
                                <input type="number" class="form-control" id="add_salary_max" name="salary_max"
                                       step="0.01" min="0" placeholder="0.00">
                            </div>
                        </div>
                        <!-- Salary type field removed since column doesn't exist -->
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="add_is_active" name="is_active" checked>
                                <label class="form-check-label" for="add_is_active">Active</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Job Posting</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Job Modal -->
<div class="modal fade" id="editJobModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="id" id="edit_job_id">

                <div class="modal-header">
                    <h5 class="modal-title">Edit Job Posting</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>

                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="edit_title" class="form-label">Job Title</label>
                                <input type="text" class="form-control" id="edit_title" name="title" required>
                                <div class="invalid-feedback">Please provide a job title.</div>
                            </div>
                        </div>
                        <!-- Department field removed since column doesn't exist -->
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_employment_type" class="form-label">Employment Type</label>
                                <select class="form-select" id="edit_employment_type" name="employment_type">
                                    <option value="">Select type</option>
                                    <option value="full-time">Full-time</option>
                                    <option value="part-time">Part-time</option>
                                    <option value="contract">Contract</option>
                                    <option value="temporary">Temporary</option>
                                    <option value="seasonal">Seasonal</option>
                                </select>
                            </div>
                        </div>
                        <!-- Location field removed since column doesn't exist -->
                    </div>

                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Job Description</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="4" required
                                  placeholder="Describe the job responsibilities, duties, and requirements..."></textarea>
                        <div class="invalid-feedback">Please provide a job description.</div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_requirements" class="form-label">Requirements (optional)</label>
                        <textarea class="form-control" id="edit_requirements" name="requirements" rows="3"
                                  placeholder="List qualifications, skills, experience requirements..."></textarea>
                    </div>

                    <!-- Benefits field removed since column doesn't exist -->

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_salary_min" class="form-label">Minimum Salary</label>
                                <input type="number" class="form-control" id="edit_salary_min" name="salary_min"
                                       step="0.01" min="0" placeholder="0.00">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_salary_max" class="form-label">Maximum Salary</label>
                                <input type="number" class="form-control" id="edit_salary_max" name="salary_max"
                                       step="0.01" min="0" placeholder="0.00">
                            </div>
                        </div>
                        <!-- Salary type field removed since column doesn't exist -->
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                                <label class="form-check-label" for="edit_is_active">Active</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Job Posting</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function toggleStatus(id) {
    fetch(`?ajax=toggle_status&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the status.');
        });
}

// toggleFeatured function removed since is_featured column doesn't exist

function editJob(id) {
    fetch(`?ajax=get_job&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const job = data.job;

                // Populate form fields
                document.getElementById('edit_job_id').value = job.id;
                document.getElementById('edit_title').value = job.title;
                document.getElementById('edit_employment_type').value = job.employment_type || '';
                document.getElementById('edit_description').value = job.description;
                document.getElementById('edit_requirements').value = job.requirements || '';
                document.getElementById('edit_salary_min').value = job.salary_min || '';
                document.getElementById('edit_salary_max').value = job.salary_max || '';
                document.getElementById('edit_is_active').checked = job.is_active == 1;

                // Show modal
                new bootstrap.Modal(document.getElementById('editJobModal')).show();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while loading the job data.');
        });
}

function deleteJob(id) {
    if (confirm('Are you sure you want to delete this job posting? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php include 'includes/footer.php'; ?>
