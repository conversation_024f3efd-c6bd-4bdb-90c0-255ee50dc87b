<?php
/**
 * Champions Sports Bar & Grill - Database Setup Script
 * 
 * This script will help you set up the MySQL database for the admin panel.
 * Run this script once to create all necessary tables and default data.
 */

// Include database configuration
require_once 'config/database.php';

// Set content type for better display
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Database Setup - Champions Sports Bar Admin</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'>
</head>
<body class='bg-light'>
    <div class='container py-5'>
        <div class='row justify-content-center'>
            <div class='col-lg-8'>
                <div class='card shadow'>
                    <div class='card-header bg-primary text-white'>
                        <h3 class='mb-0'><i class='fas fa-database me-2'></i>Database Setup</h3>
                    </div>
                    <div class='card-body'>";

try {
    echo "<div class='alert alert-info'>
            <i class='fas fa-info-circle me-2'></i>
            Setting up Champions Sports Bar Admin Database...
          </div>";
    
    // Try to connect to MySQL server (without database)
    $dsn = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    echo "<div class='alert alert-success'>
            <i class='fas fa-check me-2'></i>
            ✓ Connected to MySQL server successfully
          </div>";
    
    // Create database
    $pdo->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<div class='alert alert-success'>
            <i class='fas fa-check me-2'></i>
            ✓ Database '" . DB_NAME . "' created successfully
          </div>";
    
    // Connect to the specific database
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    // Read and execute SQL file
    $sqlFile = __DIR__ . '/database.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL file not found: " . $sqlFile);
    }
    
    $sql = file_get_contents($sqlFile);
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    $successCount = 0;
    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^--/', $statement)) {
            try {
                $pdo->exec($statement);
                $successCount++;
            } catch (PDOException $e) {
                // Ignore "table already exists" errors
                if (strpos($e->getMessage(), 'already exists') === false) {
                    throw $e;
                }
            }
        }
    }
    
    echo "<div class='alert alert-success'>
            <i class='fas fa-check me-2'></i>
            ✓ Executed {$successCount} SQL statements successfully
          </div>";
    
    // Check if admin user exists
    $stmt = $pdo->query("SELECT COUNT(*) FROM admin_users WHERE username = 'admin'");
    $adminExists = $stmt->fetchColumn() > 0;
    
    if (!$adminExists) {
        echo "<div class='alert alert-warning'>
                <i class='fas fa-exclamation-triangle me-2'></i>
                No admin user found. Creating default admin user...
              </div>";
        
        // Create default admin user
        $stmt = $pdo->prepare("
            INSERT INTO admin_users (username, email, password_hash, role, first_name, last_name) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            'admin',
            '<EMAIL>',
            password_hash('admin123', PASSWORD_DEFAULT),
            'admin',
            'Admin',
            'User'
        ]);
        
        echo "<div class='alert alert-success'>
                <i class='fas fa-check me-2'></i>
                ✓ Default admin user created
              </div>";
    }
    
    // Get table count
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    $tableCount = count($tables);
    
    echo "<div class='alert alert-success'>
            <i class='fas fa-check me-2'></i>
            ✓ Database setup completed successfully!
          </div>";
    
    echo "<div class='card mt-4'>
            <div class='card-header'>
                <h5 class='mb-0'>Setup Summary</h5>
            </div>
            <div class='card-body'>
                <ul class='list-unstyled mb-0'>
                    <li><strong>Database:</strong> " . DB_NAME . "</li>
                    <li><strong>Tables Created:</strong> {$tableCount}</li>
                    <li><strong>Admin User:</strong> admin</li>
                    <li><strong>Default Password:</strong> admin123</li>
                </ul>
            </div>
          </div>";
    
    echo "<div class='alert alert-warning mt-4'>
            <i class='fas fa-exclamation-triangle me-2'></i>
            <strong>Important Security Note:</strong> Please change the default admin password immediately after logging in!
          </div>";
    
    echo "<div class='text-center mt-4'>
            <a href='login.php' class='btn btn-primary btn-lg'>
                <i class='fas fa-sign-in-alt me-2'></i>
                Go to Admin Login
            </a>
          </div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>
            <i class='fas fa-exclamation-circle me-2'></i>
            <strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "
          </div>";
    
    echo "<div class='card mt-4'>
            <div class='card-header'>
                <h5 class='mb-0'>Troubleshooting</h5>
            </div>
            <div class='card-body'>
                <h6>Common Issues:</h6>
                <ul>
                    <li><strong>MySQL not running:</strong> Start MySQL service with <code>sudo systemctl start mysql</code></li>
                    <li><strong>Access denied:</strong> Check your MySQL username and password in <code>config/database.php</code></li>
                    <li><strong>Connection refused:</strong> Make sure MySQL is installed and running</li>
                </ul>
                
                <h6 class='mt-3'>Manual Setup:</h6>
                <ol>
                    <li>Start MySQL: <code>sudo systemctl start mysql</code></li>
                    <li>Login to MySQL: <code>mysql -u root -p</code></li>
                    <li>Create database: <code>CREATE DATABASE champions_admin;</code></li>
                    <li>Import SQL file: <code>SOURCE " . __DIR__ . "/database.sql;</code></li>
                </ol>
            </div>
          </div>";
}

echo "            </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
?>
