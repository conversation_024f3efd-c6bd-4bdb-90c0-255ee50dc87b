<?php
/**
 * Champions Sports Bar & Grill - Application Details (AJAX)
 */

require_once 'includes/auth.php';
require_once 'config/database.php';

// Check if user is logged in
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    http_response_code(403);
    echo '<div class="alert alert-danger">Access denied</div>';
    exit;
}

$db = Database::getInstance();

// Get application ID
$applicationId = (int)($_GET['id'] ?? 0);

if (!$applicationId) {
    echo '<div class="alert alert-danger">Invalid application ID</div>';
    exit;
}

// Get application details with job information
$application = $db->fetch("
    SELECT ja.*, jp.title as job_title, jp.description as job_description, 
           jp.requirements as job_requirements, jp.employment_type, jp.salary_min, jp.salary_max
    FROM job_applications ja 
    LEFT JOIN job_postings jp ON ja.job_posting_id = jp.id 
    WHERE ja.id = :id
", ['id' => $applicationId]);

if (!$application) {
    echo '<div class="alert alert-danger">Application not found</div>';
    exit;
}

// Handle notes update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_notes'])) {
    try {
        $notes = trim($_POST['notes'] ?? '');
        
        $db->query(
            "UPDATE job_applications SET notes = :notes, updated_at = NOW() WHERE id = :id",
            ['notes' => $notes, 'id' => $applicationId]
        );
        
        $application['notes'] = $notes;
        echo '<div class="alert alert-success">Notes updated successfully!</div>';
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error updating notes: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

// Status badge colors
$statusColors = [
    'new' => 'primary',
    'reviewed' => 'warning',
    'interview' => 'info',
    'hired' => 'success',
    'rejected' => 'danger'
];

$statusColor = $statusColors[$application['status']] ?? 'secondary';
?>

<div class="row">
    <!-- Applicant Information -->
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>Applicant Information
                </h6>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Name:</strong></div>
                    <div class="col-sm-8"><?php echo htmlspecialchars($application['first_name'] . ' ' . $application['last_name']); ?></div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Email:</strong></div>
                    <div class="col-sm-8">
                        <a href="mailto:<?php echo htmlspecialchars($application['email']); ?>">
                            <?php echo htmlspecialchars($application['email']); ?>
                        </a>
                    </div>
                </div>
                <?php if (!empty($application['phone'])): ?>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Phone:</strong></div>
                    <div class="col-sm-8">
                        <a href="tel:<?php echo htmlspecialchars($application['phone']); ?>">
                            <?php echo htmlspecialchars($application['phone']); ?>
                        </a>
                    </div>
                </div>
                <?php endif; ?>
                <?php if (!empty($application['address'])): ?>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Address:</strong></div>
                    <div class="col-sm-8"><?php echo nl2br(htmlspecialchars($application['address'])); ?></div>
                </div>
                <?php endif; ?>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Status:</strong></div>
                    <div class="col-sm-8">
                        <span class="badge bg-<?php echo $statusColor; ?>">
                            <?php echo ucfirst($application['status']); ?>
                        </span>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Applied:</strong></div>
                    <div class="col-sm-8"><?php echo date('F j, Y \a\t g:i A', strtotime($application['applied_at'])); ?></div>
                </div>
                <?php if (!empty($application['resume_file'])): ?>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Resume:</strong></div>
                    <div class="col-sm-8">
                        <a href="../uploads/resumes/<?php echo htmlspecialchars($application['resume_file']); ?>" 
                           target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-file-pdf me-1"></i>View Resume
                        </a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Job Information -->
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-briefcase me-2"></i>Position Applied For
                </h6>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Position:</strong></div>
                    <div class="col-sm-8"><?php echo htmlspecialchars($application['job_title'] ?? 'Unknown Position'); ?></div>
                </div>
                <?php if (!empty($application['employment_type'])): ?>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Type:</strong></div>
                    <div class="col-sm-8"><?php echo ucfirst(str_replace('-', ' ', $application['employment_type'])); ?></div>
                </div>
                <?php endif; ?>
                <?php if (!empty($application['salary_min']) || !empty($application['salary_max'])): ?>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Salary:</strong></div>
                    <div class="col-sm-8">
                        <?php 
                        if (!empty($application['salary_min']) && !empty($application['salary_max'])) {
                            echo '$' . number_format($application['salary_min'], 2) . ' - $' . number_format($application['salary_max'], 2);
                        } elseif (!empty($application['salary_min'])) {
                            echo 'From $' . number_format($application['salary_min'], 2);
                        } elseif (!empty($application['salary_max'])) {
                            echo 'Up to $' . number_format($application['salary_max'], 2);
                        }
                        ?>
                    </div>
                </div>
                <?php endif; ?>
                <?php if (!empty($application['availability'])): ?>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Availability:</strong></div>
                    <div class="col-sm-8"><?php echo htmlspecialchars($application['availability']); ?></div>
                </div>
                <?php endif; ?>
                <?php if (!empty($application['experience'])): ?>
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Experience:</strong></div>
                    <div class="col-sm-8"><?php echo htmlspecialchars($application['experience']); ?></div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Application Details -->
<div class="row mt-3">
    <?php if (!empty($application['previous_work'])): ?>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>Previous Work Experience
                </h6>
            </div>
            <div class="card-body">
                <p class="mb-0"><?php echo nl2br(htmlspecialchars($application['previous_work'])); ?></p>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if (!empty($application['why_work'])): ?>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-question-circle me-2"></i>Why Work Here?
                </h6>
            </div>
            <div class="card-body">
                <p class="mb-0"><?php echo nl2br(htmlspecialchars($application['why_work'])); ?></p>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Admin Notes -->
<div class="row mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-sticky-note me-2"></i>Admin Notes
                </h6>
            </div>
            <div class="card-body">
                <form method="POST" id="notesForm">
                    <div class="mb-3">
                        <textarea name="notes" class="form-control" rows="4" 
                                  placeholder="Add notes about this application..."><?php echo htmlspecialchars($application['notes'] ?? ''); ?></textarea>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            Last updated: <?php echo date('F j, Y \a\t g:i A', strtotime($application['updated_at'])); ?>
                        </small>
                        <button type="submit" name="update_notes" class="btn btn-primary btn-sm">
                            <i class="fas fa-save me-1"></i>Save Notes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm" 
                            onclick="updateApplicationStatus(<?php echo $applicationId; ?>, 'reviewed')">
                        <i class="fas fa-eye me-1"></i>Mark as Reviewed
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm" 
                            onclick="updateApplicationStatus(<?php echo $applicationId; ?>, 'interview')">
                        <i class="fas fa-comments me-1"></i>Schedule Interview
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm" 
                            onclick="updateApplicationStatus(<?php echo $applicationId; ?>, 'hired')">
                        <i class="fas fa-check me-1"></i>Hire
                    </button>
                    <button type="button" class="btn btn-outline-danger btn-sm" 
                            onclick="updateApplicationStatus(<?php echo $applicationId; ?>, 'rejected')">
                        <i class="fas fa-times me-1"></i>Reject
                    </button>
                </div>
                <div class="mt-2">
                    <a href="mailto:<?php echo htmlspecialchars($application['email']); ?>?subject=Re: Your Application for <?php echo urlencode($application['job_title'] ?? 'Position'); ?>" 
                       class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-envelope me-1"></i>Send Email
                    </a>
                    <?php if (!empty($application['phone'])): ?>
                    <a href="tel:<?php echo htmlspecialchars($application['phone']); ?>" 
                       class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-phone me-1"></i>Call Applicant
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateApplicationStatus(applicationId, status) {
    if (confirm(`Are you sure you want to mark this application as ${status}?`)) {
        fetch(`?ajax=update_status&id=${applicationId}&status=${status}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload the modal content
                    location.reload();
                } else {
                    alert('Error updating status: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error updating status');
            });
    }
}

// Handle notes form submission via AJAX
document.getElementById('notesForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    formData.append('update_notes', '1');
    
    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(html => {
        // Update the modal content
        document.getElementById('applicationModalBody').innerHTML = html;
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating notes');
    });
});
</script>
