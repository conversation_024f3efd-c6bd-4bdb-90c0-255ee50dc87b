/* Champions Sports Bar & Grill - Admin Panel Styles */

:root {
    --admin-primary: #dc3545;
    --admin-secondary: #6c757d;
    --admin-success: #28a745;
    --admin-info: #17a2b8;
    --admin-warning: #ffc107;
    --admin-danger: #dc3545;
    --admin-light: #f8f9fa;
    --admin-dark: #343a40;
    --admin-sidebar-width: 250px;
    --admin-header-height: 60px;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    padding-top: var(--admin-header-height);
}

/* Navigation */
.navbar {
    height: var(--admin-header-height);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255,255,255,0.1);
    border-radius: 0.25rem;
}

.navbar-nav .nav-link.active {
    background-color: var(--admin-primary);
    border-radius: 0.25rem;
}

/* Main Content */
.main-content {
    min-height: calc(100vh - var(--admin-header-height) - 80px);
    padding: 2rem 0;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: var(--admin-light);
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Dashboard Stats */
.stat-card {
    background: linear-gradient(135deg, var(--admin-primary), #e74c3c);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.stat-card.success {
    background: linear-gradient(135deg, var(--admin-success), #27ae60);
}

.stat-card.info {
    background: linear-gradient(135deg, var(--admin-info), #3498db);
}

.stat-card.warning {
    background: linear-gradient(135deg, var(--admin-warning), #f39c12);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.stat-icon {
    font-size: 3rem;
    opacity: 0.3;
}

/* Forms */
.form-control, .form-select {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--admin-dark);
}

.required::after {
    content: " *";
    color: var(--admin-danger);
}

/* Buttons */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--admin-primary);
    border-color: var(--admin-primary);
}

.btn-primary:hover {
    background-color: #c82333;
    border-color: #bd2130;
    transform: translateY(-1px);
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
}

/* Tables */
.table {
    background-color: white;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.table thead th {
    background-color: var(--admin-light);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: var(--admin-dark);
}

.table tbody tr:hover {
    background-color: rgba(220, 53, 69, 0.05);
}

/* DataTables Customization */
.dataTables_wrapper .dataTables_length select,
.dataTables_wrapper .dataTables_filter input {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 0.375rem 0.75rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: var(--admin-primary) !important;
    border-color: var(--admin-primary) !important;
    color: white !important;
}

/* File Upload */
.file-upload-area {
    border: 2px dashed #ced4da;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: var(--admin-primary);
    background-color: rgba(220, 53, 69, 0.05);
}

.file-upload-area.dragover {
    border-color: var(--admin-primary);
    background-color: rgba(220, 53, 69, 0.1);
}

.file-preview {
    margin-top: 1rem;
}

.file-preview img {
    max-width: 200px;
    border-radius: 0.375rem;
}

/* Image Gallery */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.gallery-item {
    position: relative;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.gallery-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.gallery-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.gallery-item-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-item-overlay {
    opacity: 1;
}

.gallery-item-actions {
    display: flex;
    gap: 0.5rem;
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-active {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--admin-success);
}

.status-inactive {
    background-color: rgba(108, 117, 125, 0.1);
    color: var(--admin-secondary);
}

.status-pending {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--admin-warning);
}

.status-featured {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--admin-primary);
}

/* Login Page */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--admin-primary), #e74c3c);
}

.login-card {
    width: 100%;
    max-width: 400px;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    overflow: hidden;
}

.login-header {
    background: var(--admin-dark);
    color: white;
    padding: 2rem;
    text-align: center;
}

.login-body {
    padding: 2rem;
}

/* Sortable Lists */
.sortable {
    list-style: none;
    padding: 0;
}

.sortable li {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 0.5rem;
    cursor: move;
    transition: all 0.3s ease;
}

.sortable li:hover {
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.sort-handle {
    color: var(--admin-secondary);
    cursor: grab;
}

.sort-handle:active {
    cursor: grabbing;
}

/* Progress Bars */
.progress {
    height: 0.5rem;
    border-radius: 0.25rem;
}

.progress-bar {
    background-color: var(--admin-primary);
}

/* Alerts */
.alert {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--admin-success);
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--admin-danger);
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--admin-warning);
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--admin-info);
}

/* Footer */
.admin-footer {
    margin-top: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        padding: 1rem 0;
    }
    
    .stat-card {
        text-align: center;
        margin-bottom: 1rem;
    }
    
    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--admin-secondary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--admin-dark);
}

/* Utility Classes */
.text-primary {
    color: var(--admin-primary) !important;
}

.bg-primary {
    background-color: var(--admin-primary) !important;
}

.border-primary {
    border-color: var(--admin-primary) !important;
}

.cursor-pointer {
    cursor: pointer;
}

.user-select-none {
    user-select: none;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}
