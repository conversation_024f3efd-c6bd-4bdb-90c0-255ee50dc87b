/**
 * Champions Sports Bar & Grill - Admin Panel JavaScript
 */

// Global admin object
window.ChampionsAdmin = {
    // Configuration
    config: {
        ajaxTimeout: 30000,
        autoSaveDelay: 2000,
        maxFileSize: 5 * 1024 * 1024, // 5MB
        allowedImageTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    },
    
    // Initialize admin panel
    init: function() {
        this.initComponents();
        this.bindEvents();
        this.setupAjax();
    },
    
    // Initialize UI components
    initComponents: function() {
        // Initialize tooltips
        this.initTooltips();
        
        // Initialize popovers
        this.initPopovers();
        
        // Initialize DataTables
        this.initDataTables();
        
        // Initialize Select2
        this.initSelect2();
        
        // Initialize Summernote
        this.initSummernote();
        
        // Initialize file uploads
        this.initFileUploads();
        
        // Initialize sortables
        this.initSortables();
    },
    
    // Initialize tooltips
    initTooltips: function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    },
    
    // Initialize popovers
    initPopovers: function() {
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    },
    
    // Initialize DataTables
    initDataTables: function() {
        if ($.fn.DataTable) {
            $('.data-table').each(function() {
                var table = $(this);
                var options = {
                    responsive: true,
                    pageLength: 25,
                    order: [[0, 'desc']],
                    language: {
                        search: "Search:",
                        lengthMenu: "Show _MENU_ entries",
                        info: "Showing _START_ to _END_ of _TOTAL_ entries",
                        paginate: {
                            first: "First",
                            last: "Last",
                            next: "Next",
                            previous: "Previous"
                        }
                    }
                };
                
                // Merge custom options if provided
                if (table.data('table-options')) {
                    options = $.extend(options, table.data('table-options'));
                }
                
                table.DataTable(options);
            });
        }
    },
    
    // Initialize Select2
    initSelect2: function() {
        if ($.fn.select2) {
            $('.select2').select2({
                theme: 'bootstrap-5',
                width: '100%'
            });
        }
    },
    
    // Initialize Summernote
    initSummernote: function() {
        if ($.fn.summernote) {
            $('.summernote').summernote({
                height: 200,
                toolbar: [
                    ['style', ['style']],
                    ['font', ['bold', 'underline', 'clear']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['table', ['table']],
                    ['insert', ['link', 'picture', 'video']],
                    ['view', ['fullscreen', 'codeview', 'help']]
                ],
                callbacks: {
                    onImageUpload: function(files) {
                        ChampionsAdmin.uploadSummernoteImage(files[0], $(this));
                    }
                }
            });
        }
    },
    
    // Initialize file uploads
    initFileUploads: function() {
        // File input change handler
        $('.file-input').on('change', function() {
            ChampionsAdmin.handleFilePreview(this);
        });
        
        // Drag and drop file upload
        $('.file-upload-area').on({
            dragover: function(e) {
                e.preventDefault();
                $(this).addClass('dragover');
            },
            dragleave: function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
            },
            drop: function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
                
                var files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    var fileInput = $(this).find('input[type="file"]')[0];
                    fileInput.files = files;
                    ChampionsAdmin.handleFilePreview(fileInput);
                }
            }
        });
    },
    
    // Initialize sortables
    initSortables: function() {
        if ($.fn.sortable) {
            $('.sortable').sortable({
                handle: '.sort-handle',
                update: function(event, ui) {
                    var order = $(this).sortable('toArray', {attribute: 'data-id'});
                    var table = $(this).data('table');
                    
                    if (table) {
                        ChampionsAdmin.updateSortOrder(table, order);
                    }
                }
            });
        }
    },
    
    // Bind global events
    bindEvents: function() {
        // Confirm delete actions
        $(document).on('click', '.btn-delete, .delete-btn', function(e) {
            if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
                e.preventDefault();
                return false;
            }
        });
        
        // Auto-save forms
        $(document).on('input change', '.auto-save', function() {
            ChampionsAdmin.autoSaveForm($(this).closest('form'));
        });
        
        // Toggle status buttons
        $(document).on('click', '.toggle-status', function(e) {
            e.preventDefault();
            ChampionsAdmin.toggleStatus($(this));
        });
        
        // Copy to clipboard
        $(document).on('click', '.copy-to-clipboard', function(e) {
            e.preventDefault();
            var text = $(this).data('text') || $(this).text();
            ChampionsAdmin.copyToClipboard(text);
        });
        
        // Auto-hide alerts
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    },
    
    // Setup AJAX defaults
    setupAjax: function() {
        $.ajaxSetup({
            timeout: this.config.ajaxTimeout,
            beforeSend: function(xhr, settings) {
                // Add CSRF token to all AJAX requests
                var csrfToken = $('meta[name="csrf-token"]').attr('content');
                if (csrfToken) {
                    xhr.setRequestHeader('X-CSRF-Token', csrfToken);
                }
            }
        });
    },
    
    // Handle file preview
    handleFilePreview: function(input) {
        var file = input.files[0];
        var preview = $(input).siblings('.file-preview');
        
        if (file) {
            // Validate file
            if (!this.validateFile(file)) {
                input.value = '';
                preview.html('');
                return;
            }
            
            // Show preview for images
            if (file.type.startsWith('image/')) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    preview.html('<img src="' + e.target.result + '" class="img-thumbnail" style="max-width: 200px;">');
                };
                reader.readAsDataURL(file);
            } else {
                preview.html('<div class="alert alert-info"><i class="fas fa-file me-2"></i>' + file.name + '</div>');
            }
        } else {
            preview.html('');
        }
    },
    
    // Validate file
    validateFile: function(file) {
        // Check file size
        if (file.size > this.config.maxFileSize) {
            this.showAlert('File size too large. Maximum size is ' + this.formatFileSize(this.config.maxFileSize), 'danger');
            return false;
        }
        
        // Check file type for images
        if (file.type.startsWith('image/') && !this.config.allowedImageTypes.includes(file.type)) {
            this.showAlert('Invalid image type. Only JPG, PNG, GIF, and WebP files are allowed.', 'danger');
            return false;
        }
        
        return true;
    },
    
    // Auto-save form
    autoSaveForm: function(form) {
        var self = this;
        
        // Clear existing timeout
        clearTimeout(form.data('autoSaveTimeout'));
        
        // Set new timeout
        form.data('autoSaveTimeout', setTimeout(function() {
            var formData = form.serialize() + '&auto_save=1';
            
            $.ajax({
                url: form.attr('action') || window.location.href,
                method: 'POST',
                data: formData,
                success: function(response) {
                    $('.auto-save-indicator').text('Saved').fadeIn().delay(2000).fadeOut();
                },
                error: function() {
                    $('.auto-save-indicator').text('Save failed').fadeIn().delay(2000).fadeOut();
                }
            });
        }, this.config.autoSaveDelay));
    },
    
    // Toggle status
    toggleStatus: function(button) {
        var url = button.data('url');
        var id = button.data('id');
        var table = button.data('table');
        var field = button.data('field') || 'is_active';
        
        $.ajax({
            url: url || 'ajax/toggle-status.php',
            method: 'POST',
            data: {
                id: id,
                table: table,
                field: field,
                csrf_token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    // Update button appearance
                    button.toggleClass('btn-success btn-secondary');
                    var icon = button.find('i');
                    icon.toggleClass('fa-check fa-times');
                    
                    ChampionsAdmin.showAlert('Status updated successfully', 'success');
                } else {
                    ChampionsAdmin.showAlert(response.message || 'Failed to update status', 'danger');
                }
            },
            error: function() {
                ChampionsAdmin.showAlert('Failed to update status', 'danger');
            }
        });
    },
    
    // Update sort order
    updateSortOrder: function(table, order) {
        $.ajax({
            url: 'ajax/update-order.php',
            method: 'POST',
            data: {
                table: table,
                order: order,
                csrf_token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    ChampionsAdmin.showAlert('Order updated successfully', 'success');
                } else {
                    ChampionsAdmin.showAlert(response.message || 'Failed to update order', 'danger');
                }
            },
            error: function() {
                ChampionsAdmin.showAlert('Failed to update order', 'danger');
            }
        });
    },
    
    // Upload image for Summernote
    uploadSummernoteImage: function(file, editor) {
        var formData = new FormData();
        formData.append('image', file);
        formData.append('csrf_token', $('meta[name="csrf-token"]').attr('content'));
        
        $.ajax({
            url: 'ajax/upload-image.php',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    editor.summernote('insertImage', response.url);
                } else {
                    ChampionsAdmin.showAlert(response.message || 'Failed to upload image', 'danger');
                }
            },
            error: function() {
                ChampionsAdmin.showAlert('Failed to upload image', 'danger');
            }
        });
    },
    
    // Show alert message
    showAlert: function(message, type) {
        type = type || 'info';
        
        var alertHtml = '<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
                       '<i class="fas fa-' + this.getAlertIcon(type) + ' me-2"></i>' +
                       message +
                       '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                       '</div>';
        
        $('.main-content .container-fluid').prepend(alertHtml);
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            $('.alert').first().fadeOut('slow');
        }, 5000);
    },
    
    // Get alert icon
    getAlertIcon: function(type) {
        var icons = {
            'success': 'check-circle',
            'danger': 'exclamation-circle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    },
    
    // Copy to clipboard
    copyToClipboard: function(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(function() {
                ChampionsAdmin.showAlert('Copied to clipboard!', 'success');
            });
        } else {
            // Fallback for older browsers
            var textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            ChampionsAdmin.showAlert('Copied to clipboard!', 'success');
        }
    },
    
    // Format file size
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 Bytes';
        var k = 1024;
        var sizes = ['Bytes', 'KB', 'MB', 'GB'];
        var i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    // Confirm action
    confirm: function(message, callback) {
        if (confirm(message)) {
            if (typeof callback === 'function') {
                callback();
            }
            return true;
        }
        return false;
    },
    
    // Loading state
    setLoading: function(element, loading) {
        if (loading) {
            element.addClass('loading').prop('disabled', true);
            element.find('.btn-text').hide();
            element.find('.btn-loading').show();
        } else {
            element.removeClass('loading').prop('disabled', false);
            element.find('.btn-text').show();
            element.find('.btn-loading').hide();
        }
    }
};

// Initialize when document is ready
$(document).ready(function() {
    ChampionsAdmin.init();
});

// Export for global access
window.CA = ChampionsAdmin;
