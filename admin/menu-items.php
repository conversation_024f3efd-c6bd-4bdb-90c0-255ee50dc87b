<?php
/**
 * Champions Sports Bar & Grill - Menu Items Management
 */

require_once 'includes/auth.php';
require_once 'config/database.php';

// Check if user is logged in
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$db = getDB();
$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate CSRF token
        if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
            throw new Exception('Invalid CSRF token');
        }

        $action = $_POST['action'] ?? '';
        
        if ($action === 'add') {
            $name = trim($_POST['name'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $price = floatval($_POST['price'] ?? 0);
            $categoryId = (int)($_POST['category_id'] ?? 0);
            $isActive = isset($_POST['is_active']) ? 1 : 0;
            $isFeatured = isset($_POST['is_featured']) ? 1 : 0;
            $allergens = trim($_POST['allergens'] ?? '');
            $calories = $_POST['calories'] ? (int)$_POST['calories'] : null;
            $spiceLevel = $_POST['spice_level'] ? (int)$_POST['spice_level'] : null;
            
            if (empty($name)) {
                throw new Exception('Item name is required');
            }
            
            if ($price <= 0) {
                throw new Exception('Price must be greater than 0');
            }
            
            if ($categoryId <= 0) {
                throw new Exception('Please select a category');
            }
            
            // Check if category exists
            $category = $db->fetch("SELECT id FROM menu_categories WHERE id = :id", ['id' => $categoryId]);
            if (!$category) {
                throw new Exception('Selected category does not exist');
            }
            
            $db->query(
                "INSERT INTO menu_items (category_id, name, description, price, allergens, calories, spice_level, is_active, is_featured, created_at, updated_at) VALUES (:category_id, :name, :description, :price, :allergens, :calories, :spice_level, :is_active, :is_featured, NOW(), NOW())",
                [
                    'category_id' => $categoryId,
                    'name' => $name,
                    'description' => $description,
                    'price' => $price,
                    'allergens' => $allergens,
                    'calories' => $calories,
                    'spice_level' => $spiceLevel,
                    'is_active' => $isActive,
                    'is_featured' => $isFeatured
                ]
            );
            
            $message = 'Menu item added successfully!';
            $messageType = 'success';
            
        } elseif ($action === 'edit') {
            $id = (int)($_POST['id'] ?? 0);
            $name = trim($_POST['name'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $price = floatval($_POST['price'] ?? 0);
            $categoryId = (int)($_POST['category_id'] ?? 0);
            $isActive = isset($_POST['is_active']) ? 1 : 0;
            $isFeatured = isset($_POST['is_featured']) ? 1 : 0;
            $allergens = trim($_POST['allergens'] ?? '');
            $calories = $_POST['calories'] ? (int)$_POST['calories'] : null;
            $spiceLevel = $_POST['spice_level'] ? (int)$_POST['spice_level'] : null;
            
            if (empty($name)) {
                throw new Exception('Item name is required');
            }
            
            if ($price <= 0) {
                throw new Exception('Price must be greater than 0');
            }
            
            if ($categoryId <= 0) {
                throw new Exception('Please select a category');
            }
            
            // Check if item exists
            $existing = $db->fetch("SELECT id FROM menu_items WHERE id = :id", ['id' => $id]);
            if (!$existing) {
                throw new Exception('Menu item not found');
            }
            
            // Check if category exists
            $category = $db->fetch("SELECT id FROM menu_categories WHERE id = :id", ['id' => $categoryId]);
            if (!$category) {
                throw new Exception('Selected category does not exist');
            }
            
            $db->query(
                "UPDATE menu_items SET category_id = :category_id, name = :name, description = :description, price = :price, allergens = :allergens, calories = :calories, spice_level = :spice_level, is_active = :is_active, is_featured = :is_featured, updated_at = NOW() WHERE id = :id",
                [
                    'category_id' => $categoryId,
                    'name' => $name,
                    'description' => $description,
                    'price' => $price,
                    'allergens' => $allergens,
                    'calories' => $calories,
                    'spice_level' => $spiceLevel,
                    'is_active' => $isActive,
                    'is_featured' => $isFeatured,
                    'id' => $id
                ]
            );
            
            $message = 'Menu item updated successfully!';
            $messageType = 'success';
            
        } elseif ($action === 'delete') {
            $id = (int)($_POST['id'] ?? 0);
            
            // Check if item exists
            $existing = $db->fetch("SELECT id FROM menu_items WHERE id = :id", ['id' => $id]);
            if (!$existing) {
                throw new Exception('Menu item not found');
            }
            
            $db->query("DELETE FROM menu_items WHERE id = :id", ['id' => $id]);
            
            $message = 'Menu item deleted successfully!';
            $messageType = 'success';
        }
        
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Handle AJAX requests for quick actions
if (isset($_GET['ajax'])) {
    header('Content-Type: application/json');
    
    try {
        if ($_GET['ajax'] === 'toggle_status' && isset($_GET['id'])) {
            $id = (int)$_GET['id'];
            $item = $db->fetch("SELECT is_active FROM menu_items WHERE id = :id", ['id' => $id]);
            
            if ($item) {
                $newStatus = $item['is_active'] ? 0 : 1;
                $db->query("UPDATE menu_items SET is_active = :status, updated_at = NOW() WHERE id = :id", 
                          ['status' => $newStatus, 'id' => $id]);
                echo json_encode(['success' => true, 'new_status' => $newStatus]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Menu item not found']);
            }
        } elseif ($_GET['ajax'] === 'toggle_featured' && isset($_GET['id'])) {
            $id = (int)$_GET['id'];
            $item = $db->fetch("SELECT is_featured FROM menu_items WHERE id = :id", ['id' => $id]);
            
            if ($item) {
                $newStatus = $item['is_featured'] ? 0 : 1;
                $db->query("UPDATE menu_items SET is_featured = :status, updated_at = NOW() WHERE id = :id", 
                          ['status' => $newStatus, 'id' => $id]);
                echo json_encode(['success' => true, 'new_status' => $newStatus]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Menu item not found']);
            }
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

// Get filter parameters
$categoryFilter = isset($_GET['category']) ? (int)$_GET['category'] : 0;
$searchQuery = isset($_GET['search']) ? trim($_GET['search']) : '';

// Build query
$whereConditions = [];
$params = [];

if ($categoryFilter > 0) {
    $whereConditions[] = "mi.category_id = :category_id";
    $params['category_id'] = $categoryFilter;
}

if (!empty($searchQuery)) {
    $whereConditions[] = "(mi.name LIKE :search OR mi.description LIKE :search)";
    $params['search'] = '%' . $searchQuery . '%';
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Get menu items with category names
$menuItems = $db->fetchAll("
    SELECT mi.*, mc.name as category_name 
    FROM menu_items mi 
    JOIN menu_categories mc ON mi.category_id = mc.id 
    {$whereClause}
    ORDER BY mc.sort_order ASC, mi.name ASC
", $params);

// Get all categories for filter dropdown
$categories = $db->fetchAll("SELECT * FROM menu_categories WHERE is_active = 1 ORDER BY sort_order ASC, name ASC");

// Generate CSRF token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

$pageTitle = 'Menu Items';
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Menu Items</h1>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addItemModal">
                    <i class="fas fa-plus me-2"></i>Add Menu Item
                </button>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="category" class="form-label">Filter by Category</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">All Categories</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>" 
                                            <?php echo $categoryFilter == $category['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="search" class="form-label">Search Items</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($searchQuery); ?>" 
                                   placeholder="Search by name or description...">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-outline-primary">Filter</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <?php if (empty($menuItems)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                            <h5>No menu items found</h5>
                            <p class="text-muted">
                                <?php if ($categoryFilter > 0 || !empty($searchQuery)): ?>
                                    Try adjusting your filters or <a href="?">view all items</a>.
                                <?php else: ?>
                                    Start by adding your first menu item.
                                <?php endif; ?>
                            </p>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addItemModal">
                                <i class="fas fa-plus me-2"></i>Add Menu Item
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Category</th>
                                        <th>Price</th>
                                        <th>Description</th>
                                        <th>Features</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($menuItems as $item): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if ($item['image_url']): ?>
                                                        <img src="/<?php echo htmlspecialchars($item['image_url']); ?>"
                                                             alt="<?php echo htmlspecialchars($item['name']); ?>"
                                                             class="me-3 rounded" style="width: 50px; height: 50px; object-fit: cover;">
                                                    <?php else: ?>
                                                        <div class="me-3 bg-light rounded d-flex align-items-center justify-content-center"
                                                             style="width: 50px; height: 50px;">
                                                            <i class="fas fa-image text-muted"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($item['name']); ?></strong>
                                                        <?php if ($item['is_featured']): ?>
                                                            <span class="badge bg-warning ms-2">Featured</span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary"><?php echo htmlspecialchars($item['category_name']); ?></span>
                                            </td>
                                            <td>
                                                <strong>$<?php echo number_format($item['price'], 2); ?></strong>
                                            </td>
                                            <td>
                                                <small><?php echo htmlspecialchars(substr($item['description'], 0, 100)); ?><?php echo strlen($item['description']) > 100 ? '...' : ''; ?></small>
                                            </td>
                                            <td>
                                                <div class="d-flex flex-column gap-1">
                                                    <?php if ($item['calories']): ?>
                                                        <small class="text-muted"><?php echo $item['calories']; ?> cal</small>
                                                    <?php endif; ?>
                                                    <?php if ($item['spice_level']): ?>
                                                        <small class="text-danger">
                                                            <?php echo str_repeat('🌶️', $item['spice_level']); ?>
                                                        </small>
                                                    <?php endif; ?>
                                                    <?php if ($item['allergens']): ?>
                                                        <small class="text-warning">⚠️ Allergens</small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" 
                                                           <?php echo $item['is_active'] ? 'checked' : ''; ?>
                                                           onchange="toggleStatus(<?php echo $item['id']; ?>)">
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" class="btn btn-outline-info"
                                                            onclick="uploadImage(<?php echo $item['id']; ?>, '<?php echo htmlspecialchars($item['name']); ?>')"
                                                            title="Upload Image">
                                                        <i class="fas fa-camera"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-warning"
                                                            onclick="toggleFeatured(<?php echo $item['id']; ?>)"
                                                            title="Toggle Featured">
                                                        <i class="fas fa-star"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-primary"
                                                            onclick="editItem(<?php echo htmlspecialchars(json_encode($item)); ?>)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-danger"
                                                            onclick="deleteItem(<?php echo $item['id']; ?>, '<?php echo htmlspecialchars($item['name']); ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Item Modal -->
<div class="modal fade" id="addItemModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <input type="hidden" name="action" value="add">

                <div class="modal-header">
                    <h5 class="modal-title">Add New Menu Item</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>

                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="add_name" class="form-label">Item Name</label>
                                <input type="text" class="form-control" id="add_name" name="name" required>
                                <div class="invalid-feedback">Please provide an item name.</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="add_price" class="form-label">Price</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="add_price" name="price"
                                           step="0.01" min="0.01" required>
                                    <div class="invalid-feedback">Please provide a valid price.</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="add_category_id" class="form-label">Category</label>
                        <select class="form-select" id="add_category_id" name="category_id" required>
                            <option value="">Select a category</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="invalid-feedback">Please select a category.</div>
                    </div>

                    <div class="mb-3">
                        <label for="add_description" class="form-label">Description</label>
                        <textarea class="form-control" id="add_description" name="description" rows="3"
                                  placeholder="Describe the item, ingredients, preparation method..."></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="add_calories" class="form-label">Calories (optional)</label>
                                <input type="number" class="form-control" id="add_calories" name="calories" min="0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="add_spice_level" class="form-label">Spice Level</label>
                                <select class="form-select" id="add_spice_level" name="spice_level">
                                    <option value="">None</option>
                                    <option value="1">🌶️ Mild</option>
                                    <option value="2">🌶️🌶️ Medium</option>
                                    <option value="3">🌶️🌶️🌶️ Hot</option>
                                    <option value="4">🌶️🌶️🌶️🌶️ Very Hot</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="add_is_active" name="is_active" checked>
                                    <label class="form-check-label" for="add_is_active">Active</label>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="add_is_featured" name="is_featured">
                                    <label class="form-check-label" for="add_is_featured">Featured</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="add_allergens" class="form-label">Allergens (optional)</label>
                        <input type="text" class="form-control" id="add_allergens" name="allergens"
                               placeholder="e.g., Contains nuts, dairy, gluten">
                        <div class="form-text">List any allergens or dietary restrictions</div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Item</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Item Modal -->
<div class="modal fade" id="editItemModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="id" id="edit_id">

                <div class="modal-header">
                    <h5 class="modal-title">Edit Menu Item</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>

                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="edit_name" class="form-label">Item Name</label>
                                <input type="text" class="form-control" id="edit_name" name="name" required>
                                <div class="invalid-feedback">Please provide an item name.</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_price" class="form-label">Price</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="edit_price" name="price"
                                           step="0.01" min="0.01" required>
                                    <div class="invalid-feedback">Please provide a valid price.</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_category_id" class="form-label">Category</label>
                        <select class="form-select" id="edit_category_id" name="category_id" required>
                            <option value="">Select a category</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="invalid-feedback">Please select a category.</div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_calories" class="form-label">Calories (optional)</label>
                                <input type="number" class="form-control" id="edit_calories" name="calories" min="0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_spice_level" class="form-label">Spice Level</label>
                                <select class="form-select" id="edit_spice_level" name="spice_level">
                                    <option value="">None</option>
                                    <option value="1">🌶️ Mild</option>
                                    <option value="2">🌶️🌶️ Medium</option>
                                    <option value="3">🌶️🌶️🌶️ Hot</option>
                                    <option value="4">🌶️🌶️🌶️🌶️ Very Hot</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                                    <label class="form-check-label" for="edit_is_active">Active</label>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="edit_is_featured" name="is_featured">
                                    <label class="form-check-label" for="edit_is_featured">Featured</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_allergens" class="form-label">Allergens (optional)</label>
                        <input type="text" class="form-control" id="edit_allergens" name="allergens">
                        <div class="form-text">List any allergens or dietary restrictions</div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Item</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteItemModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="id" id="delete_id">

                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>

                <div class="modal-body">
                    <p>Are you sure you want to delete the menu item "<span id="delete_name"></span>"?</p>
                    <p class="text-danger"><small>This action cannot be undone.</small></p>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete Item</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Image Upload Modal -->
<div class="modal fade" id="uploadImageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Upload Image for <span id="upload_item_name"></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>

            <div class="modal-body">
                <form id="imageUploadForm" enctype="multipart/form-data">
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                    <input type="hidden" name="upload_type" value="menu_item">
                    <input type="hidden" name="item_id" id="upload_item_id">

                    <div class="mb-3">
                        <label for="image_file" class="form-label">Select Image</label>
                        <input type="file" class="form-control" id="image_file" name="image"
                               accept="image/jpeg,image/png,image/gif,image/webp" required>
                        <div class="form-text">
                            Supported formats: JPEG, PNG, GIF, WebP. Maximum size: 5MB.
                            Recommended dimensions: 800x600 pixels.
                        </div>
                    </div>

                    <div id="image_preview" class="mb-3" style="display: none;">
                        <label class="form-label">Preview</label>
                        <div class="border rounded p-2">
                            <img id="preview_image" src="" alt="Preview" class="img-fluid" style="max-height: 200px;">
                        </div>
                    </div>

                    <div id="upload_progress" class="mb-3" style="display: none;">
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>

                    <div id="upload_message" class="alert" style="display: none;"></div>
                </form>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitImageUpload()">Upload Image</button>
            </div>
        </div>
    </div>
</div>

<script>
function editItem(item) {
    document.getElementById('edit_id').value = item.id;
    document.getElementById('edit_name').value = item.name;
    document.getElementById('edit_description').value = item.description || '';
    document.getElementById('edit_price').value = item.price;
    document.getElementById('edit_category_id').value = item.category_id;
    document.getElementById('edit_calories').value = item.calories || '';
    document.getElementById('edit_spice_level').value = item.spice_level || '';
    document.getElementById('edit_allergens').value = item.allergens || '';
    document.getElementById('edit_is_active').checked = item.is_active == 1;
    document.getElementById('edit_is_featured').checked = item.is_featured == 1;

    new bootstrap.Modal(document.getElementById('editItemModal')).show();
}

function deleteItem(id, name) {
    document.getElementById('delete_id').value = id;
    document.getElementById('delete_name').textContent = name;

    new bootstrap.Modal(document.getElementById('deleteItemModal')).show();
}

function toggleStatus(id) {
    fetch(`?ajax=toggle_status&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                alert('Error: ' + data.message);
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the status');
            location.reload();
        });
}

function toggleFeatured(id) {
    fetch(`?ajax=toggle_featured&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the featured status');
        });
}

function uploadImage(id, name) {
    document.getElementById('upload_item_id').value = id;
    document.getElementById('upload_item_name').textContent = name;
    document.getElementById('image_file').value = '';
    document.getElementById('image_preview').style.display = 'none';
    document.getElementById('upload_progress').style.display = 'none';
    document.getElementById('upload_message').style.display = 'none';

    new bootstrap.Modal(document.getElementById('uploadImageModal')).show();
}

function submitImageUpload() {
    const form = document.getElementById('imageUploadForm');
    const formData = new FormData(form);
    const progressBar = document.querySelector('#upload_progress .progress-bar');
    const messageDiv = document.getElementById('upload_message');

    // Show progress bar
    document.getElementById('upload_progress').style.display = 'block';
    progressBar.style.width = '0%';

    // Hide previous messages
    messageDiv.style.display = 'none';

    fetch('upload-image.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        progressBar.style.width = '100%';

        if (data.success) {
            messageDiv.className = 'alert alert-success';
            messageDiv.textContent = data.message;
            messageDiv.style.display = 'block';

            // Reload page after short delay
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            messageDiv.className = 'alert alert-danger';
            messageDiv.textContent = data.message;
            messageDiv.style.display = 'block';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        progressBar.style.width = '100%';
        messageDiv.className = 'alert alert-danger';
        messageDiv.textContent = 'An error occurred while uploading the image';
        messageDiv.style.display = 'block';
    });
}

// Image preview functionality
document.addEventListener('DOMContentLoaded', function() {
    const imageInput = document.getElementById('image_file');
    const previewDiv = document.getElementById('image_preview');
    const previewImg = document.getElementById('preview_image');

    if (imageInput) {
        imageInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    previewDiv.style.display = 'block';
                };
                reader.readAsDataURL(file);
            } else {
                previewDiv.style.display = 'none';
            }
        });
    }
});

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?php include 'includes/footer.php'; ?>
