<?php
/**
 * Champions Sports Bar & Grill - Menu Categories Management
 */

require_once 'includes/auth.php';
require_once 'config/database.php';

// Check if user is logged in
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$db = getDB();
$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate CSRF token
        if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
            throw new Exception('Invalid CSRF token');
        }

        $action = $_POST['action'] ?? '';
        
        if ($action === 'add') {
            $name = trim($_POST['name'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $sortOrder = (int)($_POST['sort_order'] ?? 0);
            $isActive = isset($_POST['is_active']) ? 1 : 0;
            
            if (empty($name)) {
                throw new Exception('Category name is required');
            }
            
            // Check if category already exists
            $existing = $db->fetch("SELECT id FROM menu_categories WHERE name = :name", ['name' => $name]);
            if ($existing) {
                throw new Exception('A category with this name already exists');
            }
            
            $db->query(
                "INSERT INTO menu_categories (name, description, sort_order, is_active, created_at, updated_at) VALUES (:name, :description, :sort_order, :is_active, NOW(), NOW())",
                [
                    'name' => $name,
                    'description' => $description,
                    'sort_order' => $sortOrder,
                    'is_active' => $isActive
                ]
            );
            
            $message = 'Category added successfully!';
            $messageType = 'success';
            
        } elseif ($action === 'edit') {
            $id = (int)($_POST['id'] ?? 0);
            $name = trim($_POST['name'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $sortOrder = (int)($_POST['sort_order'] ?? 0);
            $isActive = isset($_POST['is_active']) ? 1 : 0;
            
            if (empty($name)) {
                throw new Exception('Category name is required');
            }
            
            // Check if category exists
            $existing = $db->fetch("SELECT id FROM menu_categories WHERE id = :id", ['id' => $id]);
            if (!$existing) {
                throw new Exception('Category not found');
            }
            
            // Check if name is taken by another category
            $duplicate = $db->fetch("SELECT id FROM menu_categories WHERE name = :name AND id != :id", ['name' => $name, 'id' => $id]);
            if ($duplicate) {
                throw new Exception('A category with this name already exists');
            }
            
            $db->query(
                "UPDATE menu_categories SET name = :name, description = :description, sort_order = :sort_order, is_active = :is_active, updated_at = NOW() WHERE id = :id",
                [
                    'name' => $name,
                    'description' => $description,
                    'sort_order' => $sortOrder,
                    'is_active' => $isActive,
                    'id' => $id
                ]
            );
            
            $message = 'Category updated successfully!';
            $messageType = 'success';
            
        } elseif ($action === 'delete') {
            $id = (int)($_POST['id'] ?? 0);
            
            // Check if category exists
            $existing = $db->fetch("SELECT id FROM menu_categories WHERE id = :id", ['id' => $id]);
            if (!$existing) {
                throw new Exception('Category not found');
            }
            
            // Check if category has menu items
            $hasItems = $db->fetch("SELECT id FROM menu_items WHERE category_id = :id LIMIT 1", ['id' => $id]);
            if ($hasItems) {
                throw new Exception('Cannot delete category that contains menu items. Please move or delete the items first.');
            }
            
            $db->query("DELETE FROM menu_categories WHERE id = :id", ['id' => $id]);
            
            $message = 'Category deleted successfully!';
            $messageType = 'success';
        }
        
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Handle AJAX requests for quick actions
if (isset($_GET['ajax'])) {
    header('Content-Type: application/json');
    
    try {
        if ($_GET['ajax'] === 'toggle_status' && isset($_GET['id'])) {
            $id = (int)$_GET['id'];
            $category = $db->fetch("SELECT is_active FROM menu_categories WHERE id = :id", ['id' => $id]);
            
            if ($category) {
                $newStatus = $category['is_active'] ? 0 : 1;
                $db->query("UPDATE menu_categories SET is_active = :status, updated_at = NOW() WHERE id = :id", 
                          ['status' => $newStatus, 'id' => $id]);
                echo json_encode(['success' => true, 'new_status' => $newStatus]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Category not found']);
            }
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

// Get all categories
$categories = $db->fetchAll("SELECT * FROM menu_categories ORDER BY sort_order ASC, name ASC");

// Generate CSRF token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

$pageTitle = 'Menu Categories';
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Menu Categories</h1>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                    <i class="fas fa-plus me-2"></i>Add Category
                </button>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="card">
                <div class="card-body">
                    <?php if (empty($categories)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                            <h5>No categories found</h5>
                            <p class="text-muted">Start by adding your first menu category.</p>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                                <i class="fas fa-plus me-2"></i>Add First Category
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Description</th>
                                        <th>Sort Order</th>
                                        <th>Items Count</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($categories as $category): 
                                        $itemCount = $db->fetch("SELECT COUNT(*) as count FROM menu_items WHERE category_id = :id", ['id' => $category['id']]);
                                    ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($category['name']); ?></strong>
                                            </td>
                                            <td><?php echo htmlspecialchars($category['description'] ?: 'No description'); ?></td>
                                            <td>
                                                <span class="badge bg-secondary"><?php echo $category['sort_order']; ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?php echo $itemCount['count']; ?> items</span>
                                            </td>
                                            <td>
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" 
                                                           <?php echo $category['is_active'] ? 'checked' : ''; ?>
                                                           onchange="toggleStatus(<?php echo $category['id']; ?>)">
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" class="btn btn-outline-primary" 
                                                            onclick="editCategory(<?php echo htmlspecialchars(json_encode($category)); ?>)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-danger" 
                                                            onclick="deleteCategory(<?php echo $category['id']; ?>, '<?php echo htmlspecialchars($category['name']); ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <input type="hidden" name="action" value="add">
                
                <div class="modal-header">
                    <h5 class="modal-title">Add New Category</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="add_name" class="form-label">Category Name</label>
                        <input type="text" class="form-control" id="add_name" name="name" required>
                        <div class="invalid-feedback">Please provide a category name.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="add_description" class="form-label">Description</label>
                        <textarea class="form-control" id="add_description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_sort_order" class="form-label">Sort Order</label>
                                <input type="number" class="form-control" id="add_sort_order" name="sort_order" value="0" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="add_is_active" name="is_active" checked>
                                    <label class="form-check-label" for="add_is_active">Active</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Category</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Category Modal -->
<div class="modal fade" id="editCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="id" id="edit_id">

                <div class="modal-header">
                    <h5 class="modal-title">Edit Category</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>

                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Category Name</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                        <div class="invalid-feedback">Please provide a category name.</div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_sort_order" class="form-label">Sort Order</label>
                                <input type="number" class="form-control" id="edit_sort_order" name="sort_order" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                                    <label class="form-check-label" for="edit_is_active">Active</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Category</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="id" id="delete_id">

                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>

                <div class="modal-body">
                    <p>Are you sure you want to delete the category "<span id="delete_name"></span>"?</p>
                    <p class="text-danger"><small>This action cannot be undone.</small></p>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete Category</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editCategory(category) {
    document.getElementById('edit_id').value = category.id;
    document.getElementById('edit_name').value = category.name;
    document.getElementById('edit_description').value = category.description || '';
    document.getElementById('edit_sort_order').value = category.sort_order;
    document.getElementById('edit_is_active').checked = category.is_active == 1;

    new bootstrap.Modal(document.getElementById('editCategoryModal')).show();
}

function deleteCategory(id, name) {
    document.getElementById('delete_id').value = id;
    document.getElementById('delete_name').textContent = name;

    new bootstrap.Modal(document.getElementById('deleteCategoryModal')).show();
}

function toggleStatus(id) {
    fetch(`?ajax=toggle_status&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                alert('Error: ' + data.message);
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the status');
            location.reload();
        });
}

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?php include 'includes/footer.php'; ?>
