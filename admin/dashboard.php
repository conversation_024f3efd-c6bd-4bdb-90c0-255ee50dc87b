<?php
/**
 * Champions Sports Bar & Grill - Admin Dashboard
 */

$pageTitle = 'Dashboard';
require_once 'includes/header.php';

// Get dashboard statistics
$db = getDB();

// Get counts for various items
$stats = [
    'menu_items' => $db->fetch("SELECT COUNT(*) as count FROM menu_items WHERE is_available = 1")['count'],
    'total_menu_items' => $db->fetch("SELECT COUNT(*) as count FROM menu_items")['count'],
    'gallery_images' => $db->fetch("SELECT COUNT(*) as count FROM gallery_images")['count'],
    'active_events' => $db->fetch("SELECT COUNT(*) as count FROM events WHERE is_active = 1 AND (event_date >= CURDATE() OR is_recurring = 1)")['count'],
    'job_postings' => $db->fetch("SELECT COUNT(*) as count FROM job_postings WHERE is_active = 1")['count'],
    'new_applications' => $db->fetch("SELECT COUNT(*) as count FROM job_applications WHERE status = 'new'")['count'],
    'new_messages' => $db->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'new'")['count'],
    'total_applications' => $db->fetch("SELECT COUNT(*) as count FROM job_applications")['count']
];

// Get recent activity
$recentMessages = $db->fetchAll(
    "SELECT * FROM contact_messages ORDER BY created_at DESC LIMIT 5"
);

$recentApplications = $db->fetchAll(
    "SELECT ja.*, jp.title as job_title 
     FROM job_applications ja 
     LEFT JOIN job_postings jp ON ja.job_posting_id = jp.id 
     ORDER BY ja.applied_at DESC LIMIT 5"
);

// Get upcoming events
$upcomingEvents = $db->fetchAll(
    "SELECT * FROM events 
     WHERE is_active = 1 AND (event_date >= CURDATE() OR is_recurring = 1)
     ORDER BY event_date ASC LIMIT 5"
);

// Get menu items by category for chart
$menuByCategory = $db->fetchAll(
    "SELECT mc.name, COUNT(mi.id) as count 
     FROM menu_categories mc 
     LEFT JOIN menu_items mi ON mc.id = mi.category_id 
     GROUP BY mc.id, mc.name 
     ORDER BY mc.sort_order"
);
?>

<div class="row mb-4">
    <div class="col-12">
        <h1 class="h2 mb-0">
            <i class="fas fa-tachometer-alt me-2"></i>
            Dashboard
        </h1>
        <p class="text-muted">Welcome back, <?php echo htmlspecialchars($currentUser['first_name']); ?>!</p>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stat-number"><?php echo $stats['menu_items']; ?></div>
                    <div class="stat-label">Active Menu Items</div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-utensils"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card success">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stat-number"><?php echo $stats['gallery_images']; ?></div>
                    <div class="stat-label">Gallery Images</div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-images"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card info">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stat-number"><?php echo $stats['active_events']; ?></div>
                    <div class="stat-label">Active Events</div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-calendar"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stat-card warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stat-number"><?php echo $stats['new_applications']; ?></div>
                    <div class="stat-label">New Applications</div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="menu-items.php?action=add" class="btn btn-outline-primary w-100">
                            <i class="fas fa-plus-circle d-block mb-2"></i>
                            Add Menu Item
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="events.php?action=add" class="btn btn-outline-success w-100">
                            <i class="fas fa-calendar-plus d-block mb-2"></i>
                            Add Event
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="gallery.php?action=upload" class="btn btn-outline-info w-100">
                            <i class="fas fa-upload d-block mb-2"></i>
                            Upload Images
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="job-postings.php?action=add" class="btn btn-outline-warning w-100">
                            <i class="fas fa-briefcase d-block mb-2"></i>
                            Post Job
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="content.php" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-edit d-block mb-2"></i>
                            Edit Content
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="../index.php" target="_blank" class="btn btn-outline-dark w-100">
                            <i class="fas fa-external-link-alt d-block mb-2"></i>
                            View Site
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Row -->
<div class="row">
    <!-- Recent Messages -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-envelope me-2"></i>
                    Recent Messages
                </h5>
                <a href="messages.php" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <?php if (empty($recentMessages)): ?>
                    <p class="text-muted text-center py-3">No recent messages</p>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recentMessages as $message): ?>
                            <div class="list-group-item px-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">
                                            <?php echo htmlspecialchars($message['name']); ?>
                                            <?php if ($message['status'] === 'new'): ?>
                                                <span class="badge bg-danger ms-1">New</span>
                                            <?php endif; ?>
                                        </h6>
                                        <p class="mb-1 text-truncate" style="max-width: 300px;">
                                            <?php echo htmlspecialchars($message['message']); ?>
                                        </p>
                                        <small class="text-muted">
                                            <?php echo date('M j, Y g:i A', strtotime($message['created_at'])); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Recent Applications -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-alt me-2"></i>
                    Recent Applications
                </h5>
                <a href="applications.php" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <?php if (empty($recentApplications)): ?>
                    <p class="text-muted text-center py-3">No recent applications</p>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recentApplications as $application): ?>
                            <div class="list-group-item px-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">
                                            <?php echo htmlspecialchars($application['first_name'] . ' ' . $application['last_name']); ?>
                                            <?php if ($application['status'] === 'new'): ?>
                                                <span class="badge bg-warning">New</span>
                                            <?php endif; ?>
                                        </h6>
                                        <p class="mb-1">
                                            <strong>Position:</strong> <?php echo htmlspecialchars($application['job_title'] ?? 'Unknown'); ?>
                                        </p>
                                        <small class="text-muted">
                                            Applied: <?php echo date('M j, Y g:i A', strtotime($application['applied_at'])); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row">
    <!-- Menu Items Chart -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    Menu Items by Category
                </h5>
            </div>
            <div class="card-body">
                <canvas id="menuChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Upcoming Events -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>
                    Upcoming Events
                </h5>
                <a href="events.php" class="btn btn-sm btn-outline-primary">Manage Events</a>
            </div>
            <div class="card-body">
                <?php if (empty($upcomingEvents)): ?>
                    <p class="text-muted text-center py-3">No upcoming events</p>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($upcomingEvents as $event): ?>
                            <div class="list-group-item px-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">
                                            <?php echo htmlspecialchars($event['title']); ?>
                                            <?php if ($event['is_featured']): ?>
                                                <span class="badge bg-primary ms-1">Featured</span>
                                            <?php endif; ?>
                                        </h6>
                                        <p class="mb-1 text-muted">
                                            <?php if ($event['is_recurring']): ?>
                                                <i class="fas fa-redo me-1"></i>
                                                Recurring (<?php echo ucfirst($event['recurring_pattern']); ?>)
                                            <?php else: ?>
                                                <i class="fas fa-calendar me-1"></i>
                                                <?php echo date('M j, Y', strtotime($event['event_date'])); ?>
                                                <?php if ($event['start_time']): ?>
                                                    at <?php echo date('g:i A', strtotime($event['start_time'])); ?>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
// Menu Items Chart
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('menuChart').getContext('2d');
    
    const menuData = <?php echo json_encode($menuByCategory); ?>;
    const labels = menuData.map(item => item.name);
    const data = menuData.map(item => parseInt(item.count));
    const colors = [
        '#dc3545', '#28a745', '#17a2b8', '#ffc107', '#6f42c1', '#fd7e14'
    ];
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors.slice(0, data.length),
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
