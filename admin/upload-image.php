<?php
/**
 * Champions Sports Bar & Grill - Image Upload Handler
 */

require_once 'includes/auth.php';
require_once 'config/database.php';

// Check if user is logged in
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }

    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
        throw new Exception('Invalid CSRF token');
    }

    if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('No image uploaded or upload error occurred');
    }

    $file = $_FILES['image'];
    $itemId = isset($_POST['item_id']) ? (int)$_POST['item_id'] : 0;
    $uploadType = $_POST['upload_type'] ?? 'menu_item'; // menu_item, gallery, hero_banner

    // Validate file
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    $maxSize = 5 * 1024 * 1024; // 5MB

    if (!in_array($file['type'], $allowedTypes)) {
        throw new Exception('Invalid file type. Please upload a JPEG, PNG, GIF, or WebP image.');
    }

    if ($file['size'] > $maxSize) {
        throw new Exception('File too large. Maximum size is 5MB.');
    }

    // Validate image dimensions
    $imageInfo = getimagesize($file['tmp_name']);
    if (!$imageInfo) {
        throw new Exception('Invalid image file.');
    }

    $width = $imageInfo[0];
    $height = $imageInfo[1];

    // Set upload directory based on type
    $uploadDir = '';
    switch ($uploadType) {
        case 'menu_item':
            $uploadDir = '../assets/images/menu/';
            break;
        case 'gallery':
            $uploadDir = '../assets/images/gallery/';
            break;
        case 'hero_banner':
            $uploadDir = '../assets/images/heroes/';
            break;
        default:
            throw new Exception('Invalid upload type');
    }

    // Create directory if it doesn't exist
    if (!file_exists($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            throw new Exception('Failed to create upload directory');
        }
    }

    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . '_' . time() . '.' . $extension;
    $filepath = $uploadDir . $filename;

    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $filepath)) {
        throw new Exception('Failed to move uploaded file');
    }

    // Create thumbnail for menu items
    if ($uploadType === 'menu_item') {
        $thumbnailPath = $uploadDir . 'thumbs/';
        if (!file_exists($thumbnailPath)) {
            mkdir($thumbnailPath, 0755, true);
        }
        
        $thumbnailFile = $thumbnailPath . $filename;
        createThumbnail($filepath, $thumbnailFile, 300, 300);
    }

    // Update database based on upload type
    $db = getDB();
    $relativePath = str_replace('../', '', $filepath);

    if ($uploadType === 'menu_item' && $itemId > 0) {
        // Update menu item image
        $db->query(
            "UPDATE menu_items SET image_url = :image_url, updated_at = NOW() WHERE id = :id",
            ['image_url' => $relativePath, 'id' => $itemId]
        );
    }

    echo json_encode([
        'success' => true,
        'message' => 'Image uploaded successfully',
        'filename' => $filename,
        'path' => $relativePath,
        'url' => '/' . $relativePath
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Create thumbnail image
 */
function createThumbnail($source, $destination, $maxWidth, $maxHeight) {
    $imageInfo = getimagesize($source);
    $width = $imageInfo[0];
    $height = $imageInfo[1];
    $type = $imageInfo[2];

    // Calculate new dimensions
    $ratio = min($maxWidth / $width, $maxHeight / $height);
    $newWidth = (int)($width * $ratio);
    $newHeight = (int)($height * $ratio);

    // Create source image
    switch ($type) {
        case IMAGETYPE_JPEG:
            $sourceImage = imagecreatefromjpeg($source);
            break;
        case IMAGETYPE_PNG:
            $sourceImage = imagecreatefrompng($source);
            break;
        case IMAGETYPE_GIF:
            $sourceImage = imagecreatefromgif($source);
            break;
        case IMAGETYPE_WEBP:
            $sourceImage = imagecreatefromwebp($source);
            break;
        default:
            return false;
    }

    if (!$sourceImage) {
        return false;
    }

    // Create thumbnail
    $thumbnail = imagecreatetruecolor($newWidth, $newHeight);
    
    // Preserve transparency for PNG and GIF
    if ($type == IMAGETYPE_PNG || $type == IMAGETYPE_GIF) {
        imagealphablending($thumbnail, false);
        imagesavealpha($thumbnail, true);
        $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
        imagefilledrectangle($thumbnail, 0, 0, $newWidth, $newHeight, $transparent);
    }

    // Resize image
    imagecopyresampled($thumbnail, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);

    // Save thumbnail
    $result = false;
    switch ($type) {
        case IMAGETYPE_JPEG:
            $result = imagejpeg($thumbnail, $destination, 85);
            break;
        case IMAGETYPE_PNG:
            $result = imagepng($thumbnail, $destination, 8);
            break;
        case IMAGETYPE_GIF:
            $result = imagegif($thumbnail, $destination);
            break;
        case IMAGETYPE_WEBP:
            $result = imagewebp($thumbnail, $destination, 85);
            break;
    }

    // Clean up
    imagedestroy($sourceImage);
    imagedestroy($thumbnail);

    return $result;
}
?>
