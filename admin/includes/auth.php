<?php
/**
 * Champions Sports Bar & Grill - Authentication System
 */

require_once 'config/database.php';

/**
 * Authentication Class
 */
class Auth {
    private $db;
    
    public function __construct() {
        $this->db = getDB();
        
        // Start session if not already started
        if (session_status() === PHP_SESSION_NONE) {
            session_name(ADMIN_SESSION_NAME);
            session_start();
        }
    }
    
    /**
     * Login user
     */
    public function login($username, $password, $rememberMe = false) {
        // Check for too many failed attempts
        if ($this->isLockedOut($username)) {
            return [
                'success' => false,
                'message' => 'Account temporarily locked due to too many failed attempts. Try again in 15 minutes.'
            ];
        }
        
        // Get user from database
        $user = $this->db->fetch(
            "SELECT * FROM admin_users WHERE (username = :username OR email = :username) AND is_active = 1",
            ['username' => $username]
        );
        
        if (!$user || !password_verify($password, $user['password_hash'])) {
            $this->recordFailedAttempt($username);
            return [
                'success' => false,
                'message' => 'Invalid username or password.'
            ];
        }
        
        // Clear failed attempts
        $this->clearFailedAttempts($username);
        
        // Update last login
        $this->db->update(
            'admin_users',
            ['last_login' => date('Y-m-d H:i:s')],
            'id = :id',
            ['id' => $user['id']]
        );
        
        // Set session data
        $_SESSION['admin_user_id'] = $user['id'];
        $_SESSION['admin_username'] = $user['username'];
        $_SESSION['admin_role'] = $user['role'];
        $_SESSION['admin_name'] = $user['first_name'] . ' ' . $user['last_name'];
        $_SESSION['admin_login_time'] = time();
        
        // Set remember me cookie if requested
        if ($rememberMe) {
            $token = bin2hex(random_bytes(32));
            setcookie('admin_remember_token', $token, time() + (30 * 24 * 60 * 60), '/admin/', '', true, true);
            
            // Store token in database (you might want to create a remember_tokens table)
            $this->db->update(
                'admin_users',
                ['remember_token' => password_hash($token, PASSWORD_DEFAULT)],
                'id = :id',
                ['id' => $user['id']]
            );
        }
        
        // Log activity
        logActivity($user['id'], 'login');
        
        return [
            'success' => true,
            'message' => 'Login successful.',
            'user' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'role' => $user['role'],
                'name' => $user['first_name'] . ' ' . $user['last_name']
            ]
        ];
    }
    
    /**
     * Logout user
     */
    public function logout() {
        if ($this->isLoggedIn()) {
            logActivity($_SESSION['admin_user_id'], 'logout');
        }
        
        // Clear remember me cookie
        if (isset($_COOKIE['admin_remember_token'])) {
            setcookie('admin_remember_token', '', time() - 3600, '/admin/', '', true, true);
        }
        
        // Destroy session
        session_destroy();
        
        return true;
    }
    
    /**
     * Check if user is logged in
     */
    public function isLoggedIn() {
        if (!isset($_SESSION['admin_user_id'])) {
            return false;
        }
        
        // Check session timeout
        if (isset($_SESSION['admin_login_time']) && 
            (time() - $_SESSION['admin_login_time']) > ADMIN_SESSION_LIFETIME) {
            $this->logout();
            return false;
        }
        
        return true;
    }
    
    /**
     * Get current user
     */
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        return $this->db->fetch(
            "SELECT id, username, email, role, first_name, last_name FROM admin_users WHERE id = :id",
            ['id' => $_SESSION['admin_user_id']]
        );
    }
    
    /**
     * Check if user has permission
     */
    public function hasPermission($permission) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        $role = $_SESSION['admin_role'];
        
        // Admin has all permissions
        if ($role === 'admin') {
            return true;
        }
        
        // Define role permissions
        $permissions = [
            'manager' => [
                'view_dashboard', 'manage_content', 'manage_menu', 'manage_events',
                'manage_gallery', 'view_applications', 'manage_settings'
            ],
            'editor' => [
                'view_dashboard', 'manage_content', 'manage_menu', 'manage_events', 'manage_gallery'
            ]
        ];
        
        return isset($permissions[$role]) && in_array($permission, $permissions[$role]);
    }
    
    /**
     * Require login
     */
    public function requireLogin() {
        if (!$this->isLoggedIn()) {
            header('Location: login.php');
            exit;
        }
    }
    
    /**
     * Require permission
     */
    public function requirePermission($permission) {
        $this->requireLogin();
        
        if (!$this->hasPermission($permission)) {
            header('Location: dashboard.php?error=access_denied');
            exit;
        }
    }
    
    /**
     * Record failed login attempt
     */
    private function recordFailedAttempt($username) {
        $key = 'failed_attempts_' . md5($username . $_SERVER['REMOTE_ADDR']);
        $attempts = $_SESSION[$key] ?? 0;
        $_SESSION[$key] = $attempts + 1;
        $_SESSION[$key . '_time'] = time();
    }
    
    /**
     * Clear failed attempts
     */
    private function clearFailedAttempts($username) {
        $key = 'failed_attempts_' . md5($username . $_SERVER['REMOTE_ADDR']);
        unset($_SESSION[$key]);
        unset($_SESSION[$key . '_time']);
    }
    
    /**
     * Check if account is locked out
     */
    private function isLockedOut($username) {
        $key = 'failed_attempts_' . md5($username . $_SERVER['REMOTE_ADDR']);
        $attempts = $_SESSION[$key] ?? 0;
        $lastAttempt = $_SESSION[$key . '_time'] ?? 0;
        
        if ($attempts >= LOGIN_ATTEMPTS_LIMIT) {
            if ((time() - $lastAttempt) < LOGIN_LOCKOUT_TIME) {
                return true;
            } else {
                // Lockout period expired, clear attempts
                $this->clearFailedAttempts($username);
            }
        }
        
        return false;
    }
    
    /**
     * Change password
     */
    public function changePassword($userId, $currentPassword, $newPassword) {
        $user = $this->db->fetch(
            "SELECT password_hash FROM admin_users WHERE id = :id",
            ['id' => $userId]
        );
        
        if (!$user || !password_verify($currentPassword, $user['password_hash'])) {
            return [
                'success' => false,
                'message' => 'Current password is incorrect.'
            ];
        }
        
        // Validate new password
        if (strlen($newPassword) < 8) {
            return [
                'success' => false,
                'message' => 'New password must be at least 8 characters long.'
            ];
        }
        
        // Update password
        $this->db->update(
            'admin_users',
            ['password_hash' => password_hash($newPassword, PASSWORD_DEFAULT)],
            'id = :id',
            ['id' => $userId]
        );
        
        logActivity($userId, 'password_change');
        
        return [
            'success' => true,
            'message' => 'Password changed successfully.'
        ];
    }
    
    /**
     * Create new admin user
     */
    public function createUser($data) {
        // Validate required fields
        $required = ['username', 'email', 'password', 'first_name', 'last_name'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                return [
                    'success' => false,
                    'message' => "Field '{$field}' is required."
                ];
            }
        }
        
        // Check if username or email already exists
        $existing = $this->db->fetch(
            "SELECT id FROM admin_users WHERE username = :username OR email = :email",
            ['username' => $data['username'], 'email' => $data['email']]
        );
        
        if ($existing) {
            return [
                'success' => false,
                'message' => 'Username or email already exists.'
            ];
        }
        
        // Create user
        $userData = [
            'username' => $data['username'],
            'email' => $data['email'],
            'password_hash' => password_hash($data['password'], PASSWORD_DEFAULT),
            'role' => $data['role'] ?? 'editor',
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'is_active' => $data['is_active'] ?? true
        ];
        
        $userId = $this->db->insert('admin_users', $userData);
        
        logActivity($_SESSION['admin_user_id'] ?? null, 'user_create', 'admin_users', $userId);
        
        return [
            'success' => true,
            'message' => 'User created successfully.',
            'user_id' => $userId
        ];
    }
}

// Global auth instance
$auth = new Auth();

?>
