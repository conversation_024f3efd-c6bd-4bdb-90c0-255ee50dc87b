<?php
/**
 * Champions Sports Bar & Grill - Admin <PERSON>er
 */

// Start session and include auth
require_once 'includes/auth.php';

// Check if user is logged in (except for login page)
$currentPage = basename($_SERVER['PHP_SELF']);
if ($currentPage !== 'login.php') {
    $auth->requireLogin();
    $currentUser = $auth->getCurrentUser();
}

// Set page title if not set
if (!isset($pageTitle)) {
    $pageTitle = 'Admin Panel';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?> - Champions Sports Bar Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    
    <!-- Summernote CSS -->
    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs5.min.css" rel="stylesheet">
    
    <!-- Custom Admin CSS -->
    <link rel="stylesheet" href="assets/css/admin.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <?php if ($currentPage !== 'login.php'): ?>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="dashboard.php">
                <i class="fas fa-trophy me-2"></i>
                Champions Admin
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($currentPage === 'dashboard.php') ? 'active' : ''; ?>" 
                           href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i> Dashboard
                        </a>
                    </li>
                    
                    <?php if ($auth->hasPermission('manage_content')): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-edit me-1"></i> Content
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="content.php">
                                <i class="fas fa-home me-1"></i> Homepage Content
                            </a></li>
                            <li><a class="dropdown-item" href="hero-banners.php">
                                <i class="fas fa-image me-1"></i> Hero Banners
                            </a></li>
                        </ul>
                    </li>
                    <?php endif; ?>
                    
                    <?php if ($auth->hasPermission('manage_menu')): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-utensils me-1"></i> Menu
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="menu-categories.php">
                                <i class="fas fa-list me-1"></i> Categories
                            </a></li>
                            <li><a class="dropdown-item" href="menu-items.php">
                                <i class="fas fa-hamburger me-1"></i> Menu Items
                            </a></li>
                        </ul>
                    </li>
                    <?php endif; ?>
                    
                    <?php if ($auth->hasPermission('manage_gallery')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($currentPage === 'gallery.php') ? 'active' : ''; ?>" 
                           href="gallery.php">
                            <i class="fas fa-images me-1"></i> Gallery
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <?php if ($auth->hasPermission('manage_events')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($currentPage === 'events.php') ? 'active' : ''; ?>" 
                           href="events.php">
                            <i class="fas fa-calendar me-1"></i> Events
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <?php if ($auth->hasPermission('view_applications')): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-briefcase me-1"></i> Careers
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="job-postings.php">
                                <i class="fas fa-plus me-1"></i> Job Postings
                            </a></li>
                            <li><a class="dropdown-item" href="applications.php">
                                <i class="fas fa-file-alt me-1"></i> Applications
                                <?php
                                $newApps = getDB()->fetch("SELECT COUNT(*) as count FROM job_applications WHERE status = 'new'");
                                if ($newApps['count'] > 0):
                                ?>
                                <span class="badge bg-danger ms-1"><?php echo $newApps['count']; ?></span>
                                <?php endif; ?>
                            </a></li>
                        </ul>
                    </li>
                    <?php endif; ?>
                    
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($currentPage === 'messages.php') ? 'active' : ''; ?>" 
                           href="messages.php">
                            <i class="fas fa-envelope me-1"></i> Messages
                            <?php
                            $newMessages = getDB()->fetch("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'new'");
                            if ($newMessages['count'] > 0):
                            ?>
                            <span class="badge bg-danger ms-1"><?php echo $newMessages['count']; ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php" target="_blank">
                            <i class="fas fa-external-link-alt me-1"></i> View Site
                        </a>
                    </li>
                    
                    <?php if ($auth->hasPermission('manage_settings')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($currentPage === 'settings.php') ? 'active' : ''; ?>" 
                           href="settings.php">
                            <i class="fas fa-cog me-1"></i> Settings
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i> 
                            <?php echo htmlspecialchars($currentUser['first_name'] ?? 'User'); ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user-edit me-1"></i> Profile
                            </a></li>
                            <li><a class="dropdown-item" href="change-password.php">
                                <i class="fas fa-key me-1"></i> Change Password
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">
                                <i class="fas fa-sign-out-alt me-1"></i> Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid">
    <?php endif; ?>
    
    <!-- Alert Messages -->
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($_SESSION['success_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['success_message']); ?>
    <?php endif; ?>
    
    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['error_message']); ?>
    <?php endif; ?>
    
    <?php if (isset($_GET['error']) && $_GET['error'] === 'access_denied'): ?>
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            You don't have permission to access that page.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
