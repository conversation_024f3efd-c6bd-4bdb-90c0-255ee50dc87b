<?php
/**
 * Champions Sports Bar & Grill - Admin Settings
 */

require_once 'includes/auth.php';
require_once 'config/database.php';

// Check if user is logged in
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$db = getDB();
$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate CSRF token
        if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
            throw new Exception('Invalid CSRF token');
        }

        // Get form data
        $settings = [
            'site_name' => trim($_POST['site_name'] ?? ''),
            'site_description' => trim($_POST['site_description'] ?? ''),
            'contact_email' => trim($_POST['contact_email'] ?? ''),
            'contact_phone' => trim($_POST['contact_phone'] ?? ''),
            'address_line1' => trim($_POST['address_line1'] ?? ''),
            'address_line2' => trim($_POST['address_line2'] ?? ''),
            'city' => trim($_POST['city'] ?? ''),
            'state' => trim($_POST['state'] ?? ''),
            'zip_code' => trim($_POST['zip_code'] ?? ''),
            'facebook_url' => trim($_POST['facebook_url'] ?? ''),
            'instagram_url' => trim($_POST['instagram_url'] ?? ''),
            'twitter_url' => trim($_POST['twitter_url'] ?? ''),
            'google_analytics_id' => trim($_POST['google_analytics_id'] ?? ''),
            'google_maps_api_key' => trim($_POST['google_maps_api_key'] ?? ''),
        ];

        // Business hours
        $businessHours = [];
        $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        foreach ($days as $day) {
            $businessHours[$day] = [
                'open' => $_POST[$day . '_open'] ?? '',
                'close' => $_POST[$day . '_close'] ?? '',
                'closed' => isset($_POST[$day . '_closed'])
            ];
        }

        // Update or insert settings
        foreach ($settings as $key => $value) {
            $existing = $db->fetch("SELECT id FROM site_settings WHERE setting_key = :key", ['key' => $key]);
            
            if ($existing) {
                $db->query(
                    "UPDATE site_settings SET setting_value = :value, updated_at = NOW() WHERE setting_key = :key",
                    ['value' => $value, 'key' => $key]
                );
            } else {
                $db->query(
                    "INSERT INTO site_settings (setting_key, setting_value, created_at, updated_at) VALUES (:key, :value, NOW(), NOW())",
                    ['key' => $key, 'value' => $value]
                );
            }
        }

        // Update business hours
        $existing = $db->fetch("SELECT id FROM site_settings WHERE setting_key = 'business_hours'");
        $hoursJson = json_encode($businessHours);
        
        if ($existing) {
            $db->query(
                "UPDATE site_settings SET setting_value = :value, updated_at = NOW() WHERE setting_key = 'business_hours'",
                ['value' => $hoursJson]
            );
        } else {
            $db->query(
                "INSERT INTO site_settings (setting_key, setting_value, created_at, updated_at) VALUES ('business_hours', :value, NOW(), NOW())",
                ['value' => $hoursJson]
            );
        }

        $message = 'Settings updated successfully!';
        $messageType = 'success';

    } catch (Exception $e) {
        $message = 'Error updating settings: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Get current settings
$currentSettings = [];
$settingsData = $db->fetchAll("SELECT setting_key, setting_value FROM site_settings");
foreach ($settingsData as $setting) {
    $currentSettings[$setting['setting_key']] = $setting['setting_value'];
}

// Parse business hours
$businessHours = [];
if (isset($currentSettings['business_hours'])) {
    $businessHours = json_decode($currentSettings['business_hours'], true) ?: [];
}

// Default business hours if not set
$defaultHours = [
    'monday' => ['open' => '11:00', 'close' => '24:00', 'closed' => false],
    'tuesday' => ['open' => '11:00', 'close' => '24:00', 'closed' => false],
    'wednesday' => ['open' => '11:00', 'close' => '24:00', 'closed' => false],
    'thursday' => ['open' => '11:00', 'close' => '24:00', 'closed' => false],
    'friday' => ['open' => '11:00', 'close' => '02:00', 'closed' => false],
    'saturday' => ['open' => '11:00', 'close' => '02:00', 'closed' => false],
    'sunday' => ['open' => '11:00', 'close' => '24:00', 'closed' => false],
];

foreach ($defaultHours as $day => $hours) {
    if (!isset($businessHours[$day])) {
        $businessHours[$day] = $hours;
    }
}

// Generate CSRF token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

$pageTitle = 'Site Settings';
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Site Settings</h1>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

                <!-- Basic Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Basic Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="site_name" class="form-label">Site Name</label>
                                    <input type="text" class="form-control" id="site_name" name="site_name" 
                                           value="<?php echo htmlspecialchars($currentSettings['site_name'] ?? 'Champions Sports Bar & Grill'); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_email" class="form-label">Contact Email</label>
                                    <input type="email" class="form-control" id="contact_email" name="contact_email" 
                                           value="<?php echo htmlspecialchars($currentSettings['contact_email'] ?? '<EMAIL>'); ?>" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="site_description" class="form-label">Site Description</label>
                            <textarea class="form-control" id="site_description" name="site_description" rows="3"><?php echo htmlspecialchars($currentSettings['site_description'] ?? 'Champions Sports Bar & Grill - Your ultimate destination for great food, cold drinks, and live sports action in Brownstown, MI.'); ?></textarea>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Contact Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="contact_phone" name="contact_phone" 
                                           value="<?php echo htmlspecialchars($currentSettings['contact_phone'] ?? '(*************'); ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="address_line1" class="form-label">Address Line 1</label>
                                    <input type="text" class="form-control" id="address_line1" name="address_line1" 
                                           value="<?php echo htmlspecialchars($currentSettings['address_line1'] ?? '22112 Sibley Road'); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="address_line2" class="form-label">Address Line 2</label>
                                    <input type="text" class="form-control" id="address_line2" name="address_line2" 
                                           value="<?php echo htmlspecialchars($currentSettings['address_line2'] ?? ''); ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="city" class="form-label">City</label>
                                    <input type="text" class="form-control" id="city" name="city" 
                                           value="<?php echo htmlspecialchars($currentSettings['city'] ?? 'Brownstown Charter Township'); ?>">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="state" class="form-label">State</label>
                                    <input type="text" class="form-control" id="state" name="state" 
                                           value="<?php echo htmlspecialchars($currentSettings['state'] ?? 'MI'); ?>">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="zip_code" class="form-label">ZIP Code</label>
                                    <input type="text" class="form-control" id="zip_code" name="zip_code" 
                                           value="<?php echo htmlspecialchars($currentSettings['zip_code'] ?? '48183'); ?>">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Business Hours -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Business Hours</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        $dayNames = [
                            'monday' => 'Monday',
                            'tuesday' => 'Tuesday',
                            'wednesday' => 'Wednesday',
                            'thursday' => 'Thursday',
                            'friday' => 'Friday',
                            'saturday' => 'Saturday',
                            'sunday' => 'Sunday'
                        ];

                        foreach ($dayNames as $day => $dayName):
                            $hours = isset($businessHours[$day]) && is_array($businessHours[$day])
                                ? $businessHours[$day]
                                : ['open' => '11:00', 'close' => '24:00', 'closed' => false];
                        ?>
                        <div class="row align-items-center mb-3">
                            <div class="col-md-2">
                                <label class="form-label fw-bold"><?php echo $dayName; ?></label>
                            </div>
                            <div class="col-md-3">
                                <input type="time" class="form-control" name="<?php echo $day; ?>_open"
                                       value="<?php echo htmlspecialchars($hours['open']); ?>"
                                       <?php echo $hours['closed'] ? 'disabled' : ''; ?>>
                            </div>
                            <div class="col-md-1 text-center">
                                <span>to</span>
                            </div>
                            <div class="col-md-3">
                                <input type="time" class="form-control" name="<?php echo $day; ?>_close"
                                       value="<?php echo htmlspecialchars($hours['close']); ?>"
                                       <?php echo $hours['closed'] ? 'disabled' : ''; ?>>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="<?php echo $day; ?>_closed"
                                           id="<?php echo $day; ?>_closed" <?php echo $hours['closed'] ? 'checked' : ''; ?>
                                           onchange="toggleDayInputs('<?php echo $day; ?>')">
                                    <label class="form-check-label" for="<?php echo $day; ?>_closed">
                                        Closed
                                    </label>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Social Media -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Social Media</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="facebook_url" class="form-label">Facebook URL</label>
                                    <input type="url" class="form-control" id="facebook_url" name="facebook_url"
                                           value="<?php echo htmlspecialchars($currentSettings['facebook_url'] ?? ''); ?>"
                                           placeholder="https://facebook.com/championsportsbar">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="instagram_url" class="form-label">Instagram URL</label>
                                    <input type="url" class="form-control" id="instagram_url" name="instagram_url"
                                           value="<?php echo htmlspecialchars($currentSettings['instagram_url'] ?? ''); ?>"
                                           placeholder="https://instagram.com/championsportsbar">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="twitter_url" class="form-label">Twitter URL</label>
                                    <input type="url" class="form-control" id="twitter_url" name="twitter_url"
                                           value="<?php echo htmlspecialchars($currentSettings['twitter_url'] ?? ''); ?>"
                                           placeholder="https://twitter.com/championsportsbar">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Integration Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Integration Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="google_analytics_id" class="form-label">Google Analytics ID</label>
                                    <input type="text" class="form-control" id="google_analytics_id" name="google_analytics_id"
                                           value="<?php echo htmlspecialchars($currentSettings['google_analytics_id'] ?? ''); ?>"
                                           placeholder="G-XXXXXXXXXX">
                                    <div class="form-text">Enter your Google Analytics 4 measurement ID</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="google_maps_api_key" class="form-label">Google Maps API Key</label>
                                    <input type="text" class="form-control" id="google_maps_api_key" name="google_maps_api_key"
                                           value="<?php echo htmlspecialchars($currentSettings['google_maps_api_key'] ?? ''); ?>"
                                           placeholder="AIzaSyXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX">
                                    <div class="form-text">Required for Google Maps integration</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Save Settings
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function toggleDayInputs(day) {
    const checkbox = document.getElementById(day + '_closed');
    const openInput = document.querySelector('input[name="' + day + '_open"]');
    const closeInput = document.querySelector('input[name="' + day + '_close"]');

    if (checkbox.checked) {
        openInput.disabled = true;
        closeInput.disabled = true;
        openInput.value = '';
        closeInput.value = '';
    } else {
        openInput.disabled = false;
        closeInput.disabled = false;
        openInput.value = '11:00';
        closeInput.value = '24:00';
    }
}

// Initialize form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?php include 'includes/footer.php'; ?>
