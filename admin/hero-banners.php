<?php
/**
 * Champions Sports Bar & Grill - Hero Banners Management
 */

$pageTitle = 'Hero Banners';
require_once 'includes/header.php';

$auth->requirePermission('manage_content');

$db = getDB();
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (!verifyCSRFToken($csrfToken)) {
        $message = 'Invalid security token.';
        $messageType = 'danger';
    } else {
        switch ($action) {
            case 'add_banner':
            case 'edit_banner':
                $id = $action === 'edit_banner' ? (int)$_POST['id'] : null;
                $title = sanitize($_POST['title']);
                $subtitle = sanitize($_POST['subtitle']);
                $description = sanitize($_POST['description']);
                $imageUrl = sanitize($_POST['image_url']);
                $buttonText = sanitize($_POST['button_text']);
                $buttonUrl = sanitize($_POST['button_url']);
                $isActive = isset($_POST['is_active']) ? 1 : 0;
                $sortOrder = (int)($_POST['sort_order'] ?? 0);
                
                $data = [
                    'title' => $title,
                    'subtitle' => $subtitle,
                    'description' => $description,
                    'image_url' => $imageUrl,
                    'button_text' => $buttonText,
                    'button_url' => $buttonUrl,
                    'is_active' => $isActive,
                    'sort_order' => $sortOrder
                ];
                
                if ($action === 'add_banner') {
                    $data['created_by'] = $currentUser['id'];
                    $bannerId = $db->insert('hero_banners', $data);
                    logActivity($currentUser['id'], 'hero_banner_create', 'hero_banners', $bannerId);
                    $message = 'Hero banner added successfully!';
                } else {
                    $db->update('hero_banners', $data, 'id = :id', ['id' => $id]);
                    logActivity($currentUser['id'], 'hero_banner_update', 'hero_banners', $id);
                    $message = 'Hero banner updated successfully!';
                }
                
                $messageType = 'success';
                break;
                
            case 'delete_banner':
                $id = (int)$_POST['id'];
                $db->delete('hero_banners', 'id = :id', ['id' => $id]);
                logActivity($currentUser['id'], 'hero_banner_delete', 'hero_banners', $id);
                $message = 'Hero banner deleted successfully!';
                $messageType = 'success';
                break;
                
            case 'toggle_status':
                $id = (int)$_POST['id'];
                $banner = $db->fetch("SELECT is_active FROM hero_banners WHERE id = :id", ['id' => $id]);
                if ($banner) {
                    $newStatus = $banner['is_active'] ? 0 : 1;
                    $db->update('hero_banners', ['is_active' => $newStatus], 'id = :id', ['id' => $id]);
                    logActivity($currentUser['id'], 'hero_banner_toggle', 'hero_banners', $id);
                    $message = 'Banner status updated successfully!';
                    $messageType = 'success';
                }
                break;
        }
    }
}

// Get action from URL
$urlAction = $_GET['action'] ?? 'list';
$editId = $_GET['id'] ?? null;

// Get banner for editing
$editBanner = null;
if ($urlAction === 'edit' && $editId) {
    $editBanner = $db->fetch("SELECT * FROM hero_banners WHERE id = :id", ['id' => $editId]);
    if (!$editBanner) {
        $urlAction = 'list';
    }
}

// Get all banners
$banners = $db->fetchAll("SELECT * FROM hero_banners ORDER BY sort_order ASC, created_at DESC");
?>

<?php if ($message): ?>
    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
        <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-0">
                    <i class="fas fa-image me-2"></i>
                    Hero Banners
                </h1>
                <p class="text-muted">Manage homepage hero banners and sliders</p>
            </div>
            <?php if ($urlAction === 'list'): ?>
                <a href="?action=add" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i> Add New Banner
                </a>
            <?php else: ?>
                <a href="hero-banners.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Back to List
                </a>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php if ($urlAction === 'list'): ?>
    <!-- Banners List -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Current Hero Banners</h5>
        </div>
        <div class="card-body">
            <?php if (empty($banners)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-image fa-3x text-muted mb-3"></i>
                    <h5>No Hero Banners</h5>
                    <p class="text-muted">Create your first hero banner to get started.</p>
                    <a href="?action=add" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> Add Hero Banner
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Preview</th>
                                <th>Title</th>
                                <th>Status</th>
                                <th>Order</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody class="sortable" data-table="hero_banners">
                            <?php foreach ($banners as $banner): ?>
                                <tr data-id="<?php echo $banner['id']; ?>">
                                    <td>
                                        <img src="<?php echo htmlspecialchars($banner['image_url']); ?>" 
                                             alt="Banner preview" 
                                             class="img-thumbnail" 
                                             style="width: 80px; height: 50px; object-fit: cover;">
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($banner['title']); ?></strong>
                                        <?php if ($banner['subtitle']): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($banner['subtitle']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                            <input type="hidden" name="action" value="toggle_status">
                                            <input type="hidden" name="id" value="<?php echo $banner['id']; ?>">
                                            <button type="submit" 
                                                    class="btn btn-sm <?php echo $banner['is_active'] ? 'btn-success' : 'btn-secondary'; ?>">
                                                <i class="fas <?php echo $banner['is_active'] ? 'fa-check' : 'fa-times'; ?>"></i>
                                                <?php echo $banner['is_active'] ? 'Active' : 'Inactive'; ?>
                                            </button>
                                        </form>
                                    </td>
                                    <td>
                                        <i class="fas fa-grip-vertical sort-handle text-muted me-2"></i>
                                        <?php echo $banner['sort_order']; ?>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo date('M j, Y', strtotime($banner['created_at'])); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="?action=edit&id=<?php echo $banner['id']; ?>" 
                                               class="btn btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form method="POST" class="d-inline">
                                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                <input type="hidden" name="action" value="delete_banner">
                                                <input type="hidden" name="id" value="<?php echo $banner['id']; ?>">
                                                <button type="submit" 
                                                        class="btn btn-outline-danger btn-delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

<?php else: ?>
    <!-- Add/Edit Banner Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <?php echo $urlAction === 'add' ? 'Add New' : 'Edit'; ?> Hero Banner
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="<?php echo $urlAction === 'add' ? 'add_banner' : 'edit_banner'; ?>">
                        <?php if ($editBanner): ?>
                            <input type="hidden" name="id" value="<?php echo $editBanner['id']; ?>">
                        <?php endif; ?>
                        
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="title" class="form-label required">Title</label>
                                <input type="text" 
                                       class="form-control" 
                                       id="title" 
                                       name="title" 
                                       value="<?php echo htmlspecialchars($editBanner['title'] ?? ''); ?>" 
                                       required>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="sort_order" class="form-label">Sort Order</label>
                                <input type="number" 
                                       class="form-control" 
                                       id="sort_order" 
                                       name="sort_order" 
                                       value="<?php echo $editBanner['sort_order'] ?? 0; ?>" 
                                       min="0">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="subtitle" class="form-label">Subtitle</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="subtitle" 
                                   name="subtitle" 
                                   value="<?php echo htmlspecialchars($editBanner['subtitle'] ?? ''); ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" 
                                      id="description" 
                                      name="description" 
                                      rows="3"><?php echo htmlspecialchars($editBanner['description'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="image_url" class="form-label required">Image URL</label>
                            <input type="url" 
                                   class="form-control" 
                                   id="image_url" 
                                   name="image_url" 
                                   value="<?php echo htmlspecialchars($editBanner['image_url'] ?? ''); ?>" 
                                   required>
                            <div class="form-text">Enter the full URL to the hero banner image (recommended size: 1920x1080px)</div>
                            <div class="file-preview mt-2"></div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="button_text" class="form-label">Button Text</label>
                                <input type="text" 
                                       class="form-control" 
                                       id="button_text" 
                                       name="button_text" 
                                       value="<?php echo htmlspecialchars($editBanner['button_text'] ?? ''); ?>"
                                       placeholder="e.g., View Menu">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="button_url" class="form-label">Button URL</label>
                                <input type="url" 
                                       class="form-control" 
                                       id="button_url" 
                                       name="button_url" 
                                       value="<?php echo htmlspecialchars($editBanner['button_url'] ?? ''); ?>"
                                       placeholder="https://example.com/menu">
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" 
                                   class="form-check-input" 
                                   id="is_active" 
                                   name="is_active" 
                                   <?php echo ($editBanner['is_active'] ?? 1) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_active">
                                Active (show on website)
                            </label>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> 
                                <?php echo $urlAction === 'add' ? 'Add' : 'Update'; ?> Banner
                            </button>
                            <a href="hero-banners.php" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Banner Guidelines</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled small">
                        <li class="mb-2">
                            <i class="fas fa-image text-info me-1"></i>
                            <strong>Image Size:</strong> 1920x1080px recommended
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-file-image text-warning me-1"></i>
                            <strong>Format:</strong> JPG, PNG, or WebP
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-compress text-success me-1"></i>
                            <strong>File Size:</strong> Under 1MB for fast loading
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-eye text-primary me-1"></i>
                            <strong>Content:</strong> Ensure text is readable on image
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-mobile-alt text-secondary me-1"></i>
                            <strong>Mobile:</strong> Test how it looks on mobile devices
                        </li>
                    </ul>
                </div>
            </div>
            
            <?php if ($editBanner && $editBanner['image_url']): ?>
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">Current Image</h6>
                </div>
                <div class="card-body p-0">
                    <img src="<?php echo htmlspecialchars($editBanner['image_url']); ?>" 
                         alt="Current banner" 
                         class="img-fluid">
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>

<script>
// Image URL preview
document.getElementById('image_url').addEventListener('input', function() {
    const url = this.value;
    const preview = document.querySelector('.file-preview');
    
    if (url && (url.startsWith('http://') || url.startsWith('https://'))) {
        preview.innerHTML = '<img src="' + url + '" class="img-thumbnail" style="max-width: 300px;" onerror="this.style.display=\'none\'">';
    } else {
        preview.innerHTML = '';
    }
});

// Initialize sortable if on list page
<?php if ($urlAction === 'list' && !empty($banners)): ?>
$(document).ready(function() {
    if ($.fn.sortable) {
        $('.sortable').sortable({
            handle: '.sort-handle',
            update: function(event, ui) {
                const order = $(this).sortable('toArray', {attribute: 'data-id'});
                
                $.ajax({
                    url: 'ajax/update-order.php',
                    method: 'POST',
                    data: {
                        table: 'hero_banners',
                        order: order,
                        csrf_token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            ChampionsAdmin.showAlert('Banner order updated successfully', 'success');
                        }
                    }
                });
            }
        });
    }
});
<?php endif; ?>
</script>

<?php require_once 'includes/footer.php'; ?>
