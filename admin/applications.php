<?php
/**
 * Champions Sports Bar & Grill - Job Applications Management
 */

require_once 'includes/auth.php';
require_once 'config/database.php';

// Check if user is logged in
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$db = Database::getInstance();
$message = '';
$messageType = '';

// Handle AJAX requests
if (isset($_GET['ajax'])) {
    header('Content-Type: application/json');
    
    if ($_GET['ajax'] === 'update_status' && isset($_GET['id']) && isset($_GET['status'])) {
        try {
            $id = (int)$_GET['id'];
            $status = $_GET['status'];
            
            // Validate status
            $validStatuses = ['new', 'reviewed', 'interview', 'hired', 'rejected'];
            if (!in_array($status, $validStatuses)) {
                throw new Exception('Invalid status');
            }
            
            $db->query(
                "UPDATE job_applications SET status = :status, updated_at = NOW() WHERE id = :id",
                ['status' => $status, 'id' => $id]
            );
            
            echo json_encode(['success' => true, 'message' => 'Status updated successfully']);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
        exit;
    }
    
    if ($_GET['ajax'] === 'delete' && isset($_GET['id'])) {
        try {
            $id = (int)$_GET['id'];
            
            // Get application details for file cleanup
            $application = $db->fetch("SELECT resume_file FROM job_applications WHERE id = :id", ['id' => $id]);
            
            if ($application && !empty($application['resume_file'])) {
                $filePath = '../uploads/resumes/' . $application['resume_file'];
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }
            
            $db->query("DELETE FROM job_applications WHERE id = :id", ['id' => $id]);
            
            echo json_encode(['success' => true, 'message' => 'Application deleted successfully']);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
        exit;
    }
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // CSRF protection
        if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
            throw new Exception('Invalid CSRF token');
        }

        $action = $_POST['action'] ?? '';
        
        if ($action === 'update_notes') {
            $id = (int)$_POST['application_id'];
            $notes = trim($_POST['notes'] ?? '');
            
            $db->query(
                "UPDATE job_applications SET notes = :notes, updated_at = NOW() WHERE id = :id",
                ['notes' => $notes, 'id' => $id]
            );
            
            $message = 'Notes updated successfully!';
            $messageType = 'success';
        }
        
        if ($action === 'bulk_action') {
            $selectedIds = $_POST['selected_applications'] ?? [];
            $bulkAction = $_POST['bulk_action'] ?? '';
            
            if (empty($selectedIds)) {
                throw new Exception('No applications selected');
            }
            
            if ($bulkAction === 'delete') {
                // Get all resume files for cleanup
                $placeholders = str_repeat('?,', count($selectedIds) - 1) . '?';
                $stmt = $db->getConnection()->prepare("SELECT resume_file FROM job_applications WHERE id IN ($placeholders)");
                $stmt->execute($selectedIds);
                $applications = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                // Delete files
                foreach ($applications as $app) {
                    if (!empty($app['resume_file'])) {
                        $filePath = '../uploads/resumes/' . $app['resume_file'];
                        if (file_exists($filePath)) {
                            unlink($filePath);
                        }
                    }
                }
                
                // Delete applications
                $stmt = $db->getConnection()->prepare("DELETE FROM job_applications WHERE id IN ($placeholders)");
                $stmt->execute($selectedIds);
                
                $message = count($selectedIds) . ' applications deleted successfully!';
                $messageType = 'success';
            } elseif (in_array($bulkAction, ['new', 'reviewed', 'interview', 'hired', 'rejected'])) {
                $stmt = $db->getConnection()->prepare("UPDATE job_applications SET status = ?, updated_at = NOW() WHERE id IN ($placeholders)");
                $stmt->execute(array_merge([$bulkAction], $selectedIds));
                
                $message = count($selectedIds) . ' applications updated to ' . ucfirst($bulkAction) . ' status!';
                $messageType = 'success';
            }
        }
        
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Generate CSRF token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Get filter parameters
$statusFilter = $_GET['status'] ?? '';
$jobFilter = $_GET['job'] ?? '';
$searchQuery = $_GET['search'] ?? '';
$page = max(1, (int)($_GET['page'] ?? 1));
$perPage = 20;
$offset = ($page - 1) * $perPage;

// Build WHERE clause
$whereConditions = [];
$params = [];

if (!empty($statusFilter)) {
    $whereConditions[] = "ja.status = :status";
    $params['status'] = $statusFilter;
}

if (!empty($jobFilter)) {
    $whereConditions[] = "ja.job_posting_id = :job_id";
    $params['job_id'] = $jobFilter;
}

if (!empty($searchQuery)) {
    $whereConditions[] = "(ja.first_name LIKE :search OR ja.last_name LIKE :search OR ja.email LIKE :search OR jp.title LIKE :search)";
    $params['search'] = '%' . $searchQuery . '%';
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Get applications with job details
$sql = "
    SELECT ja.*, jp.title as job_title, jp.employment_type,
           CONCAT(ja.first_name, ' ', ja.last_name) as full_name
    FROM job_applications ja 
    LEFT JOIN job_postings jp ON ja.job_posting_id = jp.id 
    $whereClause
    ORDER BY ja.applied_at DESC 
    LIMIT :limit OFFSET :offset
";

$params['limit'] = $perPage;
$params['offset'] = $offset;

$applications = $db->fetchAll($sql, $params);

// Get total count for pagination
$countSql = "
    SELECT COUNT(*) as total 
    FROM job_applications ja 
    LEFT JOIN job_postings jp ON ja.job_posting_id = jp.id 
    $whereClause
";
$countParams = array_diff_key($params, ['limit' => '', 'offset' => '']);
$totalApplications = $db->fetch($countSql, $countParams)['total'];
$totalPages = ceil($totalApplications / $perPage);

// Get statistics
$stats = [
    'total' => $db->fetch("SELECT COUNT(*) as count FROM job_applications")['count'],
    'new' => $db->fetch("SELECT COUNT(*) as count FROM job_applications WHERE status = 'new'")['count'],
    'reviewed' => $db->fetch("SELECT COUNT(*) as count FROM job_applications WHERE status = 'reviewed'")['count'],
    'interview' => $db->fetch("SELECT COUNT(*) as count FROM job_applications WHERE status = 'interview'")['count'],
    'hired' => $db->fetch("SELECT COUNT(*) as count FROM job_applications WHERE status = 'hired'")['count'],
    'rejected' => $db->fetch("SELECT COUNT(*) as count FROM job_applications WHERE status = 'rejected'")['count']
];

// Get all job postings for filter dropdown
$jobPostings = $db->fetchAll("SELECT id, title FROM job_postings ORDER BY title");

include 'includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Job Applications</h1>
            <p class="text-muted">Manage job applications and candidate information</p>
        </div>
        <div>
            <a href="jobs.php" class="btn btn-outline-primary">
                <i class="fas fa-briefcase me-2"></i>Manage Jobs
            </a>
        </div>
    </div>

    <!-- Alert Messages -->
    <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-2 col-md-4 col-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $stats['total']; ?></h4>
                            <small>Total Applications</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $stats['new']; ?></h4>
                            <small>New</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-star fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $stats['reviewed']; ?></h4>
                            <small>Reviewed</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-eye fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-6 mb-3">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $stats['interview']; ?></h4>
                            <small>Interview</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-comments fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $stats['hired']; ?></h4>
                            <small>Hired</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-6 mb-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $stats['rejected']; ?></h4>
                            <small>Rejected</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">All Statuses</option>
                        <option value="new" <?php echo $statusFilter === 'new' ? 'selected' : ''; ?>>New</option>
                        <option value="reviewed" <?php echo $statusFilter === 'reviewed' ? 'selected' : ''; ?>>Reviewed</option>
                        <option value="interview" <?php echo $statusFilter === 'interview' ? 'selected' : ''; ?>>Interview</option>
                        <option value="hired" <?php echo $statusFilter === 'hired' ? 'selected' : ''; ?>>Hired</option>
                        <option value="rejected" <?php echo $statusFilter === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="job" class="form-label">Job Position</label>
                    <select name="job" id="job" class="form-select">
                        <option value="">All Positions</option>
                        <?php foreach ($jobPostings as $job): ?>
                            <option value="<?php echo $job['id']; ?>" <?php echo $jobFilter == $job['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($job['title']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" name="search" id="search" class="form-control"
                           placeholder="Search by name, email, or job title..."
                           value="<?php echo htmlspecialchars($searchQuery); ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Filter
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Applications Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-list me-2"></i>
                Applications (<?php echo number_format($totalApplications); ?>)
            </h5>
            <div>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="selectAll()">
                    <i class="fas fa-check-square me-1"></i>Select All
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="selectNone()">
                    <i class="fas fa-square me-1"></i>Select None
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            <?php if (empty($applications)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No applications found</h5>
                    <p class="text-muted">No job applications match your current filters.</p>
                </div>
            <?php else: ?>
                <form id="bulkActionForm" method="POST">
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                    <input type="hidden" name="action" value="bulk_action">

                    <!-- Bulk Actions -->
                    <div class="p-3 border-bottom bg-light">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <select name="bulk_action" class="form-select" style="max-width: 200px;">
                                        <option value="">Bulk Actions</option>
                                        <option value="new">Mark as New</option>
                                        <option value="reviewed">Mark as Reviewed</option>
                                        <option value="interview">Mark as Interview</option>
                                        <option value="hired">Mark as Hired</option>
                                        <option value="rejected">Mark as Rejected</option>
                                        <option value="delete">Delete</option>
                                    </select>
                                    <button type="submit" class="btn btn-outline-primary" onclick="return confirmBulkAction()">
                                        Apply
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <small class="text-muted">
                                    Showing <?php echo $offset + 1; ?>-<?php echo min($offset + $perPage, $totalApplications); ?>
                                    of <?php echo number_format($totalApplications); ?> applications
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                                    </th>
                                    <th>Applicant</th>
                                    <th>Position</th>
                                    <th>Contact</th>
                                    <th>Status</th>
                                    <th>Applied</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($applications as $application): ?>
                                    <tr>
                                        <td>
                                            <input type="checkbox" name="selected_applications[]"
                                                   value="<?php echo $application['id']; ?>"
                                                   class="application-checkbox">
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                                    <?php echo strtoupper(substr($application['first_name'], 0, 1) . substr($application['last_name'], 0, 1)); ?>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0"><?php echo htmlspecialchars($application['full_name']); ?></h6>
                                                    <?php if (!empty($application['experience'])): ?>
                                                        <small class="text-muted"><?php echo htmlspecialchars($application['experience']); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <strong><?php echo htmlspecialchars($application['job_title'] ?? 'Unknown Position'); ?></strong>
                                                <?php if (!empty($application['employment_type'])): ?>
                                                    <br><small class="text-muted"><?php echo ucfirst($application['employment_type']); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <i class="fas fa-envelope me-1"></i>
                                                <a href="mailto:<?php echo htmlspecialchars($application['email']); ?>">
                                                    <?php echo htmlspecialchars($application['email']); ?>
                                                </a>
                                            </div>
                                            <?php if (!empty($application['phone'])): ?>
                                                <div class="mt-1">
                                                    <i class="fas fa-phone me-1"></i>
                                                    <a href="tel:<?php echo htmlspecialchars($application['phone']); ?>">
                                                        <?php echo htmlspecialchars($application['phone']); ?>
                                                    </a>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <select class="form-select form-select-sm status-select"
                                                    data-id="<?php echo $application['id']; ?>"
                                                    onchange="updateStatus(this)">
                                                <option value="new" <?php echo $application['status'] === 'new' ? 'selected' : ''; ?>>New</option>
                                                <option value="reviewed" <?php echo $application['status'] === 'reviewed' ? 'selected' : ''; ?>>Reviewed</option>
                                                <option value="interview" <?php echo $application['status'] === 'interview' ? 'selected' : ''; ?>>Interview</option>
                                                <option value="hired" <?php echo $application['status'] === 'hired' ? 'selected' : ''; ?>>Hired</option>
                                                <option value="rejected" <?php echo $application['status'] === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                            </select>
                                        </td>
                                        <td>
                                            <div>
                                                <?php echo date('M j, Y', strtotime($application['applied_at'])); ?>
                                                <br><small class="text-muted"><?php echo date('g:i A', strtotime($application['applied_at'])); ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary"
                                                        onclick="viewApplication(<?php echo $application['id']; ?>)"
                                                        title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <?php if (!empty($application['resume_file'])): ?>
                                                    <a href="../uploads/resumes/<?php echo htmlspecialchars($application['resume_file']); ?>"
                                                       target="_blank" class="btn btn-sm btn-outline-success" title="View Resume">
                                                        <i class="fas fa-file-pdf"></i>
                                                    </a>
                                                <?php endif; ?>
                                                <button type="button" class="btn btn-sm btn-outline-danger"
                                                        onclick="deleteApplication(<?php echo $application['id']; ?>)"
                                                        title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </form>
            <?php endif; ?>
        </div>

        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
            <div class="card-footer">
                <nav aria-label="Applications pagination">
                    <ul class="pagination justify-content-center mb-0">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php
                        $startPage = max(1, $page - 2);
                        $endPage = min($totalPages, $page + 2);

                        for ($i = $startPage; $i <= $endPage; $i++):
                        ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Application Details Modal -->
<div class="modal fade" id="applicationModal" tabindex="-1" aria-labelledby="applicationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="applicationModalLabel">Application Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="applicationModalBody">
                <!-- Content will be loaded via AJAX -->
                <div class="text-center py-4">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
    font-size: 14px;
    font-weight: 600;
}

.status-select {
    min-width: 120px;
}

.table td {
    vertical-align: middle;
}

.btn-group .btn {
    border-radius: 0.25rem !important;
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}
</style>

<script>
// Select all functionality
function selectAll() {
    document.querySelectorAll('.application-checkbox').forEach(checkbox => {
        checkbox.checked = true;
    });
    document.getElementById('selectAllCheckbox').checked = true;
}

function selectNone() {
    document.querySelectorAll('.application-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    document.getElementById('selectAllCheckbox').checked = false;
}

function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    document.querySelectorAll('.application-checkbox').forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
}

// Update status via AJAX
function updateStatus(selectElement) {
    const applicationId = selectElement.dataset.id;
    const newStatus = selectElement.value;

    fetch(`?ajax=update_status&id=${applicationId}&status=${newStatus}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                showAlert('Status updated successfully!', 'success');

                // Update the row styling based on status
                const row = selectElement.closest('tr');
                row.className = `status-${newStatus}`;
            } else {
                showAlert('Error updating status: ' + data.message, 'danger');
                // Revert the select value
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Error updating status', 'danger');
            location.reload();
        });
}

// View application details
function viewApplication(applicationId) {
    const modal = new bootstrap.Modal(document.getElementById('applicationModal'));
    const modalBody = document.getElementById('applicationModalBody');

    // Show loading spinner
    modalBody.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    `;

    modal.show();

    // Load application details via AJAX
    fetch(`application-details.php?id=${applicationId}`)
        .then(response => response.text())
        .then(html => {
            modalBody.innerHTML = html;
        })
        .catch(error => {
            console.error('Error:', error);
            modalBody.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Error loading application details. Please try again.
                </div>
            `;
        });
}

// Delete application
function deleteApplication(applicationId) {
    if (confirm('Are you sure you want to delete this application? This action cannot be undone.')) {
        fetch(`?ajax=delete&id=${applicationId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('Application deleted successfully!', 'success');
                    // Remove the row from the table
                    const row = document.querySelector(`input[value="${applicationId}"]`).closest('tr');
                    row.remove();
                } else {
                    showAlert('Error deleting application: ' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('Error deleting application', 'danger');
            });
    }
}

// Confirm bulk action
function confirmBulkAction() {
    const selectedCheckboxes = document.querySelectorAll('.application-checkbox:checked');
    const bulkAction = document.querySelector('select[name="bulk_action"]').value;

    if (selectedCheckboxes.length === 0) {
        alert('Please select at least one application.');
        return false;
    }

    if (!bulkAction) {
        alert('Please select an action.');
        return false;
    }

    if (bulkAction === 'delete') {
        return confirm(`Are you sure you want to delete ${selectedCheckboxes.length} application(s)? This action cannot be undone.`);
    }

    return confirm(`Are you sure you want to ${bulkAction} ${selectedCheckboxes.length} application(s)?`);
}

// Show alert message
function showAlert(message, type) {
    const alertContainer = document.querySelector('.container-fluid');
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert after the page header
    const pageHeader = alertContainer.querySelector('.d-flex.justify-content-between');
    pageHeader.parentNode.insertBefore(alert, pageHeader.nextSibling);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Update checkbox state based on individual selections
    document.querySelectorAll('.application-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const allCheckboxes = document.querySelectorAll('.application-checkbox');
            const checkedCheckboxes = document.querySelectorAll('.application-checkbox:checked');
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');

            if (checkedCheckboxes.length === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
            } else if (checkedCheckboxes.length === allCheckboxes.length) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
            } else {
                selectAllCheckbox.indeterminate = true;
            }
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
