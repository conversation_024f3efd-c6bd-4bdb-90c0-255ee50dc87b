<?php
/**
 * Error Handling Test Script
 * 
 * This script tests various error handling scenarios
 */

require_once 'config.php';

$errorHandler = ErrorHandler::getInstance();

echo "<h1>Champions Sports Bar - Error Handling Test</h1>\n";
echo "<p>Testing error handling and logging system...</p>\n";

// Test 1: Log Info Message
echo "<h2>Test 1: Info Logging</h2>\n";
$errorHandler->logInfo('Error handling test started', ['test_id' => 1]);
echo "<p>✅ Info message logged successfully</p>\n";

// Test 2: Log Warning
echo "<h2>Test 2: Warning Logging</h2>\n";
$errorHandler->logWarning('This is a test warning', ['test_id' => 2]);
echo "<p>✅ Warning message logged successfully</p>\n";

// Test 3: Log Error
echo "<h2>Test 3: Error Logging</h2>\n";
$errorHandler->logError('This is a test error', ['test_id' => 3]);
echo "<p>✅ Error message logged successfully</p>\n";

// Test 4: Log Debug (only in debug mode)
echo "<h2>Test 4: Debug Logging</h2>\n";
$errorHandler->logDebug('This is a test debug message', ['test_id' => 4]);
echo "<p>✅ Debug message logged (if debug mode enabled)</p>\n";

// Test 5: Validation Error
echo "<h2>Test 5: Validation Error</h2>\n";
$errorHandler->handleValidationError('email', 'Invalid email format', 'invalid-email');
echo "<p>✅ Validation error logged successfully</p>\n";

// Test 6: File Upload Error
echo "<h2>Test 6: File Upload Error</h2>\n";
$errorHandler->handleFileUploadError(UPLOAD_ERR_INI_SIZE, 'test_file.jpg');
echo "<p>✅ File upload error logged successfully</p>\n";

// Test 7: Security Violation
echo "<h2>Test 7: Security Violation</h2>\n";
$errorHandler->handleSecurityViolation('SQL Injection Attempt', [
    'query' => "SELECT * FROM users WHERE id = '1 OR 1=1'",
    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
]);
echo "<p>✅ Security violation logged successfully</p>\n";

// Test 8: Database Error (simulated)
echo "<h2>Test 8: Database Error</h2>\n";
try {
    $errorHandler->handleDatabaseError('Table does not exist', 'SELECT * FROM non_existent_table');
} catch (Exception $e) {
    echo "<p>✅ Database error handled: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// Test 9: Recent Logs
echo "<h2>Test 9: Recent Log Entries</h2>\n";
$recentLogs = $errorHandler->getRecentLogs(10);
echo "<p>Retrieved " . count($recentLogs) . " recent log entries:</p>\n";
echo "<div style='background: #f8f9fa; padding: 1rem; border-radius: 5px; font-family: monospace; font-size: 0.9rem; max-height: 300px; overflow-y: auto;'>\n";
foreach (array_slice($recentLogs, -5) as $log) {
    echo htmlspecialchars($log) . "<br>\n";
}
echo "</div>\n";

// Test 10: Configuration Check
echo "<h2>Test 10: Configuration Check</h2>\n";
echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>\n";
echo "<tr><th>Setting</th><th>Value</th></tr>\n";
echo "<tr><td>Debug Mode</td><td>" . (DEBUG_MODE ? 'Enabled' : 'Disabled') . "</td></tr>\n";
echo "<tr><td>Environment</td><td>" . ENVIRONMENT . "</td></tr>\n";
echo "<tr><td>Error Logging</td><td>" . (LOG_ERRORS ? 'Enabled' : 'Disabled') . "</td></tr>\n";
echo "<tr><td>Log Retention</td><td>" . LOG_RETENTION_DAYS . " days</td></tr>\n";
echo "<tr><td>Session Timeout</td><td>" . SESSION_TIMEOUT . " seconds</td></tr>\n";
echo "<tr><td>Max Login Attempts</td><td>" . MAX_LOGIN_ATTEMPTS . "</td></tr>\n";
echo "<tr><td>Login Lockout Time</td><td>" . LOGIN_LOCKOUT_TIME . " seconds</td></tr>\n";
echo "</table>\n";

// Test 11: Log File Status
echo "<h2>Test 11: Log File Status</h2>\n";
$logFiles = [
    'Application Log' => __DIR__ . '/logs/application.log',
    'Error Log' => __DIR__ . '/logs/error.log'
];

echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>\n";
echo "<tr><th>Log File</th><th>Exists</th><th>Size</th><th>Last Modified</th></tr>\n";

foreach ($logFiles as $name => $file) {
    $exists = file_exists($file);
    $size = $exists ? filesize($file) : 0;
    $modified = $exists ? date('Y-m-d H:i:s', filemtime($file)) : 'N/A';
    
    echo "<tr>";
    echo "<td>$name</td>";
    echo "<td>" . ($exists ? '✅ Yes' : '❌ No') . "</td>";
    echo "<td>" . ($exists ? number_format($size) . ' bytes' : 'N/A') . "</td>";
    echo "<td>$modified</td>";
    echo "</tr>\n";
}
echo "</table>\n";

// Test 12: Error Types Test
echo "<h2>Test 12: Error Types Test</h2>\n";
echo "<p>Testing different error severity levels...</p>\n";

// Trigger a notice (if notices are enabled)
$undefinedVar = $potentiallyUndefinedVariable ?? 'default_value';
echo "<p>✅ Notice handling test completed</p>\n";

// Test warning
$result = array_merge(['test'], null);
echo "<p>✅ Warning handling test completed</p>\n";

echo "<h2>🎉 Error Handling Test Complete!</h2>\n";
echo "<p><strong>Summary:</strong></p>\n";
echo "<ul>\n";
echo "<li>✅ Error handler initialized successfully</li>\n";
echo "<li>✅ All logging functions working</li>\n";
echo "<li>✅ Configuration loaded properly</li>\n";
echo "<li>✅ Log files created and accessible</li>\n";
echo "<li>✅ Error types handled correctly</li>\n";
echo "</ul>\n";

echo "<p><em>Check the log files in the /logs directory for detailed error information.</em></p>\n";

$errorHandler->logInfo('Error handling test completed successfully');
?>
