#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create placeholder images for the Champions Sports Bar website.
This creates simple placeholder images that can be replaced with actual photos later.
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_placeholder_image(width, height, text, filename, bg_color=(45, 55, 72), text_color=(255, 255, 255)):
    """Create a placeholder image with text."""
    # Create image
    img = Image.new('RGB', (width, height), bg_color)
    draw = ImageDraw.Draw(img)
    
    # Try to use a default font, fallback to basic if not available
    try:
        font_size = min(width, height) // 15
        font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", font_size)
    except:
        try:
            font_size = min(width, height) // 15
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            font = ImageFont.load_default()
    
    # Calculate text position (center)
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (width - text_width) // 2
    y = (height - text_height) // 2
    
    # Draw text
    draw.text((x, y), text, fill=text_color, font=font)
    
    # Save image
    os.makedirs(os.path.dirname(filename), exist_ok=True)
    img.save(filename, 'JPEG' if filename.endswith('.jpg') else 'PNG')
    print(f"Created: {filename}")

def main():
    """Create all placeholder images."""
    
    # Logo
    create_placeholder_image(200, 80, "CHAMPIONS\nSPORTS BAR", "assets/images/logo.png", 
                           bg_color=(220, 38, 38), text_color=(255, 255, 255))
    
    # Hero background
    create_placeholder_image(1920, 1080, "CHAMPIONS SPORTS BAR\nWelcome to the Ultimate Sports Experience", 
                           "assets/images/hero-bg.jpg", bg_color=(30, 41, 59), text_color=(255, 255, 255))
    
    # Outdoor patio
    create_placeholder_image(800, 600, "OUTDOOR PATIO\nRelax in our beautiful outdoor space", 
                           "assets/images/outdoor-patio.jpg", bg_color=(34, 197, 94), text_color=(255, 255, 255))
    
    # Gallery images
    gallery_images = [
        ("food-1.jpg", "DELICIOUS FOOD\nTasty appetizers"),
        ("food-2.jpg", "MAIN COURSES\nHearty entrees"),
        ("bar-1.jpg", "FULL BAR\nCraft cocktails"),
        ("drinks-1.jpg", "BEER SELECTION\nCold draft beer"),
        ("drinks-2.jpg", "WINE & SPIRITS\nPremium selection"),
        ("patio-1.jpg", "OUTDOOR SEATING\nPatio dining"),
        ("patio-2.jpg", "PATIO BAR\nOutdoor bar area"),
        ("patio-3.jpg", "GARDEN VIEW\nScenic outdoor space"),
        ("patio-4.jpg", "FIRE PIT\nCozy evening atmosphere"),
        ("interior-1.jpg", "MAIN DINING\nComfortable seating"),
        ("interior-2.jpg", "BAR AREA\nSports viewing"),
        ("interior-3.jpg", "PRIVATE DINING\nGroup events"),
        ("sports-1.jpg", "BIG SCREENS\nWatch the game"),
        ("sports-2.jpg", "SPORTS MEMORABILIA\nAuthentic atmosphere"),
        ("events-1.jpg", "LIVE EVENTS\nSpecial occasions"),
        ("events-2.jpg", "PRIVATE PARTIES\nCelebrations"),
        ("events-3.jpg", "GAME NIGHTS\nFun activities")
    ]
    
    for filename, text in gallery_images:
        create_placeholder_image(800, 600, text, f"assets/images/gallery/{filename}", 
                               bg_color=(55, 65, 81), text_color=(255, 255, 255))
    
    # Event images
    event_images = [
        ("super-bowl.jpg", "SUPER BOWL\nBig Game Party"),
        ("live-music.jpg", "LIVE MUSIC\nEvery Friday Night"),
        ("trivia-night.jpg", "TRIVIA NIGHT\nWednesday Fun"),
        ("march-madness.jpg", "MARCH MADNESS\nBasketball Tournament"),
        ("private-event.jpg", "PRIVATE EVENTS\nBook Your Party")
    ]
    
    for filename, text in event_images:
        # Create in both locations
        create_placeholder_image(800, 600, text, f"assets/images/events/{filename}", 
                               bg_color=(147, 51, 234), text_color=(255, 255, 255))
        create_placeholder_image(800, 600, text, f"events/assets/images/events/{filename}", 
                               bg_color=(147, 51, 234), text_color=(255, 255, 255))
    
    # Contact map marker
    create_placeholder_image(32, 32, "📍", "contact/assets/images/map-marker.png", 
                           bg_color=(220, 38, 38), text_color=(255, 255, 255))
    
    # Favicon
    create_placeholder_image(32, 32, "C", "assets/images/favicon.ico", 
                           bg_color=(220, 38, 38), text_color=(255, 255, 255))
    
    print("\nAll placeholder images created successfully!")
    print("You can now replace these with actual photos of your restaurant.")

if __name__ == "__main__":
    main()
