<?php
/**
 * Champions Sports Bar Website - Security Testing Script
 * 
 * This script tests for common security vulnerabilities including:
 * - SQL Injection
 * - XSS (Cross-Site Scripting)
 * - CSRF (Cross-Site Request Forgery)
 * - Authentication bypass
 * - File upload security
 * - Session security
 */

// Include configuration
require_once 'config.php';

// Test results storage
$testResults = [];
$totalTests = 0;
$passedTests = 0;

/**
 * Add test result
 */
function addTestResult($testName, $passed, $message = '') {
    global $testResults, $totalTests, $passedTests;
    
    $totalTests++;
    if ($passed) {
        $passedTests++;
    }
    
    $testResults[] = [
        'name' => $testName,
        'passed' => $passed,
        'message' => $message,
        'status' => $passed ? 'PASS' : 'FAIL'
    ];
}

/**
 * Test SQL Injection protection
 */
function testSQLInjection() {
    try {
        $db = SiteDatabase::getInstance();
        $connection = $db->getConnection();
        
        // Test common SQL injection payloads
        $injectionPayloads = [
            "' OR '1'='1",
            "'; DROP TABLE users; --",
            "' UNION SELECT * FROM admin_users --",
            "1' OR 1=1 --",
            "admin'--",
            "' OR 1=1#"
        ];
        
        $vulnerabilityFound = false;
        $vulnerableQueries = [];
        
        foreach ($injectionPayloads as $payload) {
            try {
                // Test contact form (should be protected by prepared statements)
                $stmt = $connection->prepare("SELECT * FROM contact_messages WHERE email = ?");
                $stmt->execute([$payload]);
                $result = $stmt->fetchAll();
                
                // If we get results with injection payload, it might be vulnerable
                // But since we're using prepared statements, this should be safe
                
                // Test a potentially vulnerable query (this should NOT exist in production)
                // We'll simulate what would happen with string concatenation
                $testQuery = "SELECT * FROM site_settings WHERE setting_key = '" . $payload . "'";
                
                // Don't actually execute this - just check if our code uses prepared statements
                if (strpos($testQuery, $payload) !== false) {
                    // This would be vulnerable if executed directly
                    // But we're testing that our code uses prepared statements instead
                }
                
            } catch (PDOException $e) {
                // Exceptions are expected with malicious payloads
                // This is actually good - it means the database is rejecting bad input
            }
        }
        
        // Check if prepared statements are used in our codebase
        $codeFiles = [
            'config.php',
            'process-contact.php',
            'admin/login.php'
        ];
        
        $usesPrepairedStatements = true;
        $unsafeQueries = [];
        
        foreach ($codeFiles as $file) {
            if (file_exists($file)) {
                $content = file_get_contents($file);
                
                // Look for unsafe query patterns
                if (preg_match('/\$.*query.*=.*["\'].*SELECT.*\$/', $content) ||
                    preg_match('/\$.*query.*=.*["\'].*INSERT.*\$/', $content) ||
                    preg_match('/\$.*query.*=.*["\'].*UPDATE.*\$/', $content)) {
                    $usesPrepairedStatements = false;
                    $unsafeQueries[] = $file;
                }
            }
        }
        
        if ($usesPrepairedStatements && empty($unsafeQueries)) {
            addTestResult('SQL Injection Protection', true, 'Prepared statements used, no unsafe queries found');
        } else {
            addTestResult('SQL Injection Protection', false, 'Potentially unsafe queries found in: ' . implode(', ', $unsafeQueries));
        }
        
    } catch (Exception $e) {
        addTestResult('SQL Injection Protection', false, 'Exception during testing: ' . $e->getMessage());
    }
}

/**
 * Test XSS (Cross-Site Scripting) protection
 */
function testXSSProtection() {
    try {
        // Test XSS payloads
        $xssPayloads = [
            '<script>alert("XSS")</script>',
            '<img src="x" onerror="alert(\'XSS\')">',
            'javascript:alert("XSS")',
            '<svg onload="alert(\'XSS\')">',
            '"><script>alert("XSS")</script>',
            '\';alert(String.fromCharCode(88,83,83))//\';alert(String.fromCharCode(88,83,83))//";alert(String.fromCharCode(88,83,83))//";alert(String.fromCharCode(88,83,83))//--></SCRIPT>">\'><SCRIPT>alert(String.fromCharCode(88,83,83))</SCRIPT>'
        ];
        
        $vulnerabilityFound = false;
        $vulnerableFields = [];
        
        // Test output escaping in our templates
        $templateFiles = [
            'index.php',
            'contact/index.php',
            'admin/dashboard.php'
        ];
        
        $usesEscaping = true;
        $unescapedOutput = [];
        
        foreach ($templateFiles as $file) {
            if (file_exists($file)) {
                $content = file_get_contents($file);
                
                // Look for potentially unescaped output
                if (preg_match('/echo\s+\$[^;]*;/', $content) ||
                    preg_match('/print\s+\$[^;]*;/', $content)) {
                    
                    // Check if htmlspecialchars or similar escaping is used
                    if (!preg_match('/htmlspecialchars|htmlentities|filter_var/', $content)) {
                        $usesEscaping = false;
                        $unescapedOutput[] = $file;
                    }
                }
            }
        }
        
        // Test form input sanitization
        $testInput = '<script>alert("test")</script>';
        $sanitized = htmlspecialchars($testInput, ENT_QUOTES, 'UTF-8');
        
        if ($sanitized !== $testInput && strpos($sanitized, '<script>') === false) {
            $sanitizationWorks = true;
        } else {
            $sanitizationWorks = false;
        }
        
        if ($usesEscaping && $sanitizationWorks) {
            addTestResult('XSS Protection', true, 'Output escaping implemented, input sanitization working');
        } else {
            $issues = [];
            if (!$usesEscaping) $issues[] = 'Unescaped output in: ' . implode(', ', $unescapedOutput);
            if (!$sanitizationWorks) $issues[] = 'Input sanitization not working';
            addTestResult('XSS Protection', false, implode('; ', $issues));
        }
        
    } catch (Exception $e) {
        addTestResult('XSS Protection', false, 'Exception during testing: ' . $e->getMessage());
    }
}

/**
 * Test authentication security
 */
function testAuthenticationSecurity() {
    try {
        // Test password hashing
        $testPassword = 'testpassword123';
        $hashedPassword = password_hash($testPassword, PASSWORD_DEFAULT);
        
        if (password_verify($testPassword, $hashedPassword)) {
            $passwordHashingWorks = true;
        } else {
            $passwordHashingWorks = false;
        }
        
        // Test session security
        $sessionSecure = true;
        $sessionIssues = [];
        
        // Check if session settings are secure
        if (ini_get('session.cookie_httponly') != 1) {
            $sessionSecure = false;
            $sessionIssues[] = 'HttpOnly not set';
        }
        
        if (ini_get('session.cookie_secure') != 1 && isset($_SERVER['HTTPS'])) {
            $sessionSecure = false;
            $sessionIssues[] = 'Secure flag not set for HTTPS';
        }
        
        // Test admin authentication
        $db = SiteDatabase::getInstance();
        $connection = $db->getConnection();
        
        // Check if admin passwords are hashed
        $stmt = $connection->prepare("SELECT password FROM admin_users LIMIT 1");
        $stmt->execute();
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $adminPasswordHashed = false;
        if ($admin && strlen($admin['password']) >= 60) {
            // Likely a bcrypt hash (60+ characters)
            $adminPasswordHashed = true;
        }
        
        if ($passwordHashingWorks && $adminPasswordHashed) {
            addTestResult('Authentication Security', true, 'Password hashing working, admin passwords hashed');
        } else {
            $issues = [];
            if (!$passwordHashingWorks) $issues[] = 'Password hashing not working';
            if (!$adminPasswordHashed) $issues[] = 'Admin passwords not properly hashed';
            if (!$sessionSecure) $issues[] = 'Session security issues: ' . implode(', ', $sessionIssues);
            addTestResult('Authentication Security', false, implode('; ', $issues));
        }
        
    } catch (Exception $e) {
        addTestResult('Authentication Security', false, 'Exception during testing: ' . $e->getMessage());
    }
}

/**
 * Test file upload security
 */
function testFileUploadSecurity() {
    try {
        // Check if file upload restrictions are in place
        $uploadSecure = true;
        $uploadIssues = [];
        
        // Test file type validation
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif'];
        $dangerousExtensions = ['php', 'exe', 'bat', 'sh', 'js'];
        
        // Simulate file upload validation
        foreach ($dangerousExtensions as $ext) {
            $testFilename = 'malicious.' . $ext;
            $fileExtension = strtolower(pathinfo($testFilename, PATHINFO_EXTENSION));
            
            if (in_array($fileExtension, $allowedExtensions)) {
                $uploadSecure = false;
                $uploadIssues[] = "Dangerous extension $ext allowed";
            }
        }
        
        // Check upload directory permissions
        $uploadDirs = [
            'assets/images/gallery/',
            'assets/images/events/',
            'uploads/'
        ];
        
        foreach ($uploadDirs as $dir) {
            if (is_dir($dir)) {
                $perms = fileperms($dir);
                // Check if directory is writable but not executable for others
                if (($perms & 0002) && ($perms & 0001)) {
                    $uploadSecure = false;
                    $uploadIssues[] = "Directory $dir has insecure permissions";
                }
            }
        }
        
        // Check for .htaccess protection in upload directories
        $htaccessProtected = true;
        foreach ($uploadDirs as $dir) {
            if (is_dir($dir) && !file_exists($dir . '.htaccess')) {
                $htaccessProtected = false;
                $uploadIssues[] = "No .htaccess protection in $dir";
            }
        }
        
        if ($uploadSecure && $htaccessProtected) {
            addTestResult('File Upload Security', true, 'File type validation working, directories protected');
        } else {
            addTestResult('File Upload Security', false, implode('; ', $uploadIssues));
        }
        
    } catch (Exception $e) {
        addTestResult('File Upload Security', false, 'Exception during testing: ' . $e->getMessage());
    }
}

/**
 * Test CSRF protection
 */
function testCSRFProtection() {
    try {
        // Check if CSRF tokens are implemented
        $csrfImplemented = false;
        $csrfIssues = [];
        
        // Check admin forms for CSRF protection
        $adminFiles = [
            'admin/content.php',
            'admin/settings.php',
            'admin/hero-banners.php'
        ];
        
        foreach ($adminFiles as $file) {
            if (file_exists($file)) {
                $content = file_get_contents($file);
                
                // Look for CSRF token generation/validation
                if (preg_match('/csrf_token|_token|nonce/', $content)) {
                    $csrfImplemented = true;
                    break;
                }
            }
        }
        
        // Check if forms use POST for sensitive operations
        $usesPostForSensitive = true;
        foreach ($adminFiles as $file) {
            if (file_exists($file)) {
                $content = file_get_contents($file);
                
                // Look for GET requests for sensitive operations
                if (preg_match('/\$_GET.*delete|action.*=.*delete.*GET/', $content)) {
                    $usesPostForSensitive = false;
                    $csrfIssues[] = "GET used for sensitive operations in $file";
                }
            }
        }
        
        if ($usesPostForSensitive) {
            addTestResult('CSRF Protection', true, 'POST used for sensitive operations, forms properly protected');
        } else {
            addTestResult('CSRF Protection', false, implode('; ', $csrfIssues));
        }
        
    } catch (Exception $e) {
        addTestResult('CSRF Protection', false, 'Exception during testing: ' . $e->getMessage());
    }
}

/**
 * Test input validation
 */
function testInputValidation() {
    try {
        // Test email validation
        $validEmails = ['<EMAIL>', '<EMAIL>'];
        $invalidEmails = ['invalid-email', 'test@', '@domain.com', '<EMAIL>'];
        
        $emailValidationWorks = true;
        foreach ($validEmails as $email) {
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $emailValidationWorks = false;
                break;
            }
        }
        
        foreach ($invalidEmails as $email) {
            if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $emailValidationWorks = false;
                break;
            }
        }
        
        // Test phone number validation
        $validPhones = ['************', '(*************', '5551234567'];
        $invalidPhones = ['abc-def-ghij', '123', '************89'];
        
        $phoneValidationWorks = true;
        foreach ($invalidPhones as $phone) {
            // Simple phone validation pattern
            if (preg_match('/^[\d\s\-\(\)]+$/', $phone) && strlen(preg_replace('/[^\d]/', '', $phone)) == 10) {
                // This should fail for invalid phones
                if (strlen(preg_replace('/[^\d]/', '', $phone)) != 10) {
                    $phoneValidationWorks = false;
                    break;
                }
            }
        }
        
        if ($emailValidationWorks && $phoneValidationWorks) {
            addTestResult('Input Validation', true, 'Email and phone validation working correctly');
        } else {
            $issues = [];
            if (!$emailValidationWorks) $issues[] = 'Email validation issues';
            if (!$phoneValidationWorks) $issues[] = 'Phone validation issues';
            addTestResult('Input Validation', false, implode('; ', $issues));
        }
        
    } catch (Exception $e) {
        addTestResult('Input Validation', false, 'Exception during testing: ' . $e->getMessage());
    }
}

// Run all security tests
echo "<h1>Champions Sports Bar - Security Test Results</h1>\n";
echo "<p>Testing for common security vulnerabilities...</p>\n";

testSQLInjection();
testXSSProtection();
testAuthenticationSecurity();
testFileUploadSecurity();
testCSRFProtection();
testInputValidation();

// Display results
echo "<h2>Security Test Results Summary</h2>\n";
echo "<p><strong>Total Tests:</strong> {$totalTests}</p>\n";
echo "<p><strong>Passed:</strong> {$passedTests}</p>\n";
echo "<p><strong>Failed:</strong> " . ($totalTests - $passedTests) . "</p>\n";
echo "<p><strong>Success Rate:</strong> " . round(($passedTests / $totalTests) * 100, 1) . "%</p>\n";

echo "<h2>Detailed Security Test Results</h2>\n";
echo "<table border='1' cellpadding='5' cellspacing='0' style='width: 100%; border-collapse: collapse;'>\n";
echo "<tr style='background: #f5f5f5;'><th>Security Test</th><th>Status</th><th>Details</th></tr>\n";

foreach ($testResults as $result) {
    $statusColor = $result['passed'] ? 'green' : 'red';
    echo "<tr>";
    echo "<td style='padding: 8px; font-weight: bold;'>{$result['name']}</td>";
    echo "<td style='color: {$statusColor}; font-weight: bold; padding: 8px;'>{$result['status']}</td>";
    echo "<td style='padding: 8px;'>{$result['message']}</td>";
    echo "</tr>\n";
}

echo "</table>\n";

if ($passedTests === $totalTests) {
    echo "<h2 style='color: green;'>🔒 All Security Tests Passed!</h2>\n";
    echo "<p>The website appears to be secure against common vulnerabilities.</p>\n";
} else {
    echo "<h2 style='color: orange;'>⚠️ Some Security Issues Found</h2>\n";
    echo "<p>Please review the failed tests and address any security vulnerabilities before production deployment.</p>\n";
}

echo "<h3>Security Areas Tested:</h3>\n";
echo "<ul>\n";
echo "<li>🛡️ SQL Injection Protection - Prepared statements and query safety</li>\n";
echo "<li>🔒 XSS Protection - Output escaping and input sanitization</li>\n";
echo "<li>🔐 Authentication Security - Password hashing and session security</li>\n";
echo "<li>📁 File Upload Security - File type validation and directory protection</li>\n";
echo "<li>🛡️ CSRF Protection - Cross-site request forgery prevention</li>\n";
echo "<li>✅ Input Validation - Email, phone, and data validation</li>\n";
echo "</ul>\n";

echo "<h3>Security Recommendations:</h3>\n";
echo "<ul>\n";
echo "<li>✅ Use HTTPS in production</li>\n";
echo "<li>✅ Implement Content Security Policy (CSP) headers</li>\n";
echo "<li>✅ Regular security updates and patches</li>\n";
echo "<li>✅ Implement rate limiting for forms</li>\n";
echo "<li>✅ Regular security audits and penetration testing</li>\n";
echo "<li>✅ Backup and disaster recovery procedures</li>\n";
echo "</ul>\n";

echo "<p><em>Security test completed at: " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
