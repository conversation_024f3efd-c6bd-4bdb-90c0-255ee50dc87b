<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Champions Sports Bar - Responsive Design Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #dc2626;
            text-align: center;
            margin-bottom: 30px;
        }
        .device-test {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .device-header {
            background: #374151;
            color: white;
            padding: 15px;
            font-weight: bold;
        }
        .device-frame {
            border: 3px solid #6b7280;
            margin: 20px;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
        }
        .device-screen {
            width: 100%;
            height: 600px;
            border: none;
            display: block;
        }
        .mobile-frame {
            max-width: 375px;
            margin: 20px auto;
        }
        .tablet-frame {
            max-width: 768px;
            margin: 20px auto;
        }
        .desktop-frame {
            max-width: 1200px;
            margin: 20px auto;
        }
        .device-info {
            background: #f9fafb;
            padding: 15px;
            border-top: 1px solid #e5e7eb;
        }
        .test-controls {
            background: #eff6ff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .test-controls h2 {
            margin-top: 0;
            color: #1f2937;
        }
        .control-group {
            margin-bottom: 15px;
        }
        .control-group label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        .control-group select, .control-group input {
            padding: 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            margin-right: 10px;
        }
        .test-button {
            background: #dc2626;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        .test-button:hover {
            background: #b91c1c;
        }
        .page-links {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        .page-link {
            background: #3b82f6;
            color: white;
            padding: 8px 15px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
        }
        .page-link:hover {
            background: #2563eb;
        }
        .responsive-info {
            background: #fef3c7;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #f59e0b;
        }
        .responsive-info h3 {
            margin-top: 0;
            color: #92400e;
        }
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            .device-frame {
                margin: 10px;
            }
            .page-links {
                flex-direction: column;
            }
            .control-group label {
                display: block;
                width: auto;
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏆 Champions Sports Bar - Responsive Design Test</h1>
        
        <div class="responsive-info">
            <h3>📱 Current Device Information</h3>
            <p><strong>Screen Resolution:</strong> <span id="screenRes"></span></p>
            <p><strong>Viewport Size:</strong> <span id="viewportSize"></span></p>
            <p><strong>Device Type:</strong> <span id="deviceType"></span></p>
            <p><strong>Orientation:</strong> <span id="orientation"></span></p>
        </div>

        <div class="test-controls">
            <h2>🔧 Test Controls</h2>
            <div class="control-group">
                <label>Test Page:</label>
                <select id="pageSelect">
                    <option value="/">Homepage</option>
                    <option value="/menu/">Menu</option>
                    <option value="/gallery/">Gallery</option>
                    <option value="/events/">Events</option>
                    <option value="/careers/">Careers</option>
                    <option value="/contact/">Contact</option>
                    <option value="/admin/login.php">Admin Login</option>
                </select>
                <button class="test-button" onclick="loadSelectedPage()">Load Page</button>
            </div>
            
            <div class="control-group">
                <label>Device Type:</label>
                <select id="deviceSelect" onchange="changeDevice()">
                    <option value="mobile">Mobile (375px)</option>
                    <option value="tablet">Tablet (768px)</option>
                    <option value="desktop">Desktop (1200px)</option>
                    <option value="custom">Custom Size</option>
                </select>
            </div>
            
            <div class="control-group" id="customSizeGroup" style="display: none;">
                <label>Custom Width:</label>
                <input type="number" id="customWidth" value="375" min="320" max="1920">
                <label>Height:</label>
                <input type="number" id="customHeight" value="600" min="400" max="1080">
                <button class="test-button" onclick="applyCustomSize()">Apply</button>
            </div>

            <div class="page-links">
                <a href="#" class="page-link" onclick="testPage('/')">Test Homepage</a>
                <a href="#" class="page-link" onclick="testPage('/menu/')">Test Menu</a>
                <a href="#" class="page-link" onclick="testPage('/gallery/')">Test Gallery</a>
                <a href="#" class="page-link" onclick="testPage('/events/')">Test Events</a>
                <a href="#" class="page-link" onclick="testPage('/careers/')">Test Careers</a>
                <a href="#" class="page-link" onclick="testPage('/contact/')">Test Contact</a>
            </div>
        </div>

        <!-- Mobile Device Test -->
        <div class="device-test">
            <div class="device-header">📱 Mobile Device Test (375px × 600px)</div>
            <div class="device-frame mobile-frame">
                <iframe id="mobileFrame" class="device-screen" src="/"></iframe>
            </div>
            <div class="device-info">
                <strong>Target:</strong> iPhone 12/13, Samsung Galaxy S21<br>
                <strong>Breakpoint:</strong> max-width: 576px<br>
                <strong>Key Tests:</strong> Navigation menu, touch targets, form inputs, image scaling
            </div>
        </div>

        <!-- Tablet Device Test -->
        <div class="device-test">
            <div class="device-header">📱 Tablet Device Test (768px × 600px)</div>
            <div class="device-frame tablet-frame">
                <iframe id="tabletFrame" class="device-screen" src="/"></iframe>
            </div>
            <div class="device-info">
                <strong>Target:</strong> iPad, Android tablets<br>
                <strong>Breakpoint:</strong> 768px - 991px<br>
                <strong>Key Tests:</strong> Layout adaptation, grid systems, navigation behavior
            </div>
        </div>

        <!-- Desktop Device Test -->
        <div class="device-test">
            <div class="device-header">🖥️ Desktop Device Test (1200px × 600px)</div>
            <div class="device-frame desktop-frame">
                <iframe id="desktopFrame" class="device-screen" src="/"></iframe>
            </div>
            <div class="device-info">
                <strong>Target:</strong> Desktop computers, laptops<br>
                <strong>Breakpoint:</strong> ≥ 992px<br>
                <strong>Key Tests:</strong> Full layout, hover effects, multi-column layouts
            </div>
        </div>

        <div class="responsive-info">
            <h3>✅ Responsive Design Checklist</h3>
            <ul>
                <li>✓ Navigation menu collapses on mobile</li>
                <li>✓ Images scale properly on all devices</li>
                <li>✓ Text remains readable at all sizes</li>
                <li>✓ Forms are usable on touch devices</li>
                <li>✓ Gallery grid adapts to screen size</li>
                <li>✓ Contact information displays properly</li>
                <li>✓ Admin panel works on tablets</li>
                <li>✓ Loading times are acceptable on mobile</li>
            </ul>
        </div>
    </div>

    <script>
        // Update device information
        function updateDeviceInfo() {
            document.getElementById('screenRes').textContent = screen.width + 'x' + screen.height;
            document.getElementById('viewportSize').textContent = window.innerWidth + 'x' + window.innerHeight;
            
            const width = window.innerWidth;
            let deviceType = '';
            
            if (width < 576) {
                deviceType = 'Mobile (< 576px)';
            } else if (width < 768) {
                deviceType = 'Mobile Large (576px - 767px)';
            } else if (width < 992) {
                deviceType = 'Tablet (768px - 991px)';
            } else if (width < 1200) {
                deviceType = 'Desktop (992px - 1199px)';
            } else {
                deviceType = 'Large Desktop (≥ 1200px)';
            }
            
            document.getElementById('deviceType').textContent = deviceType;
            
            const orientation = window.innerHeight > window.innerWidth ? 'Portrait' : 'Landscape';
            document.getElementById('orientation').textContent = orientation;
        }

        // Load selected page in all frames
        function loadSelectedPage() {
            const page = document.getElementById('pageSelect').value;
            testPage(page);
        }

        // Test specific page
        function testPage(url) {
            document.getElementById('mobileFrame').src = url;
            document.getElementById('tabletFrame').src = url;
            document.getElementById('desktopFrame').src = url;
        }

        // Change device simulation
        function changeDevice() {
            const device = document.getElementById('deviceSelect').value;
            const customGroup = document.getElementById('customSizeGroup');
            
            if (device === 'custom') {
                customGroup.style.display = 'block';
            } else {
                customGroup.style.display = 'none';
                
                let width, height;
                switch (device) {
                    case 'mobile':
                        width = 375;
                        height = 600;
                        break;
                    case 'tablet':
                        width = 768;
                        height = 600;
                        break;
                    case 'desktop':
                        width = 1200;
                        height = 600;
                        break;
                }
                
                // Update frame sizes
                updateFrameSize('mobileFrame', device === 'mobile' ? width : 375, height);
                updateFrameSize('tabletFrame', device === 'tablet' ? width : 768, height);
                updateFrameSize('desktopFrame', device === 'desktop' ? width : 1200, height);
            }
        }

        // Apply custom size
        function applyCustomSize() {
            const width = parseInt(document.getElementById('customWidth').value);
            const height = parseInt(document.getElementById('customHeight').value);
            
            updateFrameSize('mobileFrame', width, height);
            updateFrameSize('tabletFrame', width, height);
            updateFrameSize('desktopFrame', width, height);
        }

        // Update frame size
        function updateFrameSize(frameId, width, height) {
            const frame = document.getElementById(frameId);
            frame.style.width = width + 'px';
            frame.style.height = height + 'px';
        }

        // Initialize
        window.addEventListener('load', function() {
            updateDeviceInfo();
            
            // Update device info on resize
            window.addEventListener('resize', updateDeviceInfo);
            
            // Load homepage in all frames initially
            testPage('/');
        });

        // Refresh frames every 30 seconds to catch any updates
        setInterval(function() {
            const currentPage = document.getElementById('pageSelect').value;
            // Only refresh if we're still on the same page
            if (document.getElementById('mobileFrame').src.includes(currentPage)) {
                testPage(currentPage);
            }
        }, 30000);
    </script>
</body>
</html>
