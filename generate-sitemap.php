<?php
/**
 * Champions Sports Bar & Grill - Sitemap Generator
 * 
 * This script generates an XML sitemap for the website.
 * Run this script whenever you add new pages or update content.
 */

// Configuration
$domain = 'https://champions-sportsgrill.com'; // Update with your actual domain
$sitemap_file = 'sitemap.xml';

// Define all pages with their properties
$pages = [
    [
        'url' => '/',
        'lastmod' => date('Y-m-d'),
        'changefreq' => 'weekly',
        'priority' => '1.0'
    ],
    [
        'url' => '/menu',
        'lastmod' => date('Y-m-d'),
        'changefreq' => 'monthly',
        'priority' => '0.9'
    ],
    [
        'url' => '/gallery',
        'lastmod' => date('Y-m-d'),
        'changefreq' => 'monthly',
        'priority' => '0.8'
    ],
    [
        'url' => '/events',
        'lastmod' => date('Y-m-d'),
        'changefreq' => 'weekly',
        'priority' => '0.8'
    ],
    [
        'url' => '/careers',
        'lastmod' => date('Y-m-d'),
        'changefreq' => 'monthly',
        'priority' => '0.7'
    ],
    [
        'url' => '/contact',
        'lastmod' => date('Y-m-d'),
        'changefreq' => 'monthly',
        'priority' => '0.8'
    ]
];

// Start XML output
$xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
$xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"' . "\n";
$xml .= '        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"' . "\n";
$xml .= '        xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9' . "\n";
$xml .= '        http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">' . "\n\n";

// Add each page to the sitemap
foreach ($pages as $page) {
    $xml .= "    <url>\n";
    $xml .= "        <loc>" . $domain . $page['url'] . "</loc>\n";
    $xml .= "        <lastmod>" . $page['lastmod'] . "</lastmod>\n";
    $xml .= "        <changefreq>" . $page['changefreq'] . "</changefreq>\n";
    $xml .= "        <priority>" . $page['priority'] . "</priority>\n";
    $xml .= "    </url>\n\n";
}

$xml .= "</urlset>";

// Write sitemap to file
if (file_put_contents($sitemap_file, $xml)) {
    echo "Sitemap generated successfully: " . $sitemap_file . "\n";
    echo "Total pages: " . count($pages) . "\n";
    echo "Generated on: " . date('Y-m-d H:i:s') . "\n";
} else {
    echo "Error: Could not write sitemap file.\n";
}

// Also generate a simple HTML sitemap for users
$html_sitemap = generateHtmlSitemap($pages, $domain);
if (file_put_contents('sitemap.html', $html_sitemap)) {
    echo "HTML sitemap generated: sitemap.html\n";
}

/**
 * Generate HTML sitemap for users
 */
function generateHtmlSitemap($pages, $domain) {
    $html = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sitemap - Champions Sports Bar & Grill</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="container py-5">
        <div class="row">
            <div class="col-12">
                <h1 class="display-4 fw-bold mb-4">Site Map</h1>
                <p class="lead mb-4">Find all pages on the Champions Sports Bar & Grill website</p>
                
                <div class="row g-4">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title mb-0">Website Pages</h3>
                            </div>
                            <div class="card-body">
                                <ul class="list-group list-group-flush">';
    
    $page_names = [
        '/' => 'Homepage',
        '/menu' => 'Menu',
        '/gallery' => 'Photo Gallery', 
        '/events' => 'Events & Happenings',
        '/careers' => 'Careers',
        '/contact' => 'Contact Us'
    ];
    
    foreach ($pages as $page) {
        $name = isset($page_names[$page['url']]) ? $page_names[$page['url']] : ucfirst(trim($page['url'], '/'));
        $html .= '
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <a href="' . $page['url'] . '" class="text-decoration-none fw-semibold">' . $name . '</a>
                                            <small class="text-muted d-block">Last updated: ' . $page['lastmod'] . '</small>
                                        </div>
                                        <span class="badge bg-primary rounded-pill">' . ucfirst($page['changefreq']) . '</span>
                                    </li>';
    }
    
    $html .= '
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title mb-0">Quick Links</h4>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="/" class="btn btn-primary">Homepage</a>
                                    <a href="/menu" class="btn btn-outline-primary">View Menu</a>
                                    <a href="/contact" class="btn btn-outline-primary">Contact Us</a>
                                    <a href="tel:+17342847000" class="btn btn-outline-success">Call Now</a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mt-4">
                            <div class="card-header">
                                <h4 class="card-title mb-0">Contact Info</h4>
                            </div>
                            <div class="card-body">
                                <p class="mb-2"><strong>Address:</strong><br>
                                22112 Sibley Road<br>
                                Brownstown Charter Township, MI 48183</p>
                                <p class="mb-2"><strong>Phone:</strong><br>
                                <a href="tel:+17342847000">(*************</a></p>
                                <p class="mb-0"><strong>Email:</strong><br>
                                <a href="mailto:<EMAIL>"><EMAIL></a></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-5">
                    <p class="text-muted">
                        <small>Sitemap generated on ' . date('Y-m-d H:i:s') . ' | 
                        <a href="sitemap.xml" class="text-decoration-none">XML Sitemap</a></small>
                    </p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>';
    
    return $html;
}

?>
