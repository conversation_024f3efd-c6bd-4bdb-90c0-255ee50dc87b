<?php
/**
 * Champions Sports Bar Website - Comprehensive Testing Script
 * 
 * This script tests all major functionality of the website including:
 * - Database connections
 * - Form submissions
 * - Admin panel functionality
 * - Page loading
 * - Image assets
 */

// Include configuration
require_once 'config.php';

// Test results storage
$testResults = [];
$totalTests = 0;
$passedTests = 0;

/**
 * Add test result
 */
function addTestResult($testName, $passed, $message = '') {
    global $testResults, $totalTests, $passedTests;
    
    $totalTests++;
    if ($passed) {
        $passedTests++;
    }
    
    $testResults[] = [
        'name' => $testName,
        'passed' => $passed,
        'message' => $message,
        'status' => $passed ? 'PASS' : 'FAIL'
    ];
}

/**
 * Test database connection
 */
function testDatabaseConnection() {
    try {
        $db = SiteDatabase::getInstance();
        $connection = $db->getConnection();
        
        if ($connection) {
            // Test a simple query
            $stmt = $connection->prepare("SELECT COUNT(*) as count FROM site_settings");
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            addTestResult('Database Connection', true, 'Connected successfully, found ' . $result['count'] . ' settings');
        } else {
            addTestResult('Database Connection', false, 'Failed to establish connection');
        }
    } catch (Exception $e) {
        addTestResult('Database Connection', false, 'Exception: ' . $e->getMessage());
    }
}

/**
 * Test all database tables exist
 */
function testDatabaseTables() {
    try {
        $db = SiteDatabase::getInstance();
        $connection = $db->getConnection();
        
        $requiredTables = [
            'site_settings',
            'content_pages',
            'hero_banners',
            'events',
            'jobs',
            'job_applications',
            'gallery_images',
            'menu_categories',
            'menu_items',
            'contact_messages'
        ];
        
        $existingTables = [];
        $stmt = $connection->prepare("SHOW TABLES");
        $stmt->execute();
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $missingTables = array_diff($requiredTables, $tables);
        
        if (empty($missingTables)) {
            addTestResult('Database Tables', true, 'All ' . count($requiredTables) . ' required tables exist');
        } else {
            addTestResult('Database Tables', false, 'Missing tables: ' . implode(', ', $missingTables));
        }
        
    } catch (Exception $e) {
        addTestResult('Database Tables', false, 'Exception: ' . $e->getMessage());
    }
}

/**
 * Test contact form processing
 */
function testContactForm() {
    try {
        // Simulate contact form data
        $testData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '************',
            'subject' => 'Test Message',
            'message' => 'This is a test message from the automated testing script.',
            'newsletter' => false
        ];
        
        // Test database insertion
        $db = SiteDatabase::getInstance();
        $connection = $db->getConnection();
        
        $stmt = $connection->prepare("
            INSERT INTO contact_messages (name, email, phone, subject, message, status, ip_address, user_agent)
            VALUES (:name, :email, :phone, :subject, :message, 'test', '127.0.0.1', 'Test Script')
        ");
        
        $result = $stmt->execute($testData);
        
        if ($result) {
            $messageId = $connection->lastInsertId();
            
            // Clean up test data
            $cleanupStmt = $connection->prepare("DELETE FROM contact_messages WHERE id = ?");
            $cleanupStmt->execute([$messageId]);
            
            addTestResult('Contact Form Processing', true, 'Form data saved and cleaned up successfully');
        } else {
            addTestResult('Contact Form Processing', false, 'Failed to save form data');
        }
        
    } catch (Exception $e) {
        addTestResult('Contact Form Processing', false, 'Exception: ' . $e->getMessage());
    }
}

/**
 * Test job application processing
 */
function testJobApplication() {
    try {
        // First, ensure we have a test job
        $db = SiteDatabase::getInstance();
        $connection = $db->getConnection();
        
        // Check if jobs exist
        $stmt = $connection->prepare("SELECT COUNT(*) as count FROM jobs WHERE status = 'active'");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result['count'] > 0) {
            // Get first active job
            $stmt = $connection->prepare("SELECT id FROM jobs WHERE status = 'active' LIMIT 1");
            $stmt->execute();
            $job = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Test application data
            $testData = [
                'job_id' => $job['id'],
                'name' => 'Test Applicant',
                'email' => '<EMAIL>',
                'phone' => '************',
                'cover_letter' => 'This is a test cover letter.',
                'status' => 'test'
            ];
            
            $stmt = $connection->prepare("
                INSERT INTO job_applications (job_id, name, email, phone, cover_letter, status)
                VALUES (:job_id, :name, :email, :phone, :cover_letter, :status)
            ");
            
            $result = $stmt->execute($testData);
            
            if ($result) {
                $applicationId = $connection->lastInsertId();
                
                // Clean up test data
                $cleanupStmt = $connection->prepare("DELETE FROM job_applications WHERE id = ?");
                $cleanupStmt->execute([$applicationId]);
                
                addTestResult('Job Application Processing', true, 'Application saved and cleaned up successfully');
            } else {
                addTestResult('Job Application Processing', false, 'Failed to save application data');
            }
        } else {
            addTestResult('Job Application Processing', false, 'No active jobs found to test against');
        }
        
    } catch (Exception $e) {
        addTestResult('Job Application Processing', false, 'Exception: ' . $e->getMessage());
    }
}

/**
 * Test image assets
 */
function testImageAssets() {
    $requiredImages = [
        'assets/images/logo.png',
        'assets/images/hero-bg.jpg',
        'assets/images/outdoor-patio.jpg',
        'assets/images/favicon.ico',
        'contact/assets/images/map-marker.png'
    ];
    
    $missingImages = [];
    $existingImages = [];
    
    foreach ($requiredImages as $image) {
        if (file_exists($image)) {
            $existingImages[] = $image;
        } else {
            $missingImages[] = $image;
        }
    }
    
    if (empty($missingImages)) {
        addTestResult('Core Image Assets', true, 'All ' . count($requiredImages) . ' core images exist');
    } else {
        addTestResult('Core Image Assets', false, 'Missing images: ' . implode(', ', $missingImages));
    }
    
    // Test gallery images
    $galleryPath = 'assets/images/gallery/';
    if (is_dir($galleryPath)) {
        $galleryImages = glob($galleryPath . '*.{jpg,jpeg,png,gif}', GLOB_BRACE);
        addTestResult('Gallery Images', true, 'Found ' . count($galleryImages) . ' gallery images');
    } else {
        addTestResult('Gallery Images', false, 'Gallery directory does not exist');
    }
}

/**
 * Test admin authentication
 */
function testAdminAuth() {
    try {
        // Test admin user exists
        $db = SiteDatabase::getInstance();
        $connection = $db->getConnection();
        
        $stmt = $connection->prepare("SELECT COUNT(*) as count FROM admin_users WHERE username = 'admin'");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result['count'] > 0) {
            addTestResult('Admin User Exists', true, 'Admin user found in database');
        } else {
            addTestResult('Admin User Exists', false, 'Admin user not found');
        }
        
    } catch (Exception $e) {
        addTestResult('Admin User Exists', false, 'Exception: ' . $e->getMessage());
    }
}

/**
 * Test configuration settings
 */
function testConfiguration() {
    try {
        // Test site configuration
        $siteName = SiteConfig::get('site_name', null);
        if ($siteName) {
            addTestResult('Site Configuration', true, 'Site name: ' . $siteName);
        } else {
            addTestResult('Site Configuration', false, 'Site name not configured');
        }
        
        // Test database configuration
        if (defined('DB_HOST') && defined('DB_NAME') && defined('DB_USER')) {
            addTestResult('Database Configuration', true, 'Database constants defined');
        } else {
            addTestResult('Database Configuration', false, 'Database constants missing');
        }
        
    } catch (Exception $e) {
        addTestResult('Configuration', false, 'Exception: ' . $e->getMessage());
    }
}

// Run all tests
echo "<h1>Champions Sports Bar Website - Functionality Test Results</h1>\n";
echo "<p>Testing all major website functionality...</p>\n";

testDatabaseConnection();
testDatabaseTables();
testContactForm();
testJobApplication();
testImageAssets();
testAdminAuth();
testConfiguration();

// Display results
echo "<h2>Test Results Summary</h2>\n";
echo "<p><strong>Total Tests:</strong> {$totalTests}</p>\n";
echo "<p><strong>Passed:</strong> {$passedTests}</p>\n";
echo "<p><strong>Failed:</strong> " . ($totalTests - $passedTests) . "</p>\n";
echo "<p><strong>Success Rate:</strong> " . round(($passedTests / $totalTests) * 100, 1) . "%</p>\n";

echo "<h2>Detailed Results</h2>\n";
echo "<table border='1' cellpadding='5' cellspacing='0'>\n";
echo "<tr><th>Test Name</th><th>Status</th><th>Message</th></tr>\n";

foreach ($testResults as $result) {
    $statusColor = $result['passed'] ? 'green' : 'red';
    echo "<tr>";
    echo "<td>{$result['name']}</td>";
    echo "<td style='color: {$statusColor}; font-weight: bold;'>{$result['status']}</td>";
    echo "<td>{$result['message']}</td>";
    echo "</tr>\n";
}

echo "</table>\n";

if ($passedTests === $totalTests) {
    echo "<h2 style='color: green;'>🎉 All Tests Passed!</h2>\n";
    echo "<p>The website is functioning correctly and ready for use.</p>\n";
} else {
    echo "<h2 style='color: red;'>⚠️ Some Tests Failed</h2>\n";
    echo "<p>Please review the failed tests and address any issues before deployment.</p>\n";
}

echo "<p><em>Test completed at: " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
