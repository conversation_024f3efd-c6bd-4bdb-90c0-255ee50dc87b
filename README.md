# Champions Sports Bar & Grill Website

A modern, responsive website for Champions Sports Bar & Grill located in Brownstown, Michigan. Built with PHP, Bootstrap 5, and modern web technologies.

## 🏆 Features

- **Responsive Design**: Fully responsive layout that works on all devices
- **Modern UI/UX**: Clean, professional design with smooth animations
- **SEO Optimized**: Proper meta tags, structured data, and SEO-friendly URLs
- **Photo Gallery**: Interactive gallery with Fancybox lightbox
- **Contact Forms**: Functional contact and job application forms
- **Performance Optimized**: Compressed assets, caching, and optimized images
- **Security**: Security headers, input validation, and protection against common attacks

## 🛠️ Technologies Used

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Framework**: Bootstrap 5.3.2
- **Backend**: PHP 7.4+
- **Icons**: Font Awesome 6.4.0
- **Animations**: Animate.css 4.1.1
- **Gallery**: Fancybox 5.0
- **Fonts**: Google Fonts (Roboto, Oswald)

## 📁 Project Structure

```
Champions-Sports-Bar-2025/
├── assets/
│   ├── css/
│   │   └── style.css          # Main stylesheet
│   ├── js/
│   │   └── main.js            # Main JavaScript file
│   └── images/
│       ├── gallery/           # Gallery images
│       ├── events/            # Event images
│       └── README.md          # Image guidelines
├── includes/
│   ├── header.php             # Site header
│   └── footer.php             # Site footer
├── oldsite/                   # Original website files (reference)
├── index.php                  # Homepage
├── menu.php                   # Menu page
├── gallery.php                # Photo gallery
├── events.php                 # Events & happenings
├── careers.php                # Careers/jobs page
├── contact.php                # Contact page
├── process-contact.php        # Contact form processor
├── 404.php                    # 404 error page
├── .htaccess                  # Apache configuration
├── robots.txt                 # Search engine directives
├── sitemap.xml                # XML sitemap
└── README.md                  # This file
```

## 🚀 Installation

1. **Clone or download** the project files to your web server
2. **Configure web server** to point to the project directory
3. **Update configuration**:
   - Edit contact email in `process-contact.php`
   - Update Google Maps API key in `contact.php`
   - Modify domain name in `.htaccess` and `sitemap.xml`
4. **Upload images** to the `assets/images/` directory
5. **Test the website** on different devices and browsers

## ⚙️ Configuration

### Contact Form
Edit `process-contact.php` to configure:
- Email recipient address
- SMTP settings (if using external mail service)
- Spam protection settings

### Google Maps
1. Get a Google Maps API key from [Google Cloud Console](https://console.cloud.google.com/)
2. Replace `YOUR_GOOGLE_MAPS_API_KEY` in `contact.php`
3. Enable Maps JavaScript API for your project

### SEO Settings
Update the following files with your domain:
- `sitemap.xml` - Replace domain URLs
- `.htaccess` - Configure redirects and security
- `robots.txt` - Update sitemap URL

## 📱 Pages Overview

### Homepage (`index.php`)
- Hero section with call-to-action
- Outdoor patio highlight
- Featured gallery preview
- Key features showcase

### Menu (`menu.php`)
- Organized menu categories
- Filterable menu items
- Responsive pricing display
- Call-to-action sections

### Gallery (`gallery.php`)
- Filterable photo gallery
- Fancybox lightbox integration
- Responsive grid layout
- Load more functionality

### Events (`events.php`)
- Featured events
- Weekly schedule
- Private event information
- Newsletter signup

### Careers (`careers.php`)
- Job listings
- Application modal
- Company benefits
- Application process

### Contact (`contact.php`)
- Contact information
- Interactive Google Map
- Contact form
- Business hours

## 🎨 Customization

### Colors
Main colors are defined in CSS variables in `assets/css/style.css`:
```css
:root {
    --primary-color: #dc3545;    /* Red */
    --secondary-color: #28a745;  /* Green */
    --dark-color: #212529;       /* Dark gray */
    --light-color: #f8f9fa;      /* Light gray */
}
```

### Fonts
Current fonts can be changed in the CSS:
- **Headings**: Oswald (Google Fonts)
- **Body text**: Roboto (Google Fonts)

### Images
Follow the guidelines in `assets/images/README.md` for:
- Image sizes and formats
- Optimization requirements
- Content guidelines

## 🔧 Maintenance

### Regular Updates
- Update menu items and prices
- Add new gallery images
- Update event information
- Review and respond to contact form submissions

### Performance Monitoring
- Monitor page load speeds
- Check mobile responsiveness
- Test contact forms regularly
- Update dependencies as needed

### SEO Maintenance
- Update meta descriptions
- Add new content regularly
- Monitor search rankings
- Update sitemap when adding pages

## 📞 Support

For technical support or questions about this website:
- **Email**: <EMAIL>
- **Phone**: (*************
- **Address**: 22112 Sibley Road, Brownstown Charter Township, MI 48183

## 📄 License

This website is proprietary to Champions Sports Bar & Grill. All rights reserved.

---

**Built with ❤️ for Champions Sports Bar & Grill**
