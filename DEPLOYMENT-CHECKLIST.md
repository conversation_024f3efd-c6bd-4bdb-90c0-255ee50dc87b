# Deployment Checklist - Champions Sports Bar & Grill Website

## 🚀 Pre-Deployment Checklist

### Content Preparation
- [ ] **Images**: Upload all required images to `assets/images/` directory
  - [ ] Hero background image (`hero-bg.jpg`)
  - [ ] Outdoor patio image (`outdoor-patio.jpg`)
  - [ ] Logo files (`logo.png`, `champions-logo.jpg`)
  - [ ] Gallery images (food, interior, patio, events, sports)
  - [ ] Event images
  - [ ] Favicon and touch icons
- [ ] **Menu Content**: Update menu items, prices, and descriptions
- [ ] **Contact Information**: Verify all contact details are correct
- [ ] **Business Hours**: Confirm operating hours are accurate
- [ ] **Event Information**: Update current and upcoming events

### Configuration Updates
- [ ] **Domain Configuration**:
  - [ ] Update domain in `sitemap.xml`
  - [ ] Update domain in `.htaccess` (if using redirects)
  - [ ] Update domain in `generate-sitemap.php`
  - [ ] Update canonical URLs in header.php
- [ ] **Email Configuration**:
  - [ ] Update recipient email in `process-contact.php`
  - [ ] Configure SMTP settings if using external mail service
  - [ ] Test contact form functionality
- [ ] **Google Services**:
  - [ ] Get Google Maps API key and update `contact.php`
  - [ ] Set up Google Analytics tracking ID
  - [ ] Configure Google Search Console
- [ ] **Social Media Links**:
  - [ ] Update Facebook, Instagram, Twitter URLs in footer
  - [ ] Verify social media accounts exist and are active

### Security Configuration
- [ ] **SSL Certificate**: Install and configure SSL certificate
- [ ] **HTTPS Redirect**: Enable HTTPS redirect in `.htaccess`
- [ ] **File Permissions**: Set proper file permissions (644 for files, 755 for directories)
- [ ] **Sensitive Files**: Ensure sensitive files are not publicly accessible
- [ ] **Error Reporting**: Disable PHP error display in production

### Performance Optimization
- [ ] **Image Optimization**: Compress all images for web
- [ ] **CSS/JS Minification**: Minify CSS and JavaScript files (optional)
- [ ] **Caching**: Verify browser caching headers are set
- [ ] **Compression**: Enable Gzip compression
- [ ] **CDN**: Consider using CDN for static assets (optional)

## 🌐 Deployment Steps

### 1. Server Setup
- [ ] **Web Server**: Apache or Nginx with PHP 7.4+ support
- [ ] **PHP Extensions**: Ensure required PHP extensions are installed
- [ ] **Database**: MySQL/MariaDB if needed for future features
- [ ] **Email Service**: Configure mail service for contact forms

### 2. File Upload
- [ ] **Upload Files**: Transfer all website files to server
- [ ] **Set Permissions**: Configure proper file and directory permissions
- [ ] **Test Upload**: Verify all files uploaded correctly

### 3. Domain Configuration
- [ ] **DNS Settings**: Point domain to server IP address
- [ ] **SSL Setup**: Install SSL certificate
- [ ] **Redirects**: Configure www/non-www redirects

### 4. Testing on Live Server
- [ ] **Basic Functionality**: Test all pages load correctly
- [ ] **Forms**: Test contact form and job application form
- [ ] **External Services**: Verify Google Maps and other integrations work
- [ ] **Mobile Testing**: Test on various mobile devices
- [ ] **Cross-Browser**: Test on different browsers

## 📊 Post-Deployment Setup

### Analytics & Monitoring
- [ ] **Google Analytics**: Set up GA4 tracking
- [ ] **Google Search Console**: Add and verify website
- [ ] **Google My Business**: Claim and optimize business listing
- [ ] **Uptime Monitoring**: Set up website uptime monitoring
- [ ] **Performance Monitoring**: Monitor page load speeds

### SEO Setup
- [ ] **Submit Sitemap**: Submit XML sitemap to search engines
- [ ] **Local Directories**: Submit to local business directories
- [ ] **Social Media**: Update website URL on all social media profiles
- [ ] **Business Listings**: Update website URL on existing business listings

### Backup & Security
- [ ] **Backup System**: Set up automated backups
- [ ] **Security Monitoring**: Implement security monitoring
- [ ] **Update Schedule**: Plan regular updates and maintenance

## 🔧 Configuration Files to Update

### `.htaccess`
```apache
# Update domain for redirects
RewriteRule ^(.*)$ https://yourdomain.com/$1 [L,R=301]

# Enable HTTPS redirect
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

### `process-contact.php`
```php
$config = [
    'to_email' => '<EMAIL>',
    'from_email' => '<EMAIL>',
    // ... other settings
];
```

### `contact.php`
```javascript
// Replace with your actual Google Maps API key
src="https://maps.googleapis.com/maps/api/js?key=YOUR_ACTUAL_API_KEY&callback=initMap"
```

### `includes/header.php`
```php
// Update Google Analytics tracking ID
gtag('config', 'YOUR_GA_TRACKING_ID');
```

## 🧪 Post-Deployment Testing

### Functionality Testing
- [ ] **Homepage**: All sections load and display correctly
- [ ] **Navigation**: All menu links work
- [ ] **Contact Form**: Form submits and emails are received
- [ ] **Phone Links**: Phone numbers are clickable on mobile
- [ ] **Gallery**: Fancybox lightbox works properly
- [ ] **Menu Filtering**: Category filters function correctly
- [ ] **Google Maps**: Map loads and marker displays
- [ ] **Social Links**: All social media links work

### Performance Testing
- [ ] **Page Speed**: Test with Google PageSpeed Insights
- [ ] **Mobile Speed**: Verify mobile performance
- [ ] **Core Web Vitals**: Check LCP, FID, and CLS scores
- [ ] **Image Loading**: Verify all images load properly

### SEO Testing
- [ ] **Meta Tags**: Verify meta tags display correctly in search results
- [ ] **Structured Data**: Test with Google's Rich Results Test
- [ ] **Sitemap**: Verify XML sitemap is accessible
- [ ] **Robots.txt**: Check robots.txt is working

## 📱 Mobile & Cross-Browser Testing

### Mobile Devices
- [ ] iPhone (Safari)
- [ ] Android (Chrome)
- [ ] iPad (Safari)
- [ ] Android Tablet (Chrome)

### Desktop Browsers
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)

## 🚨 Emergency Procedures

### Rollback Plan
- [ ] **Backup**: Keep backup of old website
- [ ] **DNS**: Know how to quickly revert DNS changes
- [ ] **Contact Info**: Have hosting provider contact information ready

### Common Issues
- [ ] **Email Not Working**: Check SMTP settings and server configuration
- [ ] **Images Not Loading**: Verify file paths and permissions
- [ ] **Forms Not Submitting**: Check PHP configuration and error logs
- [ ] **SSL Issues**: Verify certificate installation and redirects

## 📞 Support Contacts

### Technical Support
- **Hosting Provider**: [Contact information]
- **Domain Registrar**: [Contact information]
- **SSL Provider**: [Contact information]
- **Developer**: [Contact information]

### Business Contacts
- **Champions Sports Bar**: (*************
- **Business Email**: <EMAIL>

## 📅 Maintenance Schedule

### Daily
- [ ] Monitor website uptime
- [ ] Check for any error reports

### Weekly
- [ ] Review contact form submissions
- [ ] Update event information if needed
- [ ] Check Google My Business for reviews/questions

### Monthly
- [ ] Review website analytics
- [ ] Check for broken links
- [ ] Update content as needed
- [ ] Review security logs

### Quarterly
- [ ] Comprehensive website audit
- [ ] Performance optimization review
- [ ] SEO performance analysis
- [ ] Backup system verification

---

**Note**: This checklist should be customized based on your specific hosting environment and requirements.
