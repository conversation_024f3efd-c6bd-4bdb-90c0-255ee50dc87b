# MySQL Database Setup for Champions Sports Bar Admin Panel

## 🗄️ **Quick Setup Guide**

### **Option 1: Automatic Setup (Recommended)**

1. **Start MySQL Service:**
   ```bash
   sudo systemctl start mysql
   sudo systemctl enable mysql  # Enable auto-start on boot
   ```

2. **Run the Setup Script:**
   Open your browser and go to:
   ```
   http://localhost:8000/admin/setup-database.php
   ```
   
   This will automatically:
   - Create the `champions_admin` database
   - Create all necessary tables
   - Insert default data
   - Create the admin user

3. **Login to Admin Panel:**
   ```
   URL: http://localhost:8000/admin/login.php
   Username: admin
   Password: admin123
   ```

### **Option 2: Manual Setup**

If the automatic setup doesn't work, follow these steps:

1. **Start MySQL:**
   ```bash
   sudo systemctl start mysql
   ```

2. **Login to MySQL:**
   ```bash
   mysql -u root -p
   ```
   (Enter your MySQL root password, or just press Enter if no password is set)

3. **Create Database:**
   ```sql
   CREATE DATABASE champions_admin CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   USE champions_admin;
   ```

4. **Import Database Schema:**
   ```sql
   SOURCE /home/<USER>/Documents/augment-projects/Champions-Sports-Bar-2025/admin/database.sql;
   ```

5. **Exit MySQL:**
   ```sql
   EXIT;
   ```

6. **Test Admin Panel:**
   Go to: `http://localhost:8000/admin/login.php`

## 🔧 **Configuration**

### **Database Settings**
The database configuration is in `admin/config/database.php`:

```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'champions_admin');
define('DB_USER', 'root');
define('DB_PASS', '');  // Change if you have a MySQL password
```

### **Default Admin User**
- **Username:** `admin`
- **Email:** `<EMAIL>`
- **Password:** `admin123` ⚠️ **CHANGE THIS IMMEDIATELY!**

## 🚨 **Troubleshooting**

### **MySQL Service Issues**

**Problem:** "Can't connect to local MySQL server"
```bash
# Check if MySQL is running
sudo systemctl status mysql

# Start MySQL if not running
sudo systemctl start mysql

# Check MySQL process
ps aux | grep mysql
```

**Problem:** "Access denied for user 'root'"
```bash
# Reset MySQL root password if needed
sudo mysql
ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'newpassword';
FLUSH PRIVILEGES;
EXIT;
```

### **Permission Issues**

**Problem:** "Permission denied"
```bash
# Make sure you have proper permissions
sudo chown -R $USER:$USER /home/<USER>/Documents/augment-projects/Champions-Sports-Bar-2025/
```

### **Database Connection Issues**

**Problem:** "Database connection failed"

1. **Check MySQL is running:**
   ```bash
   sudo systemctl status mysql
   ```

2. **Test connection manually:**
   ```bash
   mysql -u root -p -h localhost
   ```

3. **Check database exists:**
   ```sql
   SHOW DATABASES;
   ```

4. **Verify user permissions:**
   ```sql
   SELECT User, Host FROM mysql.user WHERE User = 'root';
   ```

## 📊 **Database Structure**

The admin panel creates these tables:

### **Core Tables:**
- `admin_users` - Admin user accounts and permissions
- `site_settings` - Website configuration settings
- `content_sections` - Homepage content management
- `hero_banners` - Hero banner/slider management

### **Content Tables:**
- `menu_categories` - Menu category organization
- `menu_items` - Individual menu items
- `events` - Events and happenings
- `gallery_images` - Photo gallery management

### **Business Tables:**
- `job_postings` - Career job listings
- `job_applications` - Job application submissions
- `contact_messages` - Contact form messages

### **System Tables:**
- `admin_activity_log` - Admin action logging

## 🔐 **Security Notes**

1. **Change Default Password:** Immediately change the admin password after first login
2. **Database User:** Consider creating a dedicated MySQL user for the application
3. **File Permissions:** Ensure proper file permissions on the admin directory
4. **Backup:** Regular database backups are recommended

## 🚀 **Production Setup**

For production deployment:

1. **Create dedicated MySQL user:**
   ```sql
   CREATE USER 'champions_admin'@'localhost' IDENTIFIED BY 'secure_password';
   GRANT ALL PRIVILEGES ON champions_admin.* TO 'champions_admin'@'localhost';
   FLUSH PRIVILEGES;
   ```

2. **Update configuration:**
   ```php
   define('DB_USER', 'champions_admin');
   define('DB_PASS', 'secure_password');
   ```

3. **Secure the setup script:**
   Delete or restrict access to `setup-database.php` after setup

## 📞 **Need Help?**

If you encounter issues:

1. **Check the error logs:**
   ```bash
   tail -f /var/log/mysql/error.log
   ```

2. **Verify PHP MySQL extension:**
   ```bash
   php -m | grep -i mysql
   ```

3. **Test basic connection:**
   ```bash
   php -r "try { new PDO('mysql:host=localhost', 'root', ''); echo 'Connection OK'; } catch(Exception \$e) { echo 'Error: ' . \$e->getMessage(); }"
   ```

## ✅ **Verification Checklist**

After setup, verify these work:

- [ ] MySQL service is running
- [ ] Database `champions_admin` exists
- [ ] All tables are created (12 tables total)
- [ ] Admin user exists and can login
- [ ] Admin dashboard loads without errors
- [ ] Content management pages are accessible

---

**Once MySQL is set up, you'll have a fully functional admin panel to manage all aspects of the Champions Sports Bar website!**
