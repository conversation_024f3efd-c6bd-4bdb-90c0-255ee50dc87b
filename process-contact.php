<?php
// Champions Sports Bar - Contact Form Processor
session_start();

// Include site configuration
require_once 'config.php';

// Enable error reporting for development (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Configuration
$config = [
    'to_email' => SiteConfig::get('contact_email', '<EMAIL>'),
    'from_email' => MAIL_FROM,
    'from_name' => MAIL_FROM_NAME,
    'subject_prefix' => SiteConfig::get('site_name', 'Champions Sports Bar & Grill') . ' - Contact Form: ',
    'redirect_success' => 'contact/?success=1',
    'redirect_error' => 'contact/?error=1'
];

// Function to sanitize input
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// Function to validate email
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// Function to validate phone number
function validate_phone($phone) {
    $phone = preg_replace('/[^0-9]/', '', $phone);
    return strlen($phone) >= 10;
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: contact.php');
    exit;
}

// Initialize variables
$errors = [];
$success = false;

// Get and sanitize form data
$name = isset($_POST['name']) ? sanitize_input($_POST['name']) : '';
$email = isset($_POST['email']) ? sanitize_input($_POST['email']) : '';
$phone = isset($_POST['phone']) ? sanitize_input($_POST['phone']) : '';
$subject = isset($_POST['subject']) ? sanitize_input($_POST['subject']) : '';
$message = isset($_POST['message']) ? sanitize_input($_POST['message']) : '';
$newsletter = isset($_POST['newsletter']) ? true : false;

// Validate required fields
if (empty($name)) {
    $errors[] = 'Name is required.';
}

if (empty($email)) {
    $errors[] = 'Email is required.';
} elseif (!validate_email($email)) {
    $errors[] = 'Please enter a valid email address.';
}

if (empty($message)) {
    $errors[] = 'Message is required.';
}

// Validate phone if provided
if (!empty($phone) && !validate_phone($phone)) {
    $errors[] = 'Please enter a valid phone number.';
}

// Basic spam protection
if (isset($_POST['honeypot']) && !empty($_POST['honeypot'])) {
    // Honeypot field was filled, likely spam
    header('Location: contact.php?error=spam');
    exit;
}

// Rate limiting (simple session-based)
if (isset($_SESSION['last_contact_time'])) {
    $time_diff = time() - $_SESSION['last_contact_time'];
    if ($time_diff < 60) { // 1 minute between submissions
        $errors[] = 'Please wait before sending another message.';
    }
}

// If no errors, process the form
if (empty($errors)) {
    try {
        // Prepare form data for email notification
        $formData = [
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'subject' => $subject,
            'message' => $message,
            'newsletter' => $newsletter
        ];
        
        // Save message to database
        $db = SiteDatabase::getInstance();
        $messageId = null;

        if ($db->getConnection()) {
            try {
                $messageData = [
                    'name' => $name,
                    'email' => $email,
                    'phone' => $phone,
                    'subject' => $subject,
                    'message' => $message,
                    'status' => 'new',
                    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
                ];

                $stmt = $db->getConnection()->prepare("
                    INSERT INTO contact_messages (name, email, phone, subject, message, status, ip_address, user_agent)
                    VALUES (:name, :email, :phone, :subject, :message, :status, :ip_address, :user_agent)
                ");

                $stmt->execute($messageData);
                $messageId = $db->getConnection()->lastInsertId();

            } catch (Exception $e) {
                error_log("Failed to save contact message to database: " . $e->getMessage());
            }
        }

        // Send email notification
        $mail_sent = sendContactNotification($formData);

        if ($mail_sent || $messageId) {
            // Set session variable to prevent rapid submissions
            $_SESSION['last_contact_time'] = time();

            // Log successful submission
            error_log("Contact form submitted successfully by: " . $email . ($messageId ? " (Message ID: $messageId)" : ""));

            // If newsletter signup requested, you could add to mailing list here
            if ($newsletter) {
                // Add newsletter signup logic here
                // This could integrate with your email marketing service
            }

            $success = true;

            // Redirect to success page
            header('Location: ' . $config['redirect_success']);
            exit;

        } else {
            $errors[] = 'Sorry, there was an error sending your message. Please try again later.';
            error_log("Failed to send contact form email for: " . $email);
        }
        
    } catch (Exception $e) {
        $errors[] = 'An unexpected error occurred. Please try again later.';
        error_log("Contact form error: " . $e->getMessage());
    }
}

// If we get here, there were errors
$_SESSION['contact_errors'] = $errors;
$_SESSION['contact_form_data'] = $_POST; // Preserve form data

// Redirect back to contact page with error
header('Location: ' . $config['redirect_error']);
exit;

?>
